{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-08-25T12:07:40.483Z", "updatedAt": "2025-08-25T12:07:40.487Z", "resourceCount": 6}, "resources": [{"id": "ai-code-guide", "source": "project", "protocol": "role", "name": "Ai Code Guide 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/ai-code-guide/ai-code-guide.role.md", "metadata": {"createdAt": "2025-08-25T12:07:40.484Z", "updatedAt": "2025-08-25T12:07:40.484Z", "scannedAt": "2025-08-25T12:07:40.484Z", "path": "role/ai-code-guide/ai-code-guide.role.md"}}, {"id": "simple-coding-standards", "source": "project", "protocol": "execution", "name": "Simple Coding Standards 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/ai-code-guide/execution/simple-coding-standards.execution.md", "metadata": {"createdAt": "2025-08-25T12:07:40.485Z", "updatedAt": "2025-08-25T12:07:40.485Z", "scannedAt": "2025-08-25T12:07:40.484Z", "path": "role/ai-code-guide/execution/simple-coding-standards.execution.md"}}, {"id": "ai-coding-mindset", "source": "project", "protocol": "thought", "name": "Ai Coding Mindset 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/ai-code-guide/thought/ai-coding-mindset.thought.md", "metadata": {"createdAt": "2025-08-25T12:07:40.485Z", "updatedAt": "2025-08-25T12:07:40.485Z", "scannedAt": "2025-08-25T12:07:40.485Z", "path": "role/ai-code-guide/thought/ai-coding-mindset.thought.md"}}, {"id": "pythonic-development", "source": "project", "protocol": "execution", "name": "Pythonic Development 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/pythonic-dev-guide/execution/pythonic-development.execution.md", "metadata": {"createdAt": "2025-08-25T12:07:40.486Z", "updatedAt": "2025-08-25T12:07:40.486Z", "scannedAt": "2025-08-25T12:07:40.486Z", "path": "role/pythonic-dev-guide/execution/pythonic-development.execution.md"}}, {"id": "pythonic-dev-guide", "source": "project", "protocol": "role", "name": "Pythonic Dev Guide 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/pythonic-dev-guide/pythonic-dev-guide.role.md", "metadata": {"createdAt": "2025-08-25T12:07:40.486Z", "updatedAt": "2025-08-25T12:07:40.486Z", "scannedAt": "2025-08-25T12:07:40.486Z", "path": "role/pythonic-dev-guide/pythonic-dev-guide.role.md"}}, {"id": "code-quality-thinking", "source": "project", "protocol": "thought", "name": "Code Quality Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/pythonic-dev-guide/thought/code-quality-thinking.thought.md", "metadata": {"createdAt": "2025-08-25T12:07:40.487Z", "updatedAt": "2025-08-25T12:07:40.487Z", "scannedAt": "2025-08-25T12:07:40.487Z", "path": "role/pythonic-dev-guide/thought/code-quality-thinking.thought.md"}}], "stats": {"totalResources": 6, "byProtocol": {"role": 2, "execution": 2, "thought": 2}, "bySource": {"project": 6}}}