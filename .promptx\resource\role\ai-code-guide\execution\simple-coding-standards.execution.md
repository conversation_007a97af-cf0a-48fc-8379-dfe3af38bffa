<execution>
  <constraint>
    ## 硬性约束（严格执行）
    - **文件长度限制**：单个Python文件不超过200行
    - **函数长度限制**：单个函数不超过20行
    - **类方法数量**：单个类不超过10个方法
    - **嵌套深度限制**：代码嵌套不超过3层
    - **导入语句规范**：所有import放在文件顶部，按标准库、第三方库、本地模块分组
    - **4分组结构强制**：每个类必须有明确的4分组注释
  </constraint>

  <rule>
    ## 强制执行规则
    - **命名规范**：类名用PascalCase，函数名用snake_case，常量用UPPER_CASE
    - **文档字符串**：所有公共函数必须有简洁的docstring
    - **类型提示**：函数参数和返回值必须有基础类型提示
    - **错误处理**：关键操作必须有try-except，但不要过度捕获
    - **日志记录**：重要操作开始和结束时记录日志
    - **文件拆分**：超过200行立即拆分成多个文件
  </rule>

  <guideline>
    ## 指导原则
    - **简单优于复杂**：能用简单方法解决的绝不用复杂方法
    - **可读性优于性能**：除非性能真的有问题，否则优先考虑可读性
    - **实用优于完美**：能用的代码比完美但复杂的代码更有价值
    - **渐进式改进**：从能用开始，逐步完善，不要一开始就追求完美
    - **维护友好**：写代码时考虑一个月后的自己能否轻松理解
  </guideline>

  <process>
    ## AI代码规范开发流程
    
    ### Phase 1: 需求确认 (30秒)
    ```
    功能需求 → 文件规划 → 类结构设计 → 开始编码
    ```
    
    **规划检查**：
    - [ ] 功能是否可以在200行内实现
    - [ ] 是否需要拆分成多个文件
    - [ ] 类的职责是否单一明确
    
    ### Phase 2: 编码实现 (主要时间)
    ```
    4分组结构 → 核心功能 → 错误处理 → 日志添加
    ```
    
    **简化版4分组结构**：
    ```python
    class SimpleManager:
        """简单管理器"""
        
        # === 1. 常量定义 ===
        DEFAULT_TIMEOUT = 30
        
        # === 2. 初始化 ===
        def __init__(self, config: dict):
            self.config = config
            self._setup_logging()
        
        # === 3. 主要功能 ===
        def main_function(self, data: str) -> dict:
            """主要功能"""
            logger.info(f"开始处理: {data}")
            result = self._process_data(data)
            logger.info(f"处理完成: {result}")
            return result
        
        # === 4. 辅助方法 ===
        def _process_data(self, data: str) -> dict:
            """处理数据"""
            return {"result": data}
        
        def _setup_logging(self) -> None:
            """设置日志"""
            logging.basicConfig(level=logging.INFO)
    ```
    
    ### Phase 3: 代码检查 (30秒)
    ```
    长度检查 → 结构检查 → 命名检查 → 功能测试
    ```
    
    **快速检查清单**：
    - [ ] 文件是否超过200行
    - [ ] 函数是否超过20行
    - [ ] 是否有4分组结构
    - [ ] 变量命名是否清晰
    - [ ] 是否有基础的错误处理
    - [ ] 是否有必要的日志
  </process>

  <criteria>
    ## 代码质量标准
    
    ### 结构质量
    - ✅ 4分组结构清晰
    - ✅ 文件长度≤200行
    - ✅ 函数长度≤20行
    - ✅ 嵌套深度≤3层
    
    ### 可读性
    - ✅ 变量命名有意义
    - ✅ 函数职责单一
    - ✅ 逻辑流程清晰
    - ✅ 注释适度恰当
    
    ### 实用性
    - ✅ 功能实现正确
    - ✅ 错误处理合理
    - ✅ 日志信息有用
    - ✅ 易于维护修改
    
    ### AI友好性
    - ✅ 结构强制明确
    - ✅ 规范简单易懂
    - ✅ 避免过度设计
    - ✅ 便于增量开发
  </criteria>
</execution>
