<thought>
  <exploration>
    ## AI代码开发的特殊考量
    
    ### AI写代码的常见问题
    - **结构混乱**：AI容易写出功能正确但结构糟糕的代码
    - **过度复杂**：AI喜欢展示"最佳实践"，导致简单问题复杂化
    - **文件冗长**：AI倾向于把所有代码放在一个文件里
    - **命名随意**：AI生成的变量名和函数名缺乏一致性
    - **缺乏分层**：AI写的代码往往是平铺直叙，缺乏合理的抽象层次
    
    ### 实用主义的代码哲学
    - **功能优先**：先实现功能，再考虑优化
    - **简单直接**：能用简单方法解决的，绝不用复杂方法
    - **渐进改进**：从能用开始，逐步完善
    - **维护友好**：代码是写给未来的自己看的
  </exploration>
  
  <reasoning>
    ## 针对AI代码的规范策略
    
    ### 结构化思维
    - **强制分组**：用明确的注释分组强制AI组织代码结构
    - **文件拆分原则**：单文件不超过200行，超过就拆分
    - **功能单一**：每个函数只做一件事，每个类只负责一个职责
    - **命名统一**：建立命名规范，让AI遵循一致的命名风格
    
    ### 可读性优先
    - **注释适度**：关键地方有注释，但不要过度注释
    - **逻辑清晰**：避免深层嵌套，优先使用早期返回
    - **变量有意义**：变量名要能表达意图，避免缩写
    - **函数简短**：单个函数不超过20行
  </reasoning>
  
  <challenge>
    ## AI代码开发的挑战与应对
    
    ### 过度设计的挑战
    - **设计模式滥用**：AI喜欢使用各种设计模式，但往往不必要
    - **抽象层次过多**：AI容易创建过多的抽象层，增加复杂度
    - **配置过度**：AI喜欢把所有东西都做成可配置的
    
    ### 应对策略
    - **YAGNI原则**：You Aren't Gonna Need It - 不要预先设计用不到的功能
    - **三次规则**：同样的代码出现三次才考虑抽象
    - **简单测试**：能手动测试就不要写复杂的单元测试
  </challenge>
  
  <plan>
    ## AI代码规范执行计划
    
    ### 代码审查检查点
    - [ ] 文件长度是否超过200行
    - [ ] 函数长度是否超过20行
    - [ ] 是否有明确的4分组结构
    - [ ] 变量命名是否有意义
    - [ ] 是否有不必要的复杂设计
    
    ### 重构指导原则
    - **先能用再优化**：确保功能正确后再考虑代码质量
    - **小步重构**：每次只改一个小地方
    - **保持简单**：重构的目标是让代码更简单，不是更复杂
    - **测试驱动**：重构前后功能要保持一致
  </plan>
</thought>
