<execution>
  <constraint>
    ## 硬性技术约束
    - **函数长度限制**：单个函数不超过30行代码
    - **类型提示要求**：所有公共方法必须有类型提示
    - **文档字符串要求**：所有公共方法必须有docstring
    - **异常处理约束**：内部方法抛出异常，外部方法捕获记录
    - **SOLID原则强制**：每个类必须遵循单一职责原则
    - **4分组结构强制**：类常量→初始化→业务方法→辅助方法
  </constraint>

  <rule>
    ## 强制执行规则
    - **Protocol接口规则**：多项目复用的组件必须定义Protocol接口
    - **命名规范规则**：使用有意义的英文名称，布尔值用is_/has_前缀
    - **异常链规则**：使用raise from保持异常链完整性
    - **日志规则**：只在关键业务节点记录日志，包含足够上下文
    - **依赖注入规则**：通过构造函数注入依赖，依赖抽象而非具体实现
    - **辅助方法规则**：内部辅助方法使用_前缀，体现封装性
  </rule>

  <guideline>
    ## 开发指导原则
    - **可读性优先**：代码是写给人看的，清晰胜过聪明
    - **渐进式重构**：小步快跑，持续改进代码结构
    - **测试友好设计**：方法设计要便于单元测试
    - **错误信息友好**：异常信息要包含足够的调试信息
    - **性能适度考虑**：在保证可读性前提下考虑性能
    - **文档适度原则**：解释为什么而不是做什么
  </guideline>

  <process>
    ## Python规范开发流程
    
    ### Phase 1: 设计阶段 (30秒)
    ```
    需求分析 → 职责划分 → 接口设计 → 异常策略
    ```
    
    **设计检查清单**：
    - [ ] 确认类的单一职责
    - [ ] 设计Protocol接口（如需多项目复用）
    - [ ] 规划4分组结构
    - [ ] 确定异常处理策略
    
    ### Phase 2: 编码阶段 (主要时间)
    ```
    类结构搭建 → 方法实现 → 异常处理 → 日志添加
    ```
    
    **4分组类结构模板**：
    ```python
    class ComponentManager:
        """组件管理器 - 标准4分组结构"""
        
        # === 1. 类常量 ===
        DEFAULT_TIMEOUT = 30
        MAX_RETRIES = 3
        
        # === 2. 初始化 ===
        def __init__(self, processor: DataProcessor):
            """初始化管理器（依赖注入）"""
            self._processor = processor
            self._cache = {}
        
        # === 3. 主要业务方法 ===
        @handle_business_error
        def process_data(self, data: Dict[str, Any]) -> ProcessResult:
            """处理数据"""
            logger.info(f"开始处理数据: {data.get('id')}")
            validated_data = self._validate_input(data)
            result = self._execute_processing(validated_data)
            logger.info(f"数据处理完成: {result.id}")
            return result
        
        # === 4. 辅助方法（内部使用）===
        def _validate_input(self, data: Dict[str, Any]) -> Dict[str, Any]:
            """验证输入数据"""
            if not data.get('id'):
                raise ValueError("缺少必要的ID字段")
            return data
        
        def _execute_processing(self, data: Dict[str, Any]) -> ProcessResult:
            """执行处理逻辑"""
            return self._processor.process(data)
    ```
    
    ### Phase 3: 验证阶段 (30秒)
    ```
    SOLID检查 → 函数长度检查 → 异常处理检查 → 日志合理性检查
    ```
    
    **验证检查清单**：
    - [ ] 每个函数不超过30行
    - [ ] 每个函数只做一件事
    - [ ] 类型提示和文档字符串完整
    - [ ] 异常处理策略正确
    - [ ] 日志记录合理
    - [ ] SOLID原则遵循
  </process>

  <criteria>
    ## 代码质量评价标准
    
    ### 结构质量
    - ✅ 4分组结构清晰
    - ✅ 单一职责原则遵循
    - ✅ 依赖注入正确实现
    - ✅ Protocol接口合理使用
    
    ### 代码质量
    - ✅ 函数长度≤30行
    - ✅ 类型提示完整
    - ✅ 文档字符串适度
    - ✅ 变量命名有意义
    
    ### 异常处理
    - ✅ 内部抛出外部捕获
    - ✅ 异常信息详细
    - ✅ 异常链保持完整
    - ✅ 分层处理合理
    
    ### 日志记录
    - ✅ 关键节点覆盖
    - ✅ 上下文信息充足
    - ✅ 日志级别合理
    - ✅ 格式统一规范
  </criteria>
</execution>
