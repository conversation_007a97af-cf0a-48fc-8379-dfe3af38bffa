# 多任务终端执行器

一个简单易用的Python多任务管理和执行框架，支持彩色日志输出和灵活的配置管理。

## 特性

- 🚀 **简单启动**: 一键选择和执行任务
- 📊 **智能日志**: 彩色输出，任务标识显示，每个任务独立日志文件
- 🖥️ **任务面板**: 固定显示任务信息、定时设置、执行时间
- ⚙️ **智能配置**: 三层配置系统，自动优先级合并，简化访问语法
- 🔧 **自动依赖**: 自动检查和安装Python包依赖
- 📁 **任务隔离**: 每个任务完全独立运行，支持多终端并发
- 🎯 **智能导入**: 导入即用，无需手动初始化

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 运行程序

```bash
python main.py
# 或者
python run.py
```

### 3. 选择任务

程序会自动扫描 `tasks/` 目录下的任务文件，显示可用任务列表，选择对应数字即可执行。

### 4. 多任务运行

要同时运行多个任务，只需打开多个终端，每个终端运行一个任务：

```bash
# 终端1
python main.py  # 选择任务1

# 终端2
python main.py  # 选择任务2

# 终端3
python main.py  # 选择任务3
```

每个终端都会显示独立的任务信息面板和带标识的日志输出。

## 项目结构

```
多任务终端执行器_shike/
├── main.py                 # 主启动文件
├── run.py                  # 快速启动脚本
├── config/                 # 配置文件目录
│   ├── system.yaml         # 系统配置
│   ├── global.yaml         # 全局配置
│   └── tasks/              # 任务专属配置
│       ├── demo_task.yaml
│       └── file_processor.yaml
├── tasks/                  # 任务脚本目录
│   ├── demo_task.py        # 演示任务
│   └── file_processor.py   # 文件处理任务
├── models/                 # 工具类目录
│   └── file_utils.py       # 文件工具类
├── core/                   # 核心模块
│   ├── config_manager.py   # 配置管理器
│   ├── task_manager.py     # 任务管理器
│   └── logger.py           # 日志系统
├── logs/                   # 日志目录
└── requirements.txt        # 依赖包
```

## 创建新任务

### 1. 创建任务文件

在 `tasks/` 目录下创建 `.py` 文件：

```python
# tasks/my_task.py
import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))

# 智能导入 - 自动初始化！
from core import G, logger

def main():
    """任务主函数"""
    logger.step("开始执行")

    # 读取配置 (智能访问，无需G.task前缀)
    message = G.settings.message
    logger.info(f"配置消息: {message}")

    # 任务逻辑
    logger.success("任务完成")

if __name__ == "__main__":
    # 现在只需要简单调用即可！
    main()
```

### 2. 创建配置文件

在 `config/tasks/` 目录下创建同名 `.yaml` 文件：

```yaml
# config/tasks/my_task.yaml
task_info:
  name: "我的任务"
  description: "任务描述"
  dependencies:
    - requests  # 需要的Python包

settings:
  message: "Hello World"
  count: 10
```

## 配置系统

### 智能三层配置结构

1. **系统配置** (`config/system.yaml`): 系统级设置
2. **全局配置** (`config/global.yaml`): 所有任务共享
3. **任务配置** (`config/tasks/任务名.yaml`): 任务专属

### 智能配置访问方式

**🚀 新特性**: 智能配置优先级和简化访问语法！

```python
# 智能访问 - 自动按优先级查找 (任务配置 > 全局配置 > 系统配置)
message = G.settings.message        # 无需G.task前缀！
api_timeout = G.api.timeout         # 任务配置会自动覆盖全局配置
db_host = G.database.host           # 全局配置

# 明确访问特定层级（如需要）
log_level = G.system.log_level      # 明确访问系统配置
task_info = G.task.task_info        # 明确访问任务配置

# 配置来源追踪
source = G.get_config_source('api') # "任务配置 (tasks/demo_task.yaml)"
```

**配置优先级**: 任务配置 > 全局配置 > 系统配置
**智能合并**: 字典类型配置会自动深度合并

## 日志系统

### 智能日志级别

- `logger.debug()` - 🔍 调试信息（灰色，仅文件）
- `logger.info()` - ℹ️ 一般信息（默认颜色）
- `logger.warning()` - ⚠️ 警告信息（黄色）
- `logger.error()` - ❌ 错误信息（红色）
- `logger.success()` - ✅ 成功信息（亮绿色）
- `logger.step()` - 🔄 步骤信息（亮青色）

### 丰富的上下文信息

支持多种常用上下文参数：

```python
# 文件处理
logger.info("文件处理完成", file="data.xlsx", size="2.5MB", count=1000)

# API调用
logger.success("API请求成功", url="https://api.com", code=200, duration="1.2s")

# 进度追踪
logger.step("数据处理", "正在清理数据", progress="50/100", status="processing")

# 错误处理
logger.error("操作失败", code="ERR001", user="admin", date="2024-01-15")
```

### 日志文件

每次任务执行都会在 `logs/任务名/` 目录下生成独立的日志文件：
- 格式: `任务名_时间戳.log`
- 例如: `demo_task_20240125_143022.log`
- 包含所有级别日志（包括DEBUG）

📖 **详细使用指南**:
- [日志系统使用指南](docs/日志系统使用指南.md)
- [任务信息显示指南](docs/任务信息显示指南.md)

## 示例任务

项目包含两个示例任务：

1. **演示任务** (`demo_task.py`): 展示配置读取、API调用等基本功能
2. **文件处理任务** (`file_processor.py`): 文件统计和处理示例

运行程序后选择对应任务即可体验。

## 扩展开发

### 添加工具类

在 `models/` 目录下创建工具类，任务中可以直接导入使用：

```python
from models.file_utils import FileUtils

# 在任务中使用
FileUtils.ensure_dir("./output")
```

### 自定义日志装饰器

```python
from core.logger import log_function

@log_function("处理数据")
def process_data():
    # 函数执行会自动记录开始和结束日志
    pass
```

## 注意事项

1. 任务文件必须包含 `main()` 函数作为入口点
2. 配置文件使用YAML格式，注意缩进
3. 依赖包会自动安装到当前Python环境
4. 每个任务运行在独立的日志空间中
5. 支持直接运行单个任务文件进行调试
