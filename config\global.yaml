# 企业微信通知配置
notification:
  enabled: true
  webhook_url: "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=a6e8f4c3-1ba7-4370-b2c2-8977d96e2e70"
  notify_on_success: false
  notify_on_failure: true

# 本地附件参数
local:
  main_folder: C:\Users\<USER>\Documents\Projects\时刻商场自动化维护运营
  image_package_folder: 商品图片包
  urls_txt_file_name: 待采集链接.txt
  products_excel_file_name: 时刻商品管理表.xlsx

# 图鉴验证码识别配置
tujian:
  username: QingYun
  password: Tj153154

# 伊性坊配置
yixingfang:
  username: shike
  password: D3Luc8m9zxxm
  image_package_password: 123456

# 时刻crmeb
skcrmeb:
  appid: shikebot
  appsecret: KGJ3ShfnZmzmBRPHGxPRRspD87WzhnG5
  main_url: https://shop.shikejk.com

# 数据库配置
database:
  host: ************
  port: 3306
  username: shike_data
  password: YtjpRwJTiGskPBjy
  database: shike_data

# 七牛参数
qiniu:
  access_key: dKb9WQvVTDpjFfXMGbL-XsAP1UTzwAt5S9ucgTZA
  secret_key: RnF7Wb29PVH6dwUEVHwli1hZo68g9zKIr8821fIk
  bucket_name: shikejk
  sava_img_path: products
  version: v2
  domain: qn.shikejk.com

# VIP利润率计算配置
vip_profit_config:
  # 系统常量配置
  constants:
    default_sheet_name: "商品数据"      # 默认Excel工作表名称
    default_discount_rate: 0.9          # 默认折扣率（9折）
    default_shipping_cost: 6.0          # 默认快递费用
    default_weight: 500.0               # 默认商品重量（克）

  # 黑卡VIP折扣（在9折基础上再打折）
  vip_discount: 0.88

  # 快递费用配置
  shipping:
    # 基础重量（1000g以下使用基础价格）
    base_weight: 1000

    # 普通快递
    normal:
      base_cost: 2.5        # 基础费用（1000g以下）
      additional_cost: 1    # 续重费用（每超1kg）

    # 顺丰快递
    sf:
      base_cost: 6          # 基础费用（1000g以下）
      additional_cost: 1    # 续重费用（每超1000g）
      threshold: 66         # 使用顺丰快递的最低金额阈值

    # 极兔快递（超过3kg强制使用）
    jt:
      base_cost: 2.3        # 基础费用（1kg）
      additional_cost: 0.5  # 续重费用（每超1kg）
      weight_threshold: 3000 # 强制使用极兔的重量阈值（3kg = 3000g）

  # 人工费率（实际支付价格的百分比）
  labor_rate: 0.05

  # 赠品价格配置（按一级分类）
  gift_prices:
    # 安全套套：忽略，不计算赠品
    安全套套:
      enabled: false

    # 无赠品分类
    延时修护:
      enabled: true
      gift_cost: 0
    快感润滑:
      enabled: true
      gift_cost: 0
    玩具搭配:
      enabled: true
      gift_cost: 0

    # 有赠品分类（根据实际支付价格分档）
    男神玩具:
      enabled: true
      price_threshold: 98
      gift_cost_low: 2.5   # 小于阈值
      gift_cost_high: 8.5  # 大于等于阈值

    女神玩具:
      enabled: true
      price_threshold: 98
      gift_cost_low: 2.5   # 小于阈值
      gift_cost_high: 4.5  # 大于等于阈值

    情趣内衣:
      enabled: true
      price_threshold: 66
      gift_cost_low: 0     # 小于阈值（无赠品）
      gift_cost_high: 3.5  # 大于等于阈值

    情侣互动:
      enabled: true
      price_threshold: 66
      gift_cost_low: 0     # 小于阈值（无赠品）
      gift_cost_high: 2.5  # 大于等于阈值

    后庭开发:
      enabled: true
      price_threshold: 66
      gift_cost_low: 0     # 小于阈值（无赠品）
      gift_cost_high: 2.5  # 大于等于阈值

# 品牌映射表 - 将采集到的各种品牌名称统一为标准名称
brand_mapping:
  霏慕:
    - "Fée et moi"
    - "feimu"
    - "霏慕"
  魅动:
    - "魅动"
    - "meidong"
    - "MEIDONG"
  蜜恋:
    - "蜜恋"
    - "milian"
    - "MILIAN"
  Galaku:
    - "GALAKU"
    - "Galaku"
  君岛爱:
    - "君岛爱"
  热恋:
    - "热恋"
  谜姬:
    - "谜姬"
    - "MizzZee/谜姬"
  劳乐斯:
    - "劳乐斯"
  杜蕾斯:
    - "杜蕾斯"
  捷古斯:
    - "捷古斯"
  私激:
    - "私激"
  # 可以继续添加其他品牌映射
  # 格式:
  # 标准品牌名:
  #   - "变体1"
  #   - "变体2"


# 副规格配置 - 根据商品分类设置固定的sub_spec_value
sub_spec_config:
  # 情趣内衣：使用AI处理，不设置固定值
  情趣内衣:
    use_ai_processing: true
    fixed_value: null

  # 男神玩具：固定副规格
  男神玩具:
    use_ai_processing: false
    fixed_value: "赠送 润滑液或男神大礼包【具体赠送看详情页】"

  # 女神玩具：固定副规格
  女神玩具:
    use_ai_processing: false
    fixed_value: "赠送 润滑液或女神大礼包【具体赠送看详情页】"

  # 情侣互动：固定副规格
  情侣互动:
    use_ai_processing: false
    fixed_value: "满66元送 200ml 人体润滑液"

  # 后庭开发：固定副规格
  后庭开发:
    use_ai_processing: false
    fixed_value: "满66元送 200ml 人体润滑液"

  # 快感润滑：无副规格
  快感润滑:
    use_ai_processing: false
    fixed_value: ""

  # 玩具搭配：无副规格
  玩具搭配:
    use_ai_processing: false
    fixed_value: ""
  
  # 安全套套：无副规格
  安全套套:
    use_ai_processing: false
    fixed_value: ""

  # 其他分类默认使用AI处理
  default:
    use_ai_processing: true
    fixed_value: null

  # 优先级规则：如果商品同时包含多个分类，按优先级选择
  priority_rules:
    # 男神玩具和女神玩具优先级最高
    - categories: ["男神玩具"]
      sub_spec_value: "赠送 润滑液或男神大礼包【具体赠送看详情页】"
    - categories: ["女神玩具"]
      sub_spec_value: "赠送 润滑液或女神大礼包【具体赠送看详情页】"
    # 情侣互动和后庭开发次优先级
    - categories: ["情侣互动", "后庭开发"]
      sub_spec_value: "满66元送 200ml 人体润滑液"




# 文件夹整理关键词配置
folder_organize_keywords:
  # 明确需要移入资料文件夹的关键词
  move_to_data_keywords:
    - "无遮挡"
    - "无码"
    - "全裸"
    - "露点"
    - "私拍"
    - "写真"
    - "福利"
    - "特殊"
    - "限制"
    - "成人"

  # 主图文件夹名关键词（包含这些关键词的是主图文件夹）
  main_image_keywords:
    - "主图"
    - "主"
    - "封面"
    - "主要"
    - "主照"
    - "主拍"
    - "封面图"
    - "主要图片"
    - "产品图"
    - "商品图"

  # 详情文件夹名关键词（包含这些关键词的是详情文件夹）
  detail_image_keywords:
    - "详情"
    - "动"
    - "静"
    - "详细"
    - "细节"
    - "详情图"
    - "产品详情"
    - "商品详情"
    - "展示图"
    - "说明图"
    - "介绍图"

  # 规格文件夹名关键词（包含这些关键词的是规格文件夹）
  spec_image_keywords:
    - "规格"
    - "尺寸"
    - "颜色"
    - "款式"
    - "型号"
    - "规格图"
    - "尺寸图"
    - "颜色图"
    - "款式图"
    - "SKU"
    - "sku"

  # 文件夹优先级过滤关键词配置（按优先级从高到低排列，空字符串表示无关键词）
  priority_filter_keywords:
    # 主图优先级：无水印 > 800 > 无关键词 > 有水印 > 750
    main_image_priority:
      - "打禁"
      - "无水印"
      - "800"
      - ""  # 无关键词
      - "有水印"
      - "水印"
      - "750"

    # 详情优先级：无水印 > 无关键词 > 有水印
    detail_image_priority:
      - "打禁"
      - "无水印"
      - ""  # 无关键词
      - "有水印"
      - "水印"

    # 规格优先级：无关键词 > 无水印 > 实拍
    spec_image_priority:
      - ""  # 无关键词
      - "无水印"
      - "实拍"

# 主图深度处理配置
main_image_deep_process:
  # 支持的图片格式
  supported_formats:
    - ".jpg"
    - ".jpeg"
    - ".png"

  # 需要移除的子文件夹关键词（包含这些关键词的子文件夹会被直接移除到资料文件夹）
  remove_subfolder_keywords:
    - "白底"
    - "750"
    - "京东"
    - "蘑菇街"
    - "淘宝"
    - "拼多多"
    - "小红书"
    - "抖音"
    - "快手"
    - "有水印"
    - "水印"
    - "压缩"
    - "缩略图"
    - "长图"
    - "竖图"
    - "横图"

# 详情深度处理配置
detail_image_deep_process:
  # 支持的图片格式（详情支持gif）
  supported_formats:
    - ".jpg"
    - ".jpeg"
    - ".png"
    - ".gif"

  # 文件大小阈值（字节）
  small_file_threshold: 1048576  # 1MB = 1024*1024 bytes
  large_file_threshold: 3145728  # 3MB = 3*1024*1024 bytes

  # 详情根目录最少文件数量阈值
  min_root_files_count: 5

  # 需要移除的详情子文件夹关键词
  remove_detail_subfolder_keywords:
    - "无码"
    - "无遮挡"

  # 长图检测配置
  long_image_detection:
    # 高度阈值倍数（高度超过宽度的倍数视为长图）
    height_width_ratio: 5.0
    # 切片后的标准高度（像素）
    standard_slice_height: 4000

# 文件过滤配置
file_filter_keywords:
  # 需要移除的文件关键词（包含这些关键词的文件会被移除到资料文件夹）
  remove_file_keywords:
    - "私处"
    - "遮挡"
    - "臀"
    - "屁股"
    - "展示"
    - "撩衣"
    - "舌头"
    - "漏"
    - "乳"
    - "走光"
    - "姿势"
    - "衣"
    - "故意"
    - "不"
    - "腿"
    - "倒m"
    - "扯"
    - "衣"
    - "要求"
    - "手"
    - "肩"
    - "使用"
    - "建议"
    - "内裤"
    - "咪咪"
    - "若隐若现"
    - "白"
    - "违规"
    - "内"
    - "乳"
    - "腿"
    - "文"
    - "加"
    - "深"
    - "疑似"
    - "禁止"
    - "平台"
    - "风险"
    - "自理"
    - "动作"
    - "扒"
    - "有"
    - "嫌疑"

  # 过滤后最少保留文件数（如果过滤后文件数少于此值，则返回False）
  min_files_after_filter: 3

  # 高频关键词忽略阈值（如果某个关键词在文件夹中出现频率超过此百分比，则忽略该关键词）
  high_frequency_threshold: 80.0

# 白底图检测配置
white_background_detection:
  # 基于文件名的白底图关键词（包含这些关键词的文件会被判定为白底图）
  filename_keywords:
    - "白底"
    - "实拍"

# deepseek
deepseek:
  api_key: ***********************************
  model: deepseek-chat

  # SKU规格处理功能的系统提示词
  sku_processing_system_content: |
    # 角色
    你是一个专业的商品SKU拆分优化专家，具备深厚的商品信息处理知识和丰富经验，将依据用户给出的原商品标题和SKU信息，严格按照特定要求精准无误地进行拆分优化，并以python二维列表的格式规范输出。
    # 任务描述与要求
    1. 仔细从原SKU中精准提取出套装包含配件部分（一般为【】里的内容），并将其统一整理，确保内容完整准确。
    2. 对原SKU中除套装包含配件部分的剩余内容重新进行组合。组合时要充分考虑表述的流畅性与美感，做到好听通顺。合理使用◆，用◆◆包裹住每个品名和颜色，颜色后面必须加上“色”字；原来◆◆里没包裹颜色的需要添加加号。若规格值中有编号，要将编号去除；若存在多颜色描述，需将颜色合并。
    3. 严格按照用户输入SKU的顺序，将重新组合后的内容与提取出的套装包含配件部分精心整理成python二维列表的格式输出。
    # 参考示例
    示例1：
    用户：
    原标题：测试商品
    sku：["◆测试◆红【配件1 配件2】","◆测试◆红【配件1 配件2】 额外物品"]
    输出：
    [["◆测试 红色◆","【配件1 配件2】"],["◆测试 红色 ◆ +额外物品◆", "【配件1 配件2】"]]
    示例2：
    用户：
    原标题：新商品
    sku：["◆新商品◆蓝【基础套装】 小装饰1","◆新商品◆蓝【基础套装】 小装饰2"]
    输出：
    [["◆新商品 蓝色◆","【基础套装】"],["◆新商品 蓝色 ◆ +小装饰2◆", "【基础套装】"]]
    示例3：
    用户：
    原标题：服饰
    sku：["◆服饰◆黑【衣服 裤子】","◆服饰◆黑【衣服 裤子】 鞋子"]
    输出：
    [["◆服饰 黑色◆","【衣服 裤子】"],["◆服饰 黑色 ◆ +鞋子◆", "【衣服 裤子】"]]
    示例4：
    用户：
    原标题：商品
    sku：["◆素雅动人 7018 蓝色 ◆ +7210 白色长筒丝袜◆","◆萌咪女友 白色 粉色 ◆ +白长筒丝袜◆"]
    输出：
    [["◆素雅动人 蓝色 ◆ +白色长筒丝袜◆"],["◆萌咪女友 粉白色 ◆ +白长筒丝袜◆"]]
    # 相关限制
    1. 务必严格按照用户输入的SKU顺序进行输出，确保顺序准确无误。
    2. 输出格式必须严格为python二维列表格式，保证格式规范。
    3. 组合后的内容要完全符合任务描述中的表述规范，杜绝出现任何不规范表述。
    
  # 商品分类功能的系统提示词
  classification_system_content: |
    # 角色
    你是一个商品分类专家，你将根据商品标题，参考预设的一级类目和二级类目，为商品进行准确分类。
    # 任务描述与要求
    1. 仔细分析商品标题所体现的商品特性。
    2. 严格依据预设的类目进行分类，从给定的分类中选择完全一样的文字作为分类结果，不得自创类目。
    3. 分类结果要与商品标题所展现的商品特性相匹配。
    4. 分类结果必须以{"category_level1": "xxx", "category_level2": "xxx"}或{"category_level1": ["xxx","xxx"], "category_level2": ["xxx","xxx"]}或者{"category_level1": "xxx", "category_level2": ["xxx","xxx"]}的格式返回，确保格式准确无误。
    # 参考示例
    示例 1：
    用户：商品标题"某品牌延时喷剂"
    输出：{"category_level1": "延时修护", "category_level2": "延时喷剂"}
    示例 2：
    用户：商品标题"某款情趣跳蛋"
    输出：{"category_level1": "女神玩具", "category_level2": "情趣跳蛋"}
    示例 3：
    用户：商品标题"性感丝袜"
    输出：{"category_level1": "情趣内衣", "category_level2": "丝袜诱惑"}
    # 相关限制
    1. 必须严格依据预设的类目进行分类，不得自创类目。
    2. 分类结果必须符合商品标题和SKU值所体现的商品特性。
    3. 返回格式必须严格遵循{"category_level1": "xxx", "category_level2": "xxx"}或{"category_level1": ["xxx","xxx"], "category_level2": ["xxx","xxx"]}或者{"category_level1": "xxx", "category_level2": ["xxx","xxx"]}的要求，不得有格式错误。

    # 必须从下面预设分类中选择
    预设分类有， 括号前是一级分类，括号里对应二级分类
    延时修护（延时喷剂，修护清洁）
    男神玩具（飞机杯，女优名器，阴臀胸腿，真人娃娃）
    女神玩具（蜜豆吮吸，情趣跳蛋，震动棒，远程控制，阳具炮机，胸部刺激）
    快感润滑（快感增强，人体润滑，口娇水）
    情趣内衣（性感睡裙，制服诱惑，连体/束身，丝袜诱惑，激情三点，服饰搭配）
    情侣互动（SM器具，情趣套环，另类玩具）
    后庭开发（润滑清洗，肛塞拉珠，前列腺仪）
    玩具搭配（玩具配件，玩具收纳，清洗清洁）

    # 分类补充说明
    服饰搭配：这个只有在商品单独是服饰的时候才会出现，否则不要选这个分类，服饰搭配只会单独作为二级分类，不会和其他分类同时出现
    制服诱惑：标题里面包含动物或者职业的时候需要包含这个分类
    激情三点：标题包含内衣内裤文胸等也算作这个里面的
    远程控制：标题包含远程，遥控，小程序，AI等关键词，需要包含这个

  # 标题美化功能的系统提示词
  title_beautify_system_content: |
    # 角色
    你是一位专业的标题美化助手，你将根据原标题和规格值对标题进行美化。根据以下规则一步步执行：
    # 任务描述与要求
    1. 首先查看规格值中是否存在美化词，若存在，则优先提取作为美化部分。
    2. 若规格值中没有美化词，接着查看标题规格中是否有特色词或人名等，若有则提取作为美化部分。
    3. 若上述都没有，则结合整体进行命名，美化词尽量为四个字，也允许3 - 5个字。
    4. 若美化词为3个字，直接用 『xxx』 的格式；若为5个字，采用 『xxのxxx』 的格式；若为4个字，采用 『xxxのxxx』 的格式 ，并将其放在原标题前面进行输出。
    # 参考示例
    示例1：
    用户：原标题：提花身陷镂空开裆连身袜7558，规格值：◆身姿沦陷 黑色◆
    输出：
    『身姿の沦陷』提花身陷镂空开裆连身袜7558

    示例2：
    用户：原标题：1:1复刻美臀，sku规格值：桥本有菜复刻美臀
    输出：
    『桥本の有菜』1:1复刻美臀

    示例3：
    用户：原标题：无线手动震动胶囊跳蛋，规格值：微信小程序（AI版）芒果黄
    输出：
    『远程の胶囊』无线手动震动胶囊跳蛋
    或者输出：
    『远程の控制』无线手动震动胶囊跳蛋

    # 相关限制
    1. 美化词应从规格值、标题规格特色词、人名或整体内容中提取。
    2. 美化词字数尽量控制在3 - 5个字。
    3. 严格按照指定的格式输出美化后的标题。