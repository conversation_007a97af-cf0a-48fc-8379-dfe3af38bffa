# 企业微信智能表格演示任务配置

# 任务基本信息
task_info:
  name: "企业微信智能表格演示"
  description: "演示企业微信智能表格API的各种功能"
  version: "1.0.0"
  author: "AI Code Guide"

# 企业微信配置 (可覆盖全局配置)
wecom:
  # 如果需要使用不同的企业微信应用，可以在这里配置
  # corp_id: "demo_corp_id"
  # corp_secret: "demo_corp_secret"
  
  # API配置
  api:
    timeout: 60  # 演示时使用更长的超时时间
    max_retry: 5
  
  # 演示表格配置
  demo_table:
    name_prefix: "演示表格"
    description: "这是一个API功能演示表格"
    auto_cleanup: false  # 是否自动清理演示数据

# 演示配置
demo:
  # 演示数据
  sample_data:
    employees:
      - name: "张三"
        age: 25
        hire_date: "2024-01-15"
        department: "技术部"
      - name: "李四"
        age: 30
        hire_date: "2024-02-01"
        department: "产品部"
      - name: "王五"
        age: 28
        hire_date: "2024-02-15"
        department: "运营部"
  
  # 演示步骤控制
  steps:
    create_table: true
    add_fields: true
    add_data: true
    create_views: true
    batch_operations: true
    cleanup: false  # 是否在演示结束后清理数据

# 日志配置
logging:
  level: "INFO"
  detailed_api_logs: true  # 是否记录详细的API调用日志
  save_responses: true     # 是否保存API响应数据

# 错误处理配置
error_handling:
  continue_on_error: true   # 某个步骤失败时是否继续执行后续步骤
  retry_failed_operations: true
  max_retry_per_operation: 3
