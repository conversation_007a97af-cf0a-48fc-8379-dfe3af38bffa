"""
核心模块 - 智能导入
提供自动初始化的G和logger对象
"""

# 导入智能配置管理器
from .config_manager import G

# 导入智能日志系统
from .logger import (
    get_current_logger,
    log_info, log_error, log_success, log_step,
    log_warning, log_debug, get_log_file,
    log_function
)

# 创建智能logger对象
class SmartLogger:
    """智能日志器 - 自动初始化和代理"""

    def __getattr__(self, name):
        """代理所有方法到当前日志器"""
        logger = get_current_logger()
        return getattr(logger, name)

# 创建全局智能logger实例
logger = SmartLogger()

# 导出主要对象
__all__ = [
    'G',           # 智能配置管理器
    'logger',      # 智能日志器
    'log_info', 'log_error', 'log_success', 'log_step',
    'log_warning', 'log_debug', 'get_log_file', 'log_function'
]
