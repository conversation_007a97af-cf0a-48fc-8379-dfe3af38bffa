"""
配置管理器 - 支持点号访问的三层配置系统
"""
import yaml
import inspect
import os
from pathlib import Path
from typing import Any, Dict, Optional


class ConfigNode:
    """配置节点 - 支持点号访问"""
    
    def __init__(self, data: Dict[str, Any]):
        self._data = data
    
    def __getattr__(self, name: str) -> Any:
        """支持点号访问"""
        if name.startswith('_'):
            return super().__getattribute__(name)
        
        if name in self._data:
            value = self._data[name]
            # 如果是字典，返回ConfigNode以支持链式访问
            if isinstance(value, dict):
                return ConfigNode(value)
            return value
        
        raise AttributeError(f"配置项 '{name}' 不存在")
    
    def __getitem__(self, key: str) -> Any:
        """支持字典式访问"""
        return self.__getattr__(key)
    
    def __contains__(self, key: str) -> bool:
        """支持 in 操作符"""
        return key in self._data
    
    def get(self, key: str, default: Any = None) -> Any:
        """安全获取配置项"""
        try:
            return self.__getattr__(key)
        except AttributeError:
            return default
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return self._data.copy()


class ConfigManager:
    """配置管理器 - 智能三层配置系统"""

    def __init__(self):
        self._system_config = {}
        self._global_config = {}
        self._task_config = {}
        self._current_task = None
        self._auto_loaded = False

        # 加载系统和全局配置
        self._load_system_config()
        self._load_global_config()

    def _load_system_config(self):
        """加载系统配置"""
        config_file = Path("config/system.yaml")
        if config_file.exists():
            with open(config_file, 'r', encoding='utf-8') as f:
                self._system_config = yaml.safe_load(f) or {}

    def _load_global_config(self):
        """加载全局配置"""
        config_file = Path("config/global.yaml")
        if config_file.exists():
            with open(config_file, 'r', encoding='utf-8') as f:
                self._global_config = yaml.safe_load(f) or {}

    def _auto_detect_task_name(self) -> Optional[str]:
        """自动检测当前任务名称"""
        try:
            # 获取调用栈
            frame = inspect.currentframe()
            while frame:
                filename = frame.f_code.co_filename
                if filename.endswith('.py'):
                    file_path = Path(filename)

                    # 检查是否在tasks目录下
                    if 'tasks' in file_path.parts:
                        # 获取文件名（不含扩展名）
                        task_name = file_path.stem
                        if task_name != '__init__':
                            return task_name

                frame = frame.f_back

            return None
        except:
            return None

    def _auto_load_task_config(self):
        """自动加载任务配置"""
        if self._auto_loaded:
            return

        task_name = self._auto_detect_task_name()
        if task_name:
            self.load_task_config(task_name)
            self._auto_loaded = True

    def load_task_config(self, task_name: str):
        """加载任务专属配置"""
        self._current_task = task_name
        config_file = Path(f"config/tasks/{task_name}.yaml")

        if config_file.exists():
            with open(config_file, 'r', encoding='utf-8') as f:
                self._task_config = yaml.safe_load(f) or {}
        else:
            self._task_config = {}

        self._auto_loaded = True

    def _merge_configs(self, key: str) -> Any:
        """智能合并配置 - 任务配置 > 全局配置 > 系统配置"""
        # 优先级：任务配置 > 全局配置 > 系统配置

        # 1. 先查找任务配置
        if key in self._task_config:
            value = self._task_config[key]
            if isinstance(value, dict):
                # 如果是字典，需要深度合并
                merged_value = {}

                # 从系统配置开始
                if key in self._system_config and isinstance(self._system_config[key], dict):
                    merged_value.update(self._system_config[key])

                # 然后全局配置
                if key in self._global_config and isinstance(self._global_config[key], dict):
                    merged_value.update(self._global_config[key])

                # 最后任务配置（优先级最高）
                merged_value.update(value)

                return ConfigNode(merged_value)
            return value

        # 2. 再查找全局配置
        if key in self._global_config:
            value = self._global_config[key]
            if isinstance(value, dict):
                # 检查是否需要与系统配置合并
                merged_value = {}
                if key in self._system_config and isinstance(self._system_config[key], dict):
                    merged_value.update(self._system_config[key])
                merged_value.update(value)
                return ConfigNode(merged_value)
            return value

        # 3. 最后查找系统配置
        if key in self._system_config:
            value = self._system_config[key]
            if isinstance(value, dict):
                return ConfigNode(value)
            return value

        return None

    @property
    def system(self) -> ConfigNode:
        """系统配置访问 - 仅用于明确访问系统配置"""
        return ConfigNode(self._system_config)

    @property
    def task(self) -> ConfigNode:
        """任务配置访问 - 仅用于明确访问任务配置"""
        return ConfigNode(self._task_config)

    def __getattr__(self, name: str) -> Any:
        """智能配置访问 - 自动按优先级查找"""
        if name.startswith('_') or name in ['system', 'task']:
            return super().__getattribute__(name)

        # 自动加载任务配置
        self._auto_load_task_config()

        # 智能合并配置
        value = self._merge_configs(name)
        if value is not None:
            return value

        # 如果都没找到，提供友好的错误信息
        available_keys = set()
        available_keys.update(self._system_config.keys())
        available_keys.update(self._global_config.keys())
        available_keys.update(self._task_config.keys())

        if available_keys:
            suggestion = f"可用的配置项: {', '.join(sorted(available_keys))}"
        else:
            suggestion = "当前没有可用的配置项"

        raise AttributeError(f"配置项 '{name}' 不存在。{suggestion}")

    def get_current_task(self) -> Optional[str]:
        """获取当前任务名"""
        return self._current_task

    def reload_all(self):
        """重新加载所有配置"""
        self._load_system_config()
        self._load_global_config()
        if self._current_task:
            self.load_task_config(self._current_task)

    def get_config_source(self, key: str) -> str:
        """获取配置项的来源 - 用于调试"""
        if key in self._task_config:
            return f"任务配置 (tasks/{self._current_task}.yaml)"
        elif key in self._global_config:
            return "全局配置 (global.yaml)"
        elif key in self._system_config:
            return "系统配置 (system.yaml)"
        else:
            return "未找到"

    def debug_config(self, key: str = None):
        """调试配置信息"""
        if key:
            print(f"配置项 '{key}' 的来源: {self.get_config_source(key)}")
            try:
                value = getattr(self, key)
                print(f"当前值: {value}")
            except AttributeError as e:
                print(f"错误: {e}")
        else:
            print("=== 配置调试信息 ===")
            print(f"当前任务: {self._current_task}")
            print(f"系统配置项: {list(self._system_config.keys())}")
            print(f"全局配置项: {list(self._global_config.keys())}")
            print(f"任务配置项: {list(self._task_config.keys())}")


# 全局配置实例
G = ConfigManager()
