"""
智能日志系统 - 支持彩色输出和任务隔离
基于用户原有设计，增加彩色输出和多任务支持
"""
import logging
import sys
import inspect
import os
from datetime import datetime
from pathlib import Path
from colorama import Fore, Back, Style, init

# 初始化colorama
init(autoreset=True)


class ColoredFormatter(logging.Formatter):
    """彩色日志格式化器"""

    # 日志级别颜色映射
    COLORS = {
        'DEBUG': Fore.LIGHTBLACK_EX,     # 灰色 - 调试信息
        'INFO': '',                       # 默认颜色 - 正常信息
        'WARNING': Fore.YELLOW,          # 黄色 - 警告
        'ERROR': Fore.RED,               # 红色 - 错误
        'CRITICAL': Fore.RED + Back.WHITE, # 红底白字 - 严重错误
        'SUCCESS': Fore.GREEN + Style.BRIGHT,  # 亮绿色 - 成功
        'STEP': Fore.CYAN + Style.BRIGHT,      # 亮青色 - 步骤
    }
    
    def format(self, record):
        # 获取颜色
        color = self.COLORS.get(record.levelname, '')
        
        # 格式化消息
        formatted = super().format(record)
        
        # 应用颜色
        if color:
            formatted = f"{color}{formatted}{Style.RESET_ALL}"
        
        return formatted


class TaskLogger:
    """
    任务执行日志器 - 每个任务独立日志空间
    
    特点：
    1. 每次任务执行生成独立日志文件：任务名_时间戳.log
    2. 彩色控制台输出 + 详细文件记录
    3. 任务间日志完全隔离
    4. 支持上下文信息记录
    """

    def __init__(self, task_name, log_dir="logs"):
        self.task_name = task_name
        self.log_dir = Path(log_dir) / task_name
        self.log_dir.mkdir(parents=True, exist_ok=True)

        # 生成带时间戳的日志文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.log_file = self.log_dir / f"{task_name}_{timestamp}.log"

        # 创建logger
        logger_name = f"task_{task_name}_{timestamp}"
        self.logger = logging.getLogger(logger_name)
        self.logger.setLevel(logging.DEBUG)

        # 清除之前的handlers
        self.logger.handlers.clear()

        # 设置handlers
        self._setup_handlers()
        
        # 记录任务开始
        self.info(f"🚀 任务 [{task_name}] 开始执行")
        self.info(f"📝 日志文件: {self.log_file}")
    
    def _setup_handlers(self):
        """设置彩色控制台和文件处理器"""

        # 控制台格式（彩色，不包含任务标识）
        console_formatter = ColoredFormatter(
            '%(asctime)s | %(levelname)-8s | %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )

        # 文件格式（详细无颜色，不包含任务标识，文件名已包含任务信息）
        file_formatter = logging.Formatter(
            '%(asctime)s | %(levelname)-8s | %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )

        # 控制台输出（彩色）
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(console_formatter)
        self.logger.addHandler(console_handler)

        # 文件输出
        file_handler = logging.FileHandler(self.log_file, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(file_formatter)
        self.logger.addHandler(file_handler)

    def info(self, message, **kwargs):
        """信息日志"""
        self._log_with_context(logging.INFO, message, **kwargs)

    def error(self, message, **kwargs):
        """错误日志"""
        self._log_with_context(logging.ERROR, f"❌ {message}", **kwargs)

    def warning(self, message, **kwargs):
        """警告日志"""
        self._log_with_context(logging.WARNING, f"⚠️ {message}", **kwargs)

    def debug(self, message, **kwargs):
        """调试日志"""
        self._log_with_context(logging.DEBUG, message, **kwargs)

    def success(self, message, **kwargs):
        """成功日志"""
        # 使用自定义级别
        self._log_with_context(25, f"✅ {message}", level_name='SUCCESS', **kwargs)

    def step(self, step_name, message="", **kwargs):
        """步骤日志 - 方便追踪执行流程"""
        self._log_with_context(22, f"🔄 [{step_name}] {message}", level_name='STEP', **kwargs)

    def get_log_file(self):
        """获取日志文件路径"""
        return str(self.log_file)
    
    def _log_with_context(self, level, message, level_name=None, **kwargs):
        """带上下文和任务标识的日志记录"""

        # 添加额外的上下文信息
        context_parts = []

        # 处理所有传入的参数，使用友好的中文显示名称
        friendly_names = {
            'data': '数据',
            'file': '文件',
            'count': '数量',
            'duration': '耗时',
            'size': '大小',
            'status': '状态',
            'progress': '进度',
            'date': '日期',
            'user': '用户',
            'url': 'URL',
            'code': '代码',
            'script': '脚本',
            'product_code': '商品编号',
            'task': '任务',
            'step': '步骤',
            'result': '结果',
            'error': '错误',
            'total': '总数',
            'success': '成功',
            'failed': '失败',
            'processed': '已处理',
            'collected': '已采集',
            'total_urls': '总链接数',
            'avg_time': '平均耗时'
        }

        # 遍历所有传入的参数
        for key, value in kwargs.items():
            # 使用友好名称，如果没有则使用原始key
            display_name = friendly_names.get(key, key)
            context_parts.append(f"{display_name}:{value}")

        # 组装最终消息
        if context_parts:
            final_message = f"{message} | {' | '.join(context_parts)}"
        else:
            final_message = message

        # 记录日志
        if level_name:
            # 自定义级别，直接输出
            record = self.logger.makeRecord(
                self.logger.name, level, "", 0, final_message, (), None
            )
            record.levelname = level_name
            for handler in self.logger.handlers:
                handler.emit(record)
        else:
            self.logger.log(level, final_message)


# 当前任务的日志器
_current_logger = None

def _auto_detect_task_name() -> str:
    """自动检测当前任务名称"""
    try:
        # 获取调用栈
        frame = inspect.currentframe()
        while frame:
            filename = frame.f_code.co_filename
            if filename.endswith('.py'):
                file_path = Path(filename)

                # 检查是否在tasks目录下
                if 'tasks' in file_path.parts:
                    # 获取文件名（不含扩展名）
                    task_name = file_path.stem
                    if task_name != '__init__':
                        return task_name

            frame = frame.f_back

        # 如果没有检测到，使用默认名称
        return "unknown_task"
    except:
        return "unknown_task"

def _auto_init_logger():
    """自动初始化日志器"""
    global _current_logger
    if _current_logger is None:
        task_name = _auto_detect_task_name()
        _current_logger = TaskLogger(task_name)
    return _current_logger

def init_task_logger(task_name):
    """
    初始化任务日志器

    Args:
        task_name: 任务名称

    Returns:
        日志器实例
    """
    global _current_logger
    _current_logger = TaskLogger(task_name)
    return _current_logger

def get_current_logger():
    """获取当前任务的日志器"""
    return _current_logger or _auto_init_logger()

# 便捷函数 - 直接导入使用（自动初始化）
def log_info(message, **kwargs):
    """记录信息日志"""
    logger = get_current_logger()
    logger.info(message, **kwargs)

def log_error(message, **kwargs):
    """记录错误日志"""
    logger = get_current_logger()
    logger.error(message, **kwargs)

def log_success(message, **kwargs):
    """记录成功日志"""
    logger = get_current_logger()
    logger.success(message, **kwargs)

def log_step(step_name, message="", **kwargs):
    """记录步骤日志"""
    logger = get_current_logger()
    logger.step(step_name, message, **kwargs)

def log_warning(message, **kwargs):
    """记录警告日志"""
    logger = get_current_logger()
    logger.warning(message, **kwargs)

def log_debug(message, **kwargs):
    """记录调试日志"""
    logger = get_current_logger()
    logger.debug(message, **kwargs)

def get_log_file():
    """获取当前日志文件路径"""
    logger = get_current_logger()
    return logger.get_log_file()

# 装饰器 - 自动记录函数执行
def log_function(func_name=None):
    """
    函数执行日志装饰器

    使用方法:
    @log_function("处理Excel文件")
    def process_excel():
        pass
    """
    def decorator(func):
        import functools
        import time

        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            name = func_name or func.__name__

            log_step(name, "开始执行")
            start_time = time.time()

            try:
                result = func(*args, **kwargs)
                duration = f"{time.time() - start_time:.2f}s"
                log_success(f"{name} 执行成功", duration=duration)
                return result
            except Exception as e:
                duration = f"{time.time() - start_time:.2f}s"
                log_error(f"{name} 执行失败: {e}", duration=duration)
                raise

        return wrapper
    return decorator


class SystemLogger:
    """系统级日志器 - 用于主程序日志"""
    
    def __init__(self):
        self.logger = logging.getLogger("system")
        self.logger.setLevel(logging.INFO)
        self.logger.handlers.clear()
        
        # 只输出到控制台
        console_formatter = ColoredFormatter(
            '%(asctime)s | %(levelname)-8s | %(message)s',
            datefmt='%H:%M:%S'
        )
        
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(console_formatter)
        self.logger.addHandler(console_handler)
    
    def info(self, message):
        self.logger.info(message)
    
    def error(self, message):
        self.logger.error(message)
    
    def warning(self, message):
        self.logger.warning(message)
