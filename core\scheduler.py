"""
定时任务调度器
"""
import time
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from pathlib import Path
import yaml

from .task_manager import TaskManager
from .logger import SystemLogger


class CronParser:
    """简单的Cron表达式解析器"""
    
    @staticmethod
    def parse_cron(cron_expr: str) -> Dict[str, Any]:
        """
        解析cron表达式
        格式: 分钟 小时 日 月 星期
        例如: "0 9 * * 1-5" 表示工作日上午9点
        """
        parts = cron_expr.strip().split()
        if len(parts) != 5:
            raise ValueError(f"无效的cron表达式: {cron_expr}")
        
        return {
            'minute': parts[0],
            'hour': parts[1], 
            'day': parts[2],
            'month': parts[3],
            'weekday': parts[4]
        }
    
    @staticmethod
    def should_run(cron_dict: Dict[str, str], now: datetime) -> bool:
        """检查当前时间是否匹配cron表达式"""
        try:
            # 检查分钟
            if not CronParser._match_field(cron_dict['minute'], now.minute, 0, 59):
                return False
            
            # 检查小时
            if not CronParser._match_field(cron_dict['hour'], now.hour, 0, 23):
                return False
            
            # 检查日期
            if not CronParser._match_field(cron_dict['day'], now.day, 1, 31):
                return False
            
            # 检查月份
            if not CronParser._match_field(cron_dict['month'], now.month, 1, 12):
                return False
            
            # 检查星期 (0=周日, 1=周一, ..., 6=周六)
            weekday = now.weekday() + 1  # Python的weekday: 0=周一, 转换为1=周一
            if weekday == 7:  # 周日转换为0
                weekday = 0
            if not CronParser._match_field(cron_dict['weekday'], weekday, 0, 6):
                return False
            
            return True
        except Exception:
            return False
    
    @staticmethod
    def _match_field(pattern: str, value: int, min_val: int, max_val: int) -> bool:
        """匹配单个字段"""
        if pattern == '*':
            return True
        
        # 处理范围 (如 1-5)
        if '-' in pattern:
            start, end = map(int, pattern.split('-'))
            return start <= value <= end
        
        # 处理步长 (如 */2)
        if '/' in pattern:
            if pattern.startswith('*/'):
                step = int(pattern[2:])
                return value % step == 0
            else:
                range_part, step = pattern.split('/')
                step = int(step)
                if range_part == '*':
                    return value % step == 0
                else:
                    start, end = map(int, range_part.split('-'))
                    return start <= value <= end and (value - start) % step == 0
        
        # 处理列表 (如 1,3,5)
        if ',' in pattern:
            values = [int(x.strip()) for x in pattern.split(',')]
            return value in values
        
        # 处理单个值
        try:
            return int(pattern) == value
        except ValueError:
            return False


class TaskScheduler:
    """任务调度器"""
    
    def __init__(self):
        self.task_manager = TaskManager()
        self.logger = SystemLogger()
        self.running = False
        self.scheduler_thread = None
        self.scheduled_tasks = []
        
    def load_scheduled_tasks(self) -> List[Dict[str, Any]]:
        """加载所有定时任务"""
        scheduled_tasks = []
        tasks_dir = Path("tasks")
        config_dir = Path("config/tasks")
        
        if not tasks_dir.exists():
            return scheduled_tasks
        
        # 扫描所有任务配置
        for task_file in tasks_dir.glob("*.py"):
            if task_file.name.startswith("__"):
                continue
                
            task_name = task_file.stem
            config_file = config_dir / f"{task_name}.yaml"
            
            if not config_file.exists():
                continue
                
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f) or {}
                
                # 检查是否有定时配置
                schedule_config = config.get('schedule', {})
                if schedule_config.get('enabled', False) and schedule_config.get('cron'):
                    cron_expr = schedule_config['cron']
                    
                    # 解析cron表达式
                    try:
                        cron_dict = CronParser.parse_cron(cron_expr)
                        scheduled_tasks.append({
                            'name': task_name,
                            'cron': cron_expr,
                            'cron_dict': cron_dict,
                            'config': config,
                            'last_run': None,
                            'next_run': None
                        })
                        self.logger.info(f"📅 加载定时任务: {task_name} ({cron_expr})")
                    except ValueError as e:
                        self.logger.error(f"❌ 任务 {task_name} cron表达式错误: {e}")
                        
            except Exception as e:
                self.logger.error(f"❌ 加载任务配置失败 {config_file}: {e}")
        
        return scheduled_tasks
    
    def start(self):
        """启动调度器"""
        if self.running:
            self.logger.warning("调度器已在运行中")
            return
        
        self.logger.info("🚀 启动定时任务调度器")
        
        # 加载定时任务
        self.scheduled_tasks = self.load_scheduled_tasks()
        
        if not self.scheduled_tasks:
            self.logger.info("📋 没有发现定时任务")
            return
        
        self.logger.info(f"📋 发现 {len(self.scheduled_tasks)} 个定时任务")
        
        # 启动调度线程
        self.running = True
        self.scheduler_thread = threading.Thread(target=self._scheduler_loop, daemon=True)
        self.scheduler_thread.start()
        
        self.logger.info("✅ 定时任务调度器启动成功")
    
    def stop(self):
        """停止调度器"""
        if not self.running:
            return
        
        self.logger.info("🛑 停止定时任务调度器")
        self.running = False
        
        if self.scheduler_thread:
            self.scheduler_thread.join(timeout=5)
        
        self.logger.info("✅ 定时任务调度器已停止")
    
    def _scheduler_loop(self):
        """调度器主循环"""
        while self.running:
            try:
                now = datetime.now()
                
                # 检查每个定时任务
                for task in self.scheduled_tasks:
                    if self._should_run_task(task, now):
                        self._run_scheduled_task(task, now)
                
                # 每分钟检查一次
                time.sleep(60)
                
            except Exception as e:
                self.logger.error(f"调度器循环异常: {e}")
                time.sleep(60)
    
    def _should_run_task(self, task: Dict[str, Any], now: datetime) -> bool:
        """检查任务是否应该运行"""
        # 检查cron表达式是否匹配
        if not CronParser.should_run(task['cron_dict'], now):
            return False
        
        # 检查是否在同一分钟内已经运行过
        last_run = task.get('last_run')
        if last_run and last_run.year == now.year and last_run.month == now.month and \
           last_run.day == now.day and last_run.hour == now.hour and last_run.minute == now.minute:
            return False
        
        return True
    
    def _run_scheduled_task(self, task: Dict[str, Any], now: datetime):
        """运行定时任务"""
        task_name = task['name']
        
        self.logger.info(f"⏰ 定时执行任务: {task_name}")
        
        # 更新最后运行时间
        task['last_run'] = now
        
        # 在新线程中运行任务，避免阻塞调度器
        task_thread = threading.Thread(
            target=self._execute_task,
            args=(task_name,),
            daemon=True
        )
        task_thread.start()
    
    def _execute_task(self, task_name: str):
        """执行任务"""
        try:
            success = self.task_manager.run_single_task(task_name)
            if success:
                self.logger.info(f"✅ 定时任务 {task_name} 执行成功")
            else:
                self.logger.error(f"❌ 定时任务 {task_name} 执行失败")
        except Exception as e:
            self.logger.error(f"❌ 定时任务 {task_name} 执行异常: {e}")
        finally:
            # 无论成功失败，都显示下次执行时间
            self._show_next_run_time(task_name)

    def _show_next_run_time(self, task_name: str):
        """显示指定任务的下次执行时间"""
        for task in self.scheduled_tasks:
            if task['name'] == task_name:
                now = datetime.now()

                # 计算下次运行时间
                for minutes in range(1, 24 * 60):
                    future_time = now + timedelta(minutes=minutes)
                    if CronParser.should_run(task['cron_dict'], future_time):
                        next_run = future_time.strftime('%Y-%m-%d %H:%M:%S')
                        self.logger.info(f"📅 任务 {task_name} 下次执行时间: {next_run}")
                        return

                self.logger.info(f"📅 任务 {task_name} 未来24小时内无执行计划")
                break
    
    def list_scheduled_tasks(self) -> List[Dict[str, Any]]:
        """列出所有定时任务"""
        return self.scheduled_tasks.copy()
    
    def get_next_run_times(self) -> Dict[str, str]:
        """获取下次运行时间"""
        next_runs = {}
        now = datetime.now()
        
        for task in self.scheduled_tasks:
            # 简单计算下次运行时间（检查未来24小时内）
            for minutes in range(1, 24 * 60):
                future_time = now + timedelta(minutes=minutes)
                if CronParser.should_run(task['cron_dict'], future_time):
                    next_runs[task['name']] = future_time.strftime('%Y-%m-%d %H:%M:%S')
                    break
            else:
                next_runs[task['name']] = "未来24小时内无执行计划"
        
        return next_runs


# 全局调度器实例
_scheduler: Optional[TaskScheduler] = None

def get_scheduler() -> TaskScheduler:
    """获取调度器实例"""
    global _scheduler
    if _scheduler is None:
        _scheduler = TaskScheduler()
    return _scheduler

def start_scheduler():
    """启动调度器"""
    scheduler = get_scheduler()
    scheduler.start()

def stop_scheduler():
    """停止调度器"""
    scheduler = get_scheduler()
    scheduler.stop()


class SingleTaskScheduler:
    """单任务调度器 - 只调度指定的单个任务"""

    def __init__(self, task_name: str, cron_expr: str):
        self.task_name = task_name
        self.cron_expr = cron_expr
        self.task_manager = TaskManager()
        self.logger = SystemLogger()
        self.running = False
        self.scheduler_thread = None

        try:
            self.cron_dict = CronParser.parse_cron(cron_expr)
        except ValueError as e:
            raise ValueError(f"无效的cron表达式 '{cron_expr}': {e}")

    def start(self):
        """启动单任务调度器"""
        if self.running:
            self.logger.warning("调度器已在运行中")
            return

        self.logger.info(f"🚀 启动单任务调度器: {self.task_name}")

        # 启动调度线程
        self.running = True
        self.scheduler_thread = threading.Thread(target=self._scheduler_loop, daemon=True)
        self.scheduler_thread.start()

        self.logger.info("✅ 单任务调度器启动成功")

    def stop(self):
        """停止调度器"""
        if not self.running:
            return

        self.logger.info("🛑 停止单任务调度器")
        self.running = False

        if self.scheduler_thread:
            self.scheduler_thread.join(timeout=5)

        self.logger.info("✅ 单任务调度器已停止")

    def _scheduler_loop(self):
        """调度器主循环"""
        last_run = None

        while self.running:
            try:
                now = datetime.now()

                # 检查是否应该运行任务
                if self._should_run_task(now, last_run):
                    self._run_task(now)
                    last_run = now

                # 每分钟检查一次
                time.sleep(60)

            except Exception as e:
                self.logger.error(f"调度器循环异常: {e}")
                time.sleep(60)

    def _should_run_task(self, now: datetime, last_run: datetime = None) -> bool:
        """检查任务是否应该运行"""
        # 检查cron表达式是否匹配
        if not CronParser.should_run(self.cron_dict, now):
            return False

        # 检查是否在同一分钟内已经运行过
        if last_run and last_run.year == now.year and last_run.month == now.month and \
           last_run.day == now.day and last_run.hour == now.hour and last_run.minute == now.minute:
            return False

        return True

    def _run_task(self, now: datetime):
        """运行任务"""
        self.logger.info(f"⏰ 定时执行任务: {self.task_name}")

        # 在新线程中运行任务，避免阻塞调度器
        task_thread = threading.Thread(
            target=self._execute_task,
            daemon=True
        )
        task_thread.start()

    def _execute_task(self):
        """执行任务"""
        try:
            success = self.task_manager.run_single_task(self.task_name)
            if success:
                self.logger.info(f"✅ 定时任务 {self.task_name} 执行成功")
            else:
                self.logger.error(f"❌ 定时任务 {self.task_name} 执行失败")
        except Exception as e:
            self.logger.error(f"❌ 定时任务 {self.task_name} 执行异常: {e}")
        finally:
            # 无论成功失败，都显示下次执行时间
            next_run = self.get_next_run_time()
            if next_run:
                self.logger.info(f"📅 下次执行时间: {next_run}")

    def get_next_run_time(self) -> str:
        """获取下次运行时间"""
        now = datetime.now()

        # 检查未来24小时内的运行时间
        for minutes in range(1, 24 * 60):
            future_time = now + timedelta(minutes=minutes)
            if CronParser.should_run(self.cron_dict, future_time):
                return future_time.strftime('%Y-%m-%d %H:%M:%S')

        return "未来24小时内无执行计划"
