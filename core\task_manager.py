"""
任务管理器 - 任务发现、选择和执行的核心逻辑
"""
import importlib.util
import subprocess
import sys
import os
import ast
from pathlib import Path
import yaml
from datetime import datetime
from typing import List, Dict, Any, Optional, Set

from .config_manager import G
from .logger import TaskLogger, SystemLogger
from .terminal_title import set_terminal_title
from .wechat_notifier import notify_task_success, notify_task_failure


class DependencyManager:
    """依赖管理器 - 自动检查和安装依赖"""

    def __init__(self):
        self.standard_modules = self._get_standard_modules()

    def analyze_task_dependencies(self, task_file: Path) -> List[str]:
        """分析任务文件的依赖，包括models目录中的依赖"""
        all_dependencies = set()

        try:
            # 1. 分析任务文件本身的依赖
            task_deps = self._analyze_single_file(task_file)
            all_dependencies.update(task_deps)

            # 2. 分析models目录中被引用文件的依赖
            models_deps = self._analyze_models_dependencies(task_file)
            all_dependencies.update(models_deps)

            return list(all_dependencies)
        except Exception:
            return []

    def _analyze_single_file(self, file_path: Path) -> List[str]:
        """分析单个文件的第三方依赖"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            tree = ast.parse(content, filename=str(file_path))
            imports = self._extract_imports(tree)
            third_party_deps = self._filter_third_party_dependencies(imports)

            return third_party_deps
        except Exception:
            return []

    def _analyze_models_dependencies(self, task_file: Path) -> List[str]:
        """分析models目录中被任务引用的文件的依赖"""
        dependencies = set()
        models_dir = Path("models")

        if not models_dir.exists():
            return list(dependencies)

        try:
            # 找出任务文件中引用的models文件
            referenced_models = self._find_referenced_models(task_file)

            # 分析每个被引用的models文件的依赖
            for model_name in referenced_models:
                model_file = models_dir / f"{model_name}.py"
                if model_file.exists():
                    model_deps = self._analyze_single_file(model_file)
                    dependencies.update(model_deps)

        except Exception:
            pass

        return list(dependencies)

    def _find_referenced_models(self, task_file: Path) -> List[str]:
        """查找任务文件中引用的models文件"""
        referenced_models = set()

        try:
            with open(task_file, 'r', encoding='utf-8') as f:
                content = f.read()

            tree = ast.parse(content, filename=str(task_file))

            for node in ast.walk(tree):
                if isinstance(node, ast.ImportFrom):
                    if node.module and node.module.startswith('models.'):
                        # 提取models文件名
                        model_parts = node.module.split('.')
                        if len(model_parts) >= 2:
                            model_file = model_parts[1]
                            referenced_models.add(model_file)

        except Exception:
            pass

        return list(referenced_models)

    def _extract_imports(self, tree: ast.AST) -> List[str]:
        """从AST中提取导入的模块名"""
        imports = set()

        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                for alias in node.names:
                    module_name = alias.name.split('.')[0]
                    imports.add(module_name)
            elif isinstance(node, ast.ImportFrom):
                if node.module and node.level == 0:  # 只处理绝对导入
                    module_name = node.module.split('.')[0]
                    imports.add(module_name)

        return list(imports)

    def _filter_third_party_dependencies(self, imports: List[str]) -> List[str]:
        """过滤出第三方依赖"""
        third_party = []

        for module in imports:
            if module and not self._is_standard_library(module) and not self._is_local_module(module):
                third_party.append(module)

        return third_party

    def _is_standard_library(self, module_name: str) -> bool:
        """判断是否为标准库模块"""
        return module_name in self.standard_modules

    def _is_local_module(self, module_name: str) -> bool:
        """判断是否为本地模块"""
        # 简单判断：core, utils, tasks, models, config 等为本地模块
        local_prefixes = {'core', 'utils', 'tasks', 'models', 'config'}
        return module_name in local_prefixes

    def _get_standard_modules(self) -> Set[str]:
        """获取标准库模块列表"""
        standard_modules = {
            # 内置模块
            'builtins', 'sys', 'os', 'io', 'time', 'datetime', 'math', 'random',
            'json', 'csv', 'xml', 'html', 'urllib', 'http', 'email', 'base64',
            'hashlib', 'hmac', 'secrets', 'uuid', 'pickle', 'copy', 'pprint',

            # 文件和路径
            'pathlib', 'glob', 'fnmatch', 'tempfile', 'shutil', 'stat',

            # 数据结构
            'collections', 'array', 'heapq', 'bisect', 'weakref',

            # 函数式编程
            'itertools', 'functools', 'operator',

            # 字符串和正则
            'string', 're', 'difflib', 'textwrap', 'unicodedata',

            # 数据压缩
            'zlib', 'gzip', 'bz2', 'lzma', 'zipfile', 'tarfile',

            # 网络和并发
            'socket', 'ssl', 'select', 'selectors', 'asyncio',
            'threading', 'multiprocessing', 'concurrent', 'subprocess', 'queue',

            # 配置和日志
            'configparser', 'logging', 'argparse', 'getopt',

            # 测试和调试
            'unittest', 'doctest', 'test', 'pdb', 'profile', 'cProfile',
            'timeit', 'trace', 'traceback',

            # 系统相关
            'platform', 'ctypes', 'mmap', 'resource', 'gc', 'atexit',

            # 其他常用
            'warnings', 'contextlib', 'abc', 'numbers', 'decimal', 'fractions',
            'statistics', 'enum', 'types', 'typing', 'dataclasses', 'ast',
            'importlib'
        }

        # 添加sys.builtin_module_names中的模块
        standard_modules.update(sys.builtin_module_names)

        return standard_modules

    def check_and_install_dependencies(self, dependencies: List[str], logger: SystemLogger) -> bool:
        """检查并安装依赖包"""
        if not dependencies:
            return True
        
        logger.info(f"🔍 检查依赖: {', '.join(dependencies)}")
        
        missing_deps = []
        for dep in dependencies:
            try:
                __import__(dep)
                print(f"  ✅ {dep}")
            except ImportError:
                missing_deps.append(dep)
                print(f"  ❌ {dep} (缺失)")
        
        if missing_deps:
            logger.info(f"📦 开始安装缺失依赖: {', '.join(missing_deps)}")
            
            for dep in missing_deps:
                try:
                    logger.info(f"  正在安装 {dep}...")
                    result = subprocess.run(
                        [sys.executable, "-m", "pip", "install", dep],
                        capture_output=True,
                        text=True,
                        check=True
                    )
                    logger.info(f"  ✅ {dep} 安装成功")
                except subprocess.CalledProcessError as e:
                    logger.error(f"  ❌ {dep} 安装失败: {e}")
                    return False
        
        return True


class TaskManager:
    """任务管理器 - 核心任务处理逻辑"""
    
    def __init__(self):
        self.system_logger = SystemLogger()
        self.dependency_manager = DependencyManager()
    
    def discover_tasks(self) -> List[Dict[str, Any]]:
        """发现所有可用任务"""
        tasks = []
        tasks_dir = Path("tasks")
        config_dir = Path("config/tasks")
        
        if not tasks_dir.exists():
            self.system_logger.error(f"任务目录不存在: {tasks_dir}")
            return tasks
        
        # 扫描tasks目录下的py文件
        for task_file in tasks_dir.glob("*.py"):
            if task_file.name.startswith("__"):
                continue
                
            task_name = task_file.stem
            config_file = config_dir / f"{task_name}.yaml"
            
            # 加载配置
            config = {}
            if config_file.exists():
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        config = yaml.safe_load(f) or {}
                except Exception as e:
                    self.system_logger.warning(f"加载配置失败 {config_file}: {e}")
            
            tasks.append({
                'name': task_name,
                'file': task_file.name,
                'path': task_file,
                'config': config
            })
        
        return tasks
    
    def run_single_task(self, task_name: str) -> bool:
        """运行单个任务"""
        task_file = Path("tasks") / f"{task_name}.py"
        
        if not task_file.exists():
            self.system_logger.error(f"任务文件不存在: {task_file}")
            return False
        
        start_time = datetime.now()

        try:
            # 1. 加载任务配置到G对象
            G.load_task_config(task_name)

            # 2. 设置终端标题为任务名
            set_terminal_title(task_name)

            # 3. 智能依赖管理
            task_config = G.task.to_dict()
            config_dependencies = task_config.get('task_info', {}).get('dependencies', [])

            # 分析代码中的依赖
            code_dependencies = self.dependency_manager.analyze_task_dependencies(task_file)

            # 智能依赖策略
            if config_dependencies:
                # 如果有手动配置，使用混合模式
                all_dependencies = list(set((config_dependencies or []) + (code_dependencies or [])))
                config_count = len(config_dependencies or [])
                code_count = len(code_dependencies or [])
                self.system_logger.info(f"📋 混合依赖模式: 配置文件({config_count}) + 自动检测({code_count}) = 总计({len(all_dependencies)})")
                if code_dependencies:
                    auto_detected = set(code_dependencies) - set(config_dependencies)
                    if auto_detected:
                        self.system_logger.info(f"🔍 自动检测到额外依赖: {', '.join(auto_detected)}")
            else:
                # 如果没有手动配置，使用纯自动模式
                all_dependencies = code_dependencies or []
                if all_dependencies:
                    self.system_logger.info(f"🤖 纯自动依赖模式: 检测到 {len(all_dependencies)} 个依赖")
                    self.system_logger.info(f"🔍 自动检测依赖: {', '.join(all_dependencies)}")
                else:
                    self.system_logger.info("📋 无依赖需求")

            if not self.dependency_manager.check_and_install_dependencies(all_dependencies, self.system_logger):
                self.system_logger.error("依赖安装失败，任务终止")
                # 发送失败通知
                duration = str(datetime.now() - start_time).split('.')[0]
                notify_task_failure(task_name, "依赖安装失败", duration)
                return False

            # 4. 初始化任务日志
            task_logger = TaskLogger(task_name)

            # 5. 执行任务
            self.system_logger.info(f"🚀 启动任务: {task_name}")
            success, error_message = self._import_and_run(task_file, task_name, task_logger)

            # 6. 计算执行时间并发送通知
            end_time = datetime.now()
            duration = str(end_time - start_time).split('.')[0]

            if success:
                self.system_logger.info(f"✅ 任务 {task_name} 执行完成")
                # 发送成功通知
                notify_task_success(task_name, duration)
            else:
                self.system_logger.error(f"❌ 任务 {task_name} 执行失败")
                # 发送失败通知，包含具体错误信息
                notify_task_failure(task_name, error_message or "任务执行失败", duration)

            return success
            
        except Exception as e:
            self.system_logger.error(f"任务执行异常: {e}")
            # 发送异常通知
            duration = str(datetime.now() - start_time).split('.')[0]
            notify_task_failure(task_name, f"任务执行异常: {e}", duration)
            return False
    
    def _import_and_run(self, task_file: Path, task_name: str, logger: TaskLogger) -> tuple[bool, str]:
        """导入并运行任务模块，返回(成功状态, 错误信息)"""
        try:
            # 动态导入任务模块
            spec = importlib.util.spec_from_file_location(task_name, task_file)
            if spec is None or spec.loader is None:
                error_msg = f"无法加载任务模块: {task_file}"
                logger.error(error_msg)
                return False, error_msg

            task_module = importlib.util.module_from_spec(spec)

            # 将logger和G对象注入到模块中
            task_module.logger = logger
            task_module.G = G

            # 执行模块
            spec.loader.exec_module(task_module)

            # 调用main函数（如果存在）
            if hasattr(task_module, 'main'):
                logger.step("执行主函数")
                task_module.main()
                logger.success("任务执行完成")
                return True, ""
            else:
                logger.warning("任务模块没有main函数，仅执行了模块代码")
                return True, ""

        except Exception as e:
            error_msg = f"任务执行失败: {e}"
            logger.error(error_msg)
            import traceback
            logger.debug(f"详细错误信息: {traceback.format_exc()}")
            return False, str(e)
    
    def validate_task_config(self, config: Dict[str, Any]) -> bool:
        """验证任务配置的有效性"""
        if not isinstance(config, dict):
            return False
        
        # 检查必要的配置项
        task_info = config.get('task_info', {})
        if not isinstance(task_info, dict):
            return False
        
        # 检查依赖格式
        dependencies = task_info.get('dependencies', [])
        if not isinstance(dependencies, list):
            return False
        
        return True
