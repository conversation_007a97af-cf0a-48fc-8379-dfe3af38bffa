"""
企业微信通知模块
"""
import requests
from datetime import datetime
from typing import Dict, Any
from .config_manager import G


def send_wecom_notification(webhook_url: str, content: str) -> Dict:
    """
    发送企业微信群机器人文本通知
    简化只支持纯文本的内容发送

    Args:
        webhook_url: 企业微信群机器人的完整webhook地址(包含key)
        content: 消息内容

    Returns:
        Dict: 接口返回结果
    """
    try:
        # 构造请求
        response = requests.post(
            webhook_url,
            json={
                "msgtype": "text",
                "text": {
                    "content": content
                }
            },
            headers={"Content-Type": "application/json"},
            timeout=5
        )

        # 检查响应
        response.raise_for_status()
        result = response.json()

        if result.get("errcode") != 0:
            raise Exception(result.get("errmsg", "未知错误"))

        return result

    except Exception as e:
        return {
            "errcode": -1,
            "errmsg": f"发送企业微信通知失败: {str(e)}"
        }


class WeChatNotifier:
    """企业微信通知器"""

    def __init__(self, task_name: str = None):
        self.task_name = task_name
        self.config = self._load_notification_config()

    def _load_notification_config(self) -> Dict[str, Any]:
        """加载通知配置"""
        # 默认配置
        default_config = {
            'enabled': False,
            'webhook_url': '',
            'notify_on_success': True,
            'notify_on_failure': True
        }

        # 获取全局配置
        global_config = {}
        try:
            # 确保全局配置已加载
            if not hasattr(G, 'notification'):
                G.load_global_config()

            if hasattr(G, 'notification'):
                # 处理ConfigNode对象
                notification_config = G.notification
                if hasattr(notification_config, 'to_dict'):
                    global_config = notification_config.to_dict()
                else:
                    # 如果是ConfigNode，手动转换
                    global_config = {
                        'enabled': getattr(notification_config, 'enabled', False),
                        'webhook_url': getattr(notification_config, 'webhook_url', ''),
                        'notify_on_success': getattr(notification_config, 'notify_on_success', True),
                        'notify_on_failure': getattr(notification_config, 'notify_on_failure', True)
                    }
        except Exception as e:
            print(f"📱 加载通知配置失败: {e}")
            pass

        # 获取任务级配置
        task_config = {}
        try:
            if self.task_name and hasattr(G, 'task'):
                task_notification = G.task.get('notification', {})
                if hasattr(task_notification, 'to_dict'):
                    task_config = task_notification.to_dict()
                elif isinstance(task_notification, dict):
                    task_config = task_notification
                else:
                    # 如果是ConfigNode，手动转换
                    task_config = {
                        'enabled': getattr(task_notification, 'enabled', None),
                        'webhook_url': getattr(task_notification, 'webhook_url', None),
                        'notify_on_success': getattr(task_notification, 'notify_on_success', None),
                        'notify_on_failure': getattr(task_notification, 'notify_on_failure', None)
                    }
                    # 移除None值
                    task_config = {k: v for k, v in task_config.items() if v is not None}
        except Exception as e:
            print(f"📱 加载任务通知配置失败: {e}")
            pass

        # 合并配置：默认 < 全局 < 任务级
        config = {**default_config, **global_config, **task_config}

        return config
    
    def is_enabled(self) -> bool:
        """检查通知是否启用"""
        return self.config.get('enabled', False) and bool(self.config.get('webhook_url'))
    
    def should_notify_success(self) -> bool:
        """检查是否应该通知成功"""
        return self.config.get('notify_on_success', True)
    
    def should_notify_failure(self) -> bool:
        """检查是否应该通知失败"""
        return self.config.get('notify_on_failure', True)
    
    def send_success_notification(self, task_name: str, duration: str, details: str = None):
        """发送成功通知"""
        if not self.is_enabled() or not self.should_notify_success():
            return
        
        message = self._build_success_message(task_name, duration, details)
        self._send_message(message)
    
    def send_failure_notification(self, task_name: str, error: str, duration: str = None):
        """发送失败通知"""
        if not self.is_enabled() or not self.should_notify_failure():
            return
        
        message = self._build_failure_message(task_name, error, duration)
        self._send_message(message)
    
    def _build_success_message(self, task_name: str, duration: str, details: str = None) -> str:
        """构建成功消息"""
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        message = f"""✅ 任务执行成功

📋 任务名称: {task_name}
⏱️ 执行时间: {duration}
🕐 完成时间: {current_time}"""

        if details:
            message += f"\n📝 详细信息: {details}"

        return message

    def _build_failure_message(self, task_name: str, error: str, duration: str = None) -> str:
        """构建失败消息"""
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        message = f"""❌ 任务执行失败

📋 任务名称: {task_name}
🕐 失败时间: {current_time}"""

        if duration:
            message += f"\n⏱️ 运行时间: {duration}"

        message += f"\n🚨 错误信息: {error}"

        return message
    
    def _send_message(self, message: str):
        """发送消息到企业微信"""
        webhook_url = self.config.get('webhook_url')
        if not webhook_url:
            return

        try:
            result = send_wecom_notification(webhook_url, message)

            if result.get('errcode') == 0:
                print(f"📱 企业微信通知发送成功")
            else:
                print(f"📱 企业微信通知发送失败: {result.get('errmsg', '未知错误')}")

        except Exception as e:
            print(f"📱 企业微信通知发送错误: {e}")
    
def notify_task_success(task_name: str, duration: str, details: str = None):
    """通知任务成功"""
    try:
        notifier = WeChatNotifier(task_name)
        notifier.send_success_notification(task_name, duration, details)
    except Exception as e:
        print(f"📱 发送成功通知失败: {e}")

def notify_task_failure(task_name: str, error: str, duration: str = None):
    """通知任务失败"""
    try:
        notifier = WeChatNotifier(task_name)
        notifier.send_failure_notification(task_name, error, duration)
    except Exception as e:
        print(f"📱 发送失败通知失败: {e}")
