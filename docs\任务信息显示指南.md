# 📊 任务信息显示指南

## 🎯 概述

框架提供了智能的任务状态栏显示系统，在每个任务执行时会显示紧凑的任务信息，包括：
- 任务名称和描述
- 定时器设置
- 开始时间和执行时间
- 带任务标识的日志输出

## 🖥️ 显示效果

### 任务状态栏
```
────────────────────────────────────────────────────────────────────────────────
🚀 演示任务 [demo_task] ⏰ 手动执行 🕐 11:06:29
────────────────────────────────────────────────────────────────────────────────
```

### 任务日志（带标识）
```
2025-08-25 10:58:51 | [   demo_task] | INFO     | 🚀 任务开始执行
2025-08-25 10:58:51 | [   demo_task] | STEP     | 🔄 [开始演示任务] 
2025-08-25 10:58:51 | [   demo_task] | INFO     | 📋 读取配置信息
2025-08-25 10:58:51 | [   demo_task] | SUCCESS  | ✅ 处理完成
```

### 任务完成状态栏
```
────────────────────────────────────────────────────────────────────────────────
✅ 演示任务 完成 ⏱️ 0:00:06 🏁 11:06:36
────────────────────────────────────────────────────────────────────────────────
```

## 🏷️ 任务标识功能

### 日志格式
每条日志都包含任务标识，格式为：
```
时间戳 | [任务名称] | 日志级别 | 消息内容
```

### 多任务区分
当你同时运行多个终端时，每个终端的日志都有清晰的任务标识：

**终端1 - demo_task**:
```
2025-08-25 10:58:51 | [   demo_task] | INFO     | 处理数据中...
```

**终端2 - config_test**:
```
2025-08-25 10:58:52 | [config_test] | INFO     | 配置验证中...
```

**终端3 - file_processor**:
```
2025-08-25 10:58:53 | [file_processor] | INFO     | 文件处理中...
```

## 📋 状态栏详解

### 显示内容
1. **🚀 任务显示名**: 来自配置文件的友好名称
2. **[任务名称]**: 实际的任务文件名，用于日志标识
3. **⏰ 定时设置**:
   - 如果配置了定时：显示cron表达式
   - 如果没有定时：显示"手动执行"
4. **🕐 开始时间**: 任务启动的精确时间（时:分:秒）
5. **⏱️ 总耗时**: 任务执行的总时间（结束时显示）

### 配置来源
信息面板从任务配置文件中读取信息：

```yaml
# config/tasks/demo_task.yaml
task_info:
  name: "演示任务"                    # 显示名称
  description: "展示框架基本功能"      # 任务描述

schedule:
  enabled: true                      # 是否启用定时
  cron: "0 */2 * * *"               # 定时表达式
```

## 🚀 多终端使用场景

### 场景1: 并发数据处理
```bash
# 终端1
python main.py
选择: 1 (数据清理任务)

# 终端2  
python main.py
选择: 2 (数据验证任务)

# 终端3
python main.py
选择: 3 (数据转换任务)
```

每个终端都会显示：
- 独立的任务信息面板
- 带任务标识的日志输出
- 独立的执行时间统计

### 场景2: 开发调试
```bash
# 主任务终端
python main.py
选择: 1 (主业务任务)

# 监控任务终端
python main.py  
选择: 2 (监控任务)

# 测试任务终端
python main.py
选择: 3 (测试任务)
```

### 场景3: 定时任务管理
```bash
# 备份任务终端
python main.py
选择: 1 (备份任务) - 显示"每天02:00执行"

# 清理任务终端
python main.py
选择: 2 (清理任务) - 显示"每周日执行"

# 报告任务终端
python main.py
选择: 3 (报告任务) - 显示"每月1号执行"
```

## 🎨 视觉特点

### 颜色方案
- **状态栏边框**: 青色分隔线
- **任务名称**: 白色加粗
- **任务标识**: 黄色 [方括号]
- **定时信息**: 绿色（启用）/ 灰色（禁用）
- **时间信息**: 蓝色
- **完成状态**: 绿色

### 布局特点
- **紧凑设计**: 最小化占用终端空间
- **信息密集**: 一行显示关键信息
- **清晰分隔**: 分隔线区分状态栏和日志
- **标准宽度**: 80字符宽度，适配标准终端

## 🔧 技术实现

### 自动集成
任务状态栏已自动集成到任务管理器中，无需手动调用：

```python
# 在任务中无需任何额外代码
from core import G, logger

def main():
    logger.info("任务开始")  # 自动显示任务标识
    # 业务逻辑...
    logger.success("任务完成")
```

### 配置驱动
面板信息完全由配置文件驱动：

```yaml
task_info:
  name: "自定义任务名"
  description: "自定义描述"
  
schedule:
  enabled: true
  cron: "0 9 * * 1-5"  # 工作日上午9点
```

## 📊 实际使用效果

### 单任务模式
```bash
python main.py
```
- 选择任务后显示紧凑的状态栏
- 执行过程中显示带标识的日志
- 结束时显示执行统计

### 多任务模式
```bash
# 同时打开多个终端，每个运行不同任务
# 每个终端都有独立的状态栏显示
# 通过任务标识清晰区分不同任务的日志
```

## 💡 使用建议

1. **任务命名**: 使用有意义的任务名，便于在多终端中识别
2. **描述完善**: 在配置文件中添加清晰的任务描述
3. **定时配置**: 合理配置定时信息，便于了解任务执行计划
4. **终端管理**: 使用终端标签或窗口标题来进一步区分任务
5. **日志查看**: 利用任务标识快速定位特定任务的日志

## 🎯 优势总结

1. **紧凑高效**: 最小化占用终端空间，不干扰日志查看
2. **信息完整**: 显示任务的关键信息和执行状态
3. **多任务友好**: 支持同时运行多个任务而不混乱
4. **零配置**: 自动集成，无需额外设置
5. **跨平台兼容**: Windows和Unix/Linux都有良好的显示效果

现在你可以放心地同时运行多个任务，每个终端都会清晰地显示当前任务的信息，而且不会被日志输出挤掉！ 🚀
