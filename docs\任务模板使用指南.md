# 📋 任务模板使用指南

## 🎯 快速开始

### 方式1: 使用创建脚本（推荐）

```bash
# 创建新任务
python create_task.py my_new_task

# 示例
python create_task.py data_processor
python create_task.py email_sender
python create_task.py report_generator
```

### 方式2: 手动创建

1. **复制模板文件**
   ```bash
   cp templates/task_template.py tasks/my_task.py
   cp templates/task_config_template.yaml config/tasks/my_task.yaml
   ```

2. **编辑任务文件** (`tasks/my_task.py`)
3. **配置任务设置** (`config/tasks/my_task.yaml`)

## 📁 模板文件说明

### 🐍 任务模板 (`templates/task_template.py`)

**核心结构**:
```python
def main():
    """任务主函数 - 必需"""
    logger.step("开始执行任务")
    
    # 1. 读取配置
    task_name = G.settings.task_name
    
    # 2. 执行业务逻辑
    process_data()
    
    # 3. 清理资源
    cleanup_resources()
    
    logger.success("任务执行完成")

def process_data():
    """业务逻辑函数 - 自定义"""
    pass

def cleanup_resources():
    """资源清理函数 - 可选"""
    pass
```

**关键特性**:
- ✅ 自动配置和日志初始化
- ✅ 标准化错误处理
- ✅ 配置读取示例
- ✅ 日志记录最佳实践

### ⚙️ 配置模板 (`templates/task_config_template.yaml`)

**完整配置段**:
```yaml
# 任务信息（必需）
task_info:
  name: "任务显示名"
  description: "任务描述"
  dependencies: ["requests"]

# 定时配置（可选）
schedule:
  enabled: false
  cron: "0 9 * * *"

# 通知配置（可选）
notification:
  enabled: true
  notify_on_success: true
  notify_on_failure: true

# 任务设置（自定义）
settings:
  task_name: "my_task"
  batch_size: 100
  # 更多自定义配置...
```

## 🔧 配置使用方法

### 基础配置访问

```python
# 读取settings段
task_name = G.settings.task_name
batch_size = G.settings.batch_size
timeout = G.settings.timeout

# 使用默认值
log_level = G.settings.get('log_level', 'INFO')
max_retry = G.settings.get('max_retry', 3)
```

### 全局配置访问

```python
# 访问全局数据库配置
db_host = G.database.host
db_port = G.database.port

# 访问全局API配置
api_timeout = G.api.timeout
api_base_url = G.api.base_url
```

### 自定义配置段

```yaml
# 在配置文件中
custom:
  feature_flags:
    enable_cache: true
  external_apis:
    service_a: "https://api-a.com"
```

```python
# 在代码中访问
enable_cache = G.custom.feature_flags.enable_cache
api_url = G.custom.external_apis.service_a
```

## 📊 实际示例

### 示例1: 数据处理任务

**任务文件** (`tasks/data_processor.py`):
```python
def main():
    logger.step("开始数据处理")
    
    # 读取配置
    input_file = G.settings.input_file
    output_file = G.settings.output_file
    batch_size = G.settings.batch_size
    
    # 处理数据
    process_file(input_file, output_file, batch_size)
    
    logger.success("数据处理完成")

def process_file(input_file, output_file, batch_size):
    logger.info(f"处理文件: {input_file} -> {output_file}")
    # 实际处理逻辑...
```

**配置文件** (`config/tasks/data_processor.yaml`):
```yaml
task_info:
  name: "数据处理器"
  description: "批量处理CSV数据文件"
  dependencies: ["pandas"]

settings:
  task_name: "data_processor"
  input_file: "./data/input.csv"
  output_file: "./data/output.csv"
  batch_size: 1000
```

### 示例2: API调用任务

**任务文件** (`tasks/api_caller.py`):
```python
import requests

def main():
    logger.step("开始API调用")
    
    # 读取配置
    api_url = G.settings.api_url
    api_key = G.settings.api_key
    max_retry = G.settings.get('max_retry', 3)
    
    # 调用API
    result = call_api(api_url, api_key, max_retry)
    
    # 保存结果
    save_result(result)
    
    logger.success("API调用完成")

def call_api(url, key, max_retry):
    headers = {'Authorization': f'Bearer {key}'}
    
    for attempt in range(max_retry):
        try:
            response = requests.get(url, headers=headers)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.warning(f"API调用失败 (尝试 {attempt+1}/{max_retry}): {e}")
            if attempt == max_retry - 1:
                raise
```

**配置文件** (`config/tasks/api_caller.yaml`):
```yaml
task_info:
  name: "API调用器"
  description: "定期调用外部API获取数据"
  dependencies: ["requests"]

schedule:
  enabled: true
  cron: "0 */2 * * *"  # 每2小时执行

settings:
  task_name: "api_caller"
  api_url: "https://api.example.com/data"
  api_key: "your-api-key"
  max_retry: 3
  output_dir: "./api_data"
```

## 🚀 最佳实践

### 1. 任务结构

```python
def main():
    """保持main函数简洁，专注于流程控制"""
    logger.step("任务开始")
    
    # 验证配置
    validate_config()
    
    # 执行核心逻辑
    result = execute_business_logic()
    
    # 处理结果
    handle_result(result)
    
    logger.success("任务完成")

def validate_config():
    """独立的配置验证函数"""
    required = ['task_name', 'input_dir']
    for key in required:
        if not hasattr(G.settings, key):
            raise ValueError(f"缺少配置: {key}")

def execute_business_logic():
    """核心业务逻辑"""
    # 具体实现...
    pass

def handle_result(result):
    """结果处理"""
    # 保存、发送、清理等...
    pass
```

### 2. 错误处理

```python
def main():
    try:
        # 主要逻辑
        process_data()
    except FileNotFoundError as e:
        logger.error(f"文件未找到: {e}")
        raise
    except requests.RequestException as e:
        logger.error(f"网络请求失败: {e}")
        raise
    except Exception as e:
        logger.error(f"未知错误: {e}")
        raise
```

### 3. 配置管理

```yaml
# 使用有意义的配置分组
settings:
  # 基础设置
  task_name: "my_task"
  debug_mode: false
  
  # 文件设置
  files:
    input_dir: "./input"
    output_dir: "./output"
    backup_dir: "./backup"
  
  # 处理设置
  processing:
    batch_size: 100
    max_workers: 4
    timeout: 30
  
  # 通知设置
  alerts:
    email_on_error: true
    slack_webhook: "https://hooks.slack.com/..."
```

### 4. 日志记录

```python
def process_batch(batch_data):
    logger.info(f"开始处理批次，大小: {len(batch_data)}")
    
    success_count = 0
    error_count = 0
    
    for item in batch_data:
        try:
            process_item(item)
            success_count += 1
        except Exception as e:
            logger.warning(f"处理项目失败: {item}, 错误: {e}")
            error_count += 1
    
    logger.info(f"批次处理完成: 成功 {success_count}, 失败 {error_count}")
```

## 🎯 常见任务类型

### 数据处理任务
- CSV/Excel文件处理
- 数据清洗和转换
- 批量数据导入/导出

### API集成任务
- 第三方API调用
- 数据同步
- Webhook处理

### 文件操作任务
- 文件备份和归档
- 批量文件处理
- 日志清理

### 报告生成任务
- 定期报告生成
- 数据统计分析
- 邮件发送

### 监控任务
- 系统状态检查
- 服务健康监控
- 告警处理

## 💡 提示

1. **命名规范**: 使用下划线分隔的小写字母
2. **配置验证**: 在任务开始时验证必需配置
3. **错误处理**: 提供清晰的错误信息
4. **日志记录**: 记录关键步骤和结果
5. **资源清理**: 确保正确释放资源
6. **测试优先**: 先用小数据集测试

现在你可以快速创建标准化的任务了！🚀
