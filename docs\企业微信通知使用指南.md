# 📱 企业微信通知使用指南

## 🎯 功能概述

企业微信通知功能可以在任务执行完成（成功或失败）时自动发送通知到企业微信群，帮助你及时了解任务执行状态。

## 🔧 配置说明

### 📋 全局配置

编辑 `config/notification.yaml` 文件：

```yaml
# 企业微信通知全局配置
notification:
  # 是否启用通知
  enabled: false
  
  # 企业微信机器人webhook地址
  webhook_url: "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=你的机器人key"
  
  # 成功时是否通知
  notify_on_success: true
  
  # 失败时是否通知
  notify_on_failure: true
```

### 🎯 任务级配置

在任务配置文件中添加 `notification` 部分（可选）：

```yaml
task_info:
  name: "演示任务"
  description: "任务描述"

# 任务级通知配置（会覆盖全局配置）
notification:
  enabled: true
  notify_on_success: true
  notify_on_failure: true
  # webhook_url: "自定义的webhook地址"  # 可选，不设置则使用全局配置

settings:
  # 其他任务设置...
```

## 🤖 企业微信机器人设置

### 1. 创建群机器人

1. 在企业微信群中，点击右上角 `...` → `群机器人`
2. 点击 `添加机器人`
3. 选择 `自定义机器人`
4. 设置机器人名称和头像
5. 复制生成的 `Webhook地址`

### 2. 配置Webhook地址

将复制的Webhook地址填入配置文件的 `webhook_url` 字段：

```yaml
webhook_url: "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=a6e8f4c3-1ba7-4370-b2c2-8977d96e2e70"
```

## 📨 通知消息格式

### ✅ 成功通知

```
✅ 任务执行成功

📋 任务名称: demo_task
⏱️ 执行时间: 0:00:06
🕐 完成时间: 2025-08-25 13:30:45
```

### ❌ 失败通知

```
❌ 任务执行失败

📋 任务名称: demo_task
🕐 失败时间: 2025-08-25 13:30:45
⏱️ 运行时间: 0:00:03
🚨 错误信息: 依赖安装失败
```

## 🎮 配置优先级

配置的优先级从低到高：

1. **默认配置** - 系统内置默认值
2. **全局配置** - `config/notification.yaml`
3. **任务配置** - 任务配置文件中的 `notification` 部分

任务级配置会覆盖全局配置，实现灵活的通知控制。

## 🧪 测试通知功能

### 使用测试工具

```bash
python test_notification.py
```

这个工具会：
1. 加载配置
2. 发送测试通知
3. 发送成功通知示例
4. 发送失败通知示例

### 手动测试

```python
from core.wechat_notifier import test_notification

# 测试通知功能
test_notification()
```

## 📊 使用场景

### 场景1: 全局启用，所有任务都通知

```yaml
# config/notification.yaml
notification:
  enabled: true
  webhook_url: "你的webhook地址"
  notify_on_success: true
  notify_on_failure: true
```

### 场景2: 只通知失败，不通知成功

```yaml
# config/notification.yaml
notification:
  enabled: true
  webhook_url: "你的webhook地址"
  notify_on_success: false
  notify_on_failure: true
```

### 场景3: 特定任务使用不同配置

```yaml
# 全局配置：只通知失败
notification:
  enabled: true
  notify_on_success: false
  notify_on_failure: true

# 重要任务配置：成功失败都通知
notification:
  enabled: true
  notify_on_success: true
  notify_on_failure: true
```

### 场景4: 不同任务组使用不同机器人

```yaml
# 任务A配置
notification:
  enabled: true
  webhook_url: "机器人A的webhook地址"

# 任务B配置  
notification:
  enabled: true
  webhook_url: "机器人B的webhook地址"
```

## ⚙️ 高级配置

### 配置参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `enabled` | boolean | false | 是否启用通知 |
| `webhook_url` | string | "" | 企业微信机器人webhook地址 |
| `notify_on_success` | boolean | true | 成功时是否通知 |
| `notify_on_failure` | boolean | true | 失败时是否通知 |

### 通知触发时机

1. **任务成功完成** - 任务正常执行完毕
2. **依赖安装失败** - 任务依赖检查或安装失败
3. **任务执行失败** - 任务代码执行过程中出错
4. **任务执行异常** - 任务执行过程中发生未捕获异常

## 🔍 故障排除

### 通知未发送

1. **检查配置**
   ```bash
   # 确认enabled为true
   # 确认webhook_url正确
   ```

2. **检查网络**
   ```bash
   # 确认能访问企业微信API
   ping qyapi.weixin.qq.com
   ```

3. **检查机器人**
   ```bash
   # 确认机器人未被删除
   # 确认webhook地址有效
   ```

### 通知发送失败

查看控制台输出的错误信息：

```
📱 企业微信通知发送失败: invalid webhook url
📱 企业微信通知发送失败: robot not exist
📱 企业微信通知发送超时
```

### 调试模式

在代码中添加调试信息：

```python
from core.wechat_notifier import get_notifier

# 获取通知器并检查配置
notifier = get_notifier("task_name")
print(f"通知启用: {notifier.is_enabled()}")
print(f"成功通知: {notifier.should_notify_success()}")
print(f"失败通知: {notifier.should_notify_failure()}")
print(f"配置: {notifier.config}")
```

## 💡 最佳实践

1. **测试优先**: 配置完成后先使用测试工具验证
2. **分级通知**: 重要任务成功失败都通知，普通任务只通知失败
3. **分组管理**: 不同类型任务使用不同的企业微信群
4. **错误处理**: 通知发送失败不影响任务执行
5. **消息简洁**: 通知消息包含关键信息，避免过长

## 🎉 完整示例

### 1. 配置全局通知

```yaml
# config/notification.yaml
notification:
  enabled: true
  webhook_url: "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=你的key"
  notify_on_success: false  # 全局默认不通知成功
  notify_on_failure: true   # 全局默认通知失败
```

### 2. 配置重要任务

```yaml
# config/tasks/important_task.yaml
task_info:
  name: "重要任务"
  description: "关键业务任务"

notification:
  enabled: true
  notify_on_success: true   # 重要任务成功也要通知
  notify_on_failure: true

settings:
  # 任务设置...
```

### 3. 运行任务

```bash
python main.py
# 选择任务执行，自动发送通知
```

现在你的多任务终端执行器具备了完整的企业微信通知功能！🚀
