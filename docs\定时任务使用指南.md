# ⏰ 定时任务使用指南

## 🎯 功能概述

定时任务功能允许你按照指定的时间规则自动执行任务，支持cron表达式配置，完全集成到主程序中。

## 🔧 配置定时任务

### 📋 在任务配置文件中添加定时配置

编辑任务配置文件 `config/tasks/任务名.yaml`：

```yaml
task_info:
  name: "演示任务"
  description: "任务描述"
  dependencies:
    - requests

# 定时任务配置
schedule:
  enabled: true                # 是否启用定时
  cron: "*/2 * * * *"          # cron表达式

settings:
  # 其他任务设置...
```

### 🕐 Cron表达式格式

格式：`分钟 小时 日 月 星期`

| 字段 | 范围 | 说明 |
|------|------|------|
| 分钟 | 0-59 | 分钟 |
| 小时 | 0-23 | 小时 |
| 日 | 1-31 | 日期 |
| 月 | 1-12 | 月份 |
| 星期 | 0-6 | 星期 (0=周日, 1=周一, ..., 6=周六) |

### 📝 常用Cron表达式示例

```yaml
# 每分钟执行
cron: "* * * * *"

# 每2分钟执行
cron: "*/2 * * * *"

# 每小时执行
cron: "0 * * * *"

# 每天上午9点执行
cron: "0 9 * * *"

# 每天上午9点和下午5点执行
cron: "0 9,17 * * *"

# 工作日上午9点执行
cron: "0 9 * * 1-5"

# 每周一上午9点执行
cron: "0 9 * * 1"

# 每月1号上午9点执行
cron: "0 9 1 * *"

# 每年1月1号上午9点执行
cron: "0 9 1 1 *"
```

## 🚀 使用方式

### 方式1: 通过main.py启动

1. **运行主程序**
   ```bash
   python main.py
   ```

2. **选择定时任务**
   - 任务列表会显示定时配置信息
   - 选择有定时配置的任务

3. **选择执行方式**
   ```
   请选择执行方式:
   [1] 立即执行
   [2] 按定时执行 (启动调度器)
   [q] 取消
   ```

4. **按定时执行**
   - 选择 `[2]` 启动调度器
   - 调度器会在指定时间自动执行任务
   - 按 `Ctrl+C` 停止调度器

### 方式2: 独立调度器启动

```bash
python scheduler_main.py
```

这会启动独立的定时任务调度器，执行所有启用的定时任务。

## 📊 任务列表显示

在main.py中，定时任务会显示额外信息：

```
📋 可用任务列表:

[1] 📊 配置测试任务 (config_test.py)
    描述: 测试智能配置读取功能

[2] 📊 每日报告任务 (daily_report.py)
    描述: 每天上午9点生成日报
    ⏰ 定时: 0 9 * * * (已启用)
    📅 下次运行: 2025-08-26 09:00:00

[3] 📊 演示任务 (demo_task.py)
    描述: 这是一个演示任务，展示框架的基本功能
    依赖: requests
    ⏰ 定时: */2 * * * * (已启用)
    📅 下次运行: 2025-08-25 13:34:00
```

## 🎮 执行模式对比

### 立即执行模式

- ✅ 忽略定时配置，立即运行任务
- ✅ 执行完成后程序结束
- ✅ 适合测试和一次性执行

### 定时执行模式

- ✅ 启动调度器，按定时规则执行
- ✅ 持续运行，等待定时触发
- ✅ 支持多个定时任务同时调度
- ✅ 适合生产环境自动化

## 📱 通知集成

定时任务完全集成企业微信通知功能：

- ✅ **任务成功** - 自动发送成功通知
- ✅ **任务失败** - 自动发送失败通知
- ✅ **执行时间** - 通知中包含执行耗时
- ✅ **定时标识** - 通知中标明是定时执行

## 🔍 日志记录

定时任务的完整日志记录：

```
13:32:32 | INFO     | ⏰ 定时执行任务: demo_task
13:32:32 | INFO     | 🚀 启动任务: demo_task
2025-08-25 13:32:32 | INFO     | 🚀 任务 [demo_task] 开始执行
...
13:32:39 | INFO     | ✅ 定时任务 demo_task 执行成功
```

## 📋 完整示例

### 1. 创建定时任务配置

```yaml
# config/tasks/backup_task.yaml
task_info:
  name: "数据备份任务"
  description: "每天凌晨2点执行数据备份"
  dependencies: []

# 定时配置 - 每天凌晨2点
schedule:
  enabled: true
  cron: "0 2 * * *"

# 通知配置
notification:
  enabled: true
  notify_on_success: true
  notify_on_failure: true

settings:
  backup_dir: "./backups"
  retention_days: 7
```

### 2. 创建任务脚本

```python
# tasks/backup_task.py
import sys
from pathlib import Path
from datetime import datetime

sys.path.insert(0, str(Path(__file__).parent.parent))
from core import G, logger

def main():
    """数据备份主函数"""
    logger.step("开始数据备份")
    
    backup_dir = G.settings.backup_dir
    retention_days = G.settings.retention_days
    
    logger.info(f"备份目录: {backup_dir}")
    logger.info(f"保留天数: {retention_days}")
    
    # 备份逻辑...
    logger.success("数据备份完成")

if __name__ == "__main__":
    main()
```

### 3. 运行定时任务

```bash
# 方式1: 通过main.py选择
python main.py

# 方式2: 独立调度器
python scheduler_main.py
```

## ⚙️ 高级配置

### 多任务定时调度

可以同时配置多个定时任务：

```yaml
# 任务A: 每小时执行
schedule:
  enabled: true
  cron: "0 * * * *"

# 任务B: 每天执行
schedule:
  enabled: true
  cron: "0 9 * * *"

# 任务C: 每周执行
schedule:
  enabled: true
  cron: "0 9 * * 1"
```

### 条件启用

```yaml
# 开发环境关闭，生产环境启用
schedule:
  enabled: false  # 开发时设为false
  cron: "0 2 * * *"
```

## 🔍 故障排除

### 定时任务不执行

1. **检查配置**
   ```yaml
   schedule:
     enabled: true  # 确保为true
     cron: "0 9 * * *"  # 检查表达式格式
   ```

2. **检查cron表达式**
   - 使用在线cron表达式验证工具
   - 确保格式为 `分钟 小时 日 月 星期`

3. **查看调度器日志**
   ```
   13:32:32 | INFO     | 📅 加载定时任务: task_name (cron_expr)
   ```

### 任务执行失败

1. **查看任务日志**
   - 检查 `logs/任务名/` 目录下的日志文件

2. **检查依赖**
   - 确保任务依赖的包已安装

3. **检查权限**
   - 确保任务有足够的文件系统权限

## 💡 最佳实践

1. **测试优先**
   - 新建定时任务先用短间隔测试（如每分钟）
   - 确认正常后再改为实际间隔

2. **合理间隔**
   - 避免过于频繁的定时任务
   - 考虑任务执行时间，避免重叠

3. **错误处理**
   - 任务中添加适当的异常处理
   - 利用通知功能及时发现问题

4. **资源管理**
   - 长时间运行的定时任务注意内存使用
   - 定期清理临时文件和日志

5. **监控告警**
   - 启用企业微信通知
   - 关键任务失败时及时处理

现在你的多任务终端执行器具备了完整的定时任务功能！🚀
