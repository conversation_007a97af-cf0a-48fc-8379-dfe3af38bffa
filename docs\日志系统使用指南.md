# 📊 日志系统使用指南

## 🎯 概述

本框架提供了智能的日志系统，支持彩色输出、任务隔离、上下文信息记录等功能。

## 🚀 快速开始

### 基础导入
```python
# 智能导入 - 自动初始化
from core import logger

# 或者使用便捷函数
from core import log_info, log_error, log_success, log_step
```

## 📋 日志级别和使用场景

### 🔍 DEBUG - 调试信息
**颜色**: 灰色 | **图标**: 无 | **级别**: 10

**使用场景**:
- 详细的程序执行流程
- 变量值的输出
- 调试信息
- 只写入文件，不显示在控制台

```python
logger.debug("变量值检查", count=100, status="processing")
logger.debug("API响应详情", url="https://api.example.com", code=200)
```

### ℹ️ INFO - 一般信息
**颜色**: 默认颜色 | **图标**: 无 | **级别**: 20

**使用场景**:
- 程序正常运行的信息
- 配置信息显示
- 处理进度报告
- 一般性状态更新

```python
logger.info("开始处理数据", count=1000, file="data.xlsx")
logger.info("配置加载完成", status="success")
logger.info("处理进度", progress="50/100", duration="2.5s")
```

### ⚠️ WARNING - 警告信息
**颜色**: 黄色 | **图标**: ⚠️ | **级别**: 30

**使用场景**:
- 可能的问题提醒
- 非致命错误
- 配置问题警告
- 性能问题提示

```python
logger.warning("文件大小超出建议值", file="large.xlsx", size="50MB")
logger.warning("API响应较慢", url="https://slow-api.com", duration="5.2s")
logger.warning("内存使用率较高", progress="85%", status="monitoring")
```

### ❌ ERROR - 错误信息
**颜色**: 红色 | **图标**: ❌ | **级别**: 40

**使用场景**:
- 操作失败
- 异常捕获
- 文件处理错误
- 网络请求失败

```python
logger.error("文件读取失败", file="missing.txt", code="FileNotFound")
logger.error("API请求失败", url="https://api.example.com", code=500, duration="30s")
logger.error("数据处理异常", count=0, status="failed")
```

### 🔥 CRITICAL - 严重错误
**颜色**: 红底白字 | **图标**: 无 | **级别**: 50

**使用场景**:
- 系统级错误
- 程序崩溃
- 数据丢失
- 安全问题

```python
logger.critical("数据库连接失败", status="critical", duration="timeout")
logger.critical("系统内存不足", status="emergency")
```

### ✅ SUCCESS - 成功信息
**颜色**: 亮绿色 | **图标**: ✅ | **级别**: 25

**使用场景**:
- 任务完成
- 操作成功
- 目标达成
- 重要里程碑

```python
logger.success("数据处理完成", count=1000, duration="3.2s", file="output.xlsx")
logger.success("文件上传成功", size="2.5MB", url="https://storage.com/file.zip")
logger.success("任务执行完成", progress="100%", status="completed")
```

### 🔄 STEP - 步骤信息
**颜色**: 亮青色 | **图标**: 🔄 | **级别**: 22

**使用场景**:
- 流程步骤标记
- 阶段性进展
- 重要节点
- 执行流程追踪

```python
logger.step("数据预处理", "清理无效数据", count=50, progress="1/5")
logger.step("文件上传", "正在上传到服务器", size="10MB", progress="uploading")
logger.step("API调用", "获取用户信息", user="admin", status="requesting")
```

## 🏷️ 上下文参数详解

### 📁 文件相关
```python
file="data.xlsx"        # 文件名
size="2.5MB"           # 文件大小
```

### 📊 数量和进度
```python
count=100              # 数量/计数
progress="50/100"      # 进度信息
progress="75%"         # 百分比进度
```

### ⏱️ 时间相关
```python
duration="2.5s"        # 耗时
date="2024-01-15"      # 日期
```

### 🌐 网络相关
```python
url="https://api.example.com"  # 网址
code=200               # 状态码/错误码
status="success"       # 状态描述
```

### 👤 用户相关
```python
user="admin"           # 用户名
script="backup.py"     # 脚本名
```

## 🎨 使用方式对比

### 方式1: 智能Logger对象 (推荐)
```python
from core import logger

logger.info("处理开始", count=100, file="data.csv")
logger.step("数据验证", "检查数据格式", progress="1/3")
logger.success("处理完成", duration="5.2s", count=100)
```

### 方式2: 便捷函数
```python
from core import log_info, log_step, log_success

log_info("处理开始", count=100, file="data.csv")
log_step("数据验证", "检查数据格式", progress="1/3")
log_success("处理完成", duration="5.2s", count=100)
```

### 方式3: 装饰器 (函数级别)
```python
from core import log_function

@log_function("数据处理")
def process_data(file_path):
    # 函数执行会自动记录开始和结束
    return result

# 自动输出:
# 🔄 [数据处理] 开始执行
# ✅ 数据处理 执行成功 | 耗时:2.5s
```

## 📝 最佳实践

### 1. 任务开始和结束
```python
def main():
    logger.step("任务初始化", "加载配置文件")
    
    # 任务逻辑...
    
    logger.success("任务完成", duration="总耗时", count="处理数量")
```

### 2. 循环处理
```python
total = len(items)
for i, item in enumerate(items, 1):
    logger.info(f"处理项目: {item}", progress=f"{i}/{total}")
    
    try:
        result = process_item(item)
        logger.debug("处理结果", status="success", count=len(result))
    except Exception as e:
        logger.error(f"处理失败: {e}", file=item, status="failed")
```

### 3. 文件操作
```python
# 文件读取
try:
    with open(file_path, 'r') as f:
        data = f.read()
    logger.info("文件读取成功", file=file_path, size=f"{len(data)} bytes")
except FileNotFoundError:
    logger.error("文件不存在", file=file_path, status="not_found")
```

### 4. API调用
```python
import time
start_time = time.time()

try:
    response = requests.get(api_url, timeout=30)
    duration = f"{time.time() - start_time:.2f}s"
    
    if response.status_code == 200:
        logger.success("API调用成功", url=api_url, code=200, duration=duration)
    else:
        logger.warning("API返回异常", url=api_url, code=response.status_code, duration=duration)
        
except requests.RequestException as e:
    duration = f"{time.time() - start_time:.2f}s"
    logger.error(f"API调用失败: {e}", url=api_url, duration=duration, status="timeout")
```

### 5. 数据处理
```python
logger.step("数据预处理", "开始清理数据", count=len(raw_data))

# 数据清理
cleaned_data = clean_data(raw_data)
logger.info("数据清理完成", count=len(cleaned_data), progress="1/3")

# 数据验证
valid_data = validate_data(cleaned_data)
logger.info("数据验证完成", count=len(valid_data), progress="2/3")

# 数据保存
save_data(valid_data, output_file)
logger.success("数据处理完成", count=len(valid_data), file=output_file, progress="3/3")
```

## 🎯 推荐使用模式

### 简单任务
```python
from core import logger

def simple_task():
    logger.step("开始执行")
    # 业务逻辑
    logger.success("执行完成")
```

### 复杂任务
```python
from core import logger

def complex_task():
    logger.step("任务初始化", "准备执行环境")
    
    try:
        # 步骤1
        logger.step("数据加载", "从文件读取数据", file="input.csv")
        data = load_data()
        logger.info("数据加载完成", count=len(data))
        
        # 步骤2
        logger.step("数据处理", "执行业务逻辑", progress="1/2")
        result = process_data(data)
        logger.info("数据处理完成", count=len(result))
        
        # 步骤3
        logger.step("结果保存", "写入输出文件", progress="2/2")
        save_result(result)
        logger.success("任务完成", count=len(result), file="output.csv")
        
    except Exception as e:
        logger.error(f"任务失败: {e}", status="failed")
        raise
```

## 📂 日志文件

- **位置**: `logs/任务名/任务名_时间戳.log`
- **格式**: UTF-8编码
- **内容**: 包含所有级别的日志（包括DEBUG）
- **时间格式**: 完整时间戳 `2025-08-25 10:46:31`（年-月-日 时:分:秒）

## 🖥️ 控制台输出

- **时间格式**: 完整时间戳 `2025-08-25 10:46:31`（与文件格式一致）
- **颜色支持**: 根据日志级别显示不同颜色
- **级别过滤**: 只显示INFO及以上级别（DEBUG仅写入文件）

## 🔧 高级功能

### 获取日志文件路径
```python
log_file = logger.get_log_file()
print(f"日志保存在: {log_file}")
```

### 自定义上下文
```python
# 可以传入任何自定义的上下文信息
logger.info("自定义信息", 
           custom_field="自定义值",
           another_field=123)
```

记住：好的日志是调试和维护的最佳伙伴！ 🚀
