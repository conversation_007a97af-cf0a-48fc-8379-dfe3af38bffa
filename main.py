"""
多任务终端执行器 - 主启动文件
简单的任务选择和执行界面
"""
import sys
from pathlib import Path
from colorama import Fore, Style, init

# 初始化colorama
init(autoreset=True)

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from core.task_manager import TaskManager
from core.scheduler import CronParser, SingleTaskScheduler
from datetime import datetime, timedelta
import time


class TaskSelector:
    """任务选择器 - 简单的命令行界面"""
    
    def __init__(self):
        self.task_manager = TaskManager()
    
    def start(self):
        """启动任务选择器"""
        self._print_header()
        
        # 1. 扫描可用任务
        tasks = self.task_manager.discover_tasks()
        
        if not tasks:
            print(f"{Fore.RED}❌ 未发现任何任务配置{Style.RESET_ALL}")
            print(f"{Fore.YELLOW}💡 请在 tasks/ 目录下创建 .py 任务文件{Style.RESET_ALL}")
            return
        
        # 2. 显示任务列表
        self._show_task_list(tasks)
        
        # 3. 用户选择
        selected_task = self._get_user_selection(tasks)
        
        if selected_task:
            # 4. 执行选中的任务
            self._execute_task(selected_task)
    
    def _print_header(self):
        """打印程序头部信息"""
        print(f"{Fore.CYAN}🚀 多任务终端执行器{Style.RESET_ALL}")
        print("=" * 32)
    
    def _show_task_list(self, tasks):
        """显示任务列表"""
        print(f"\n{Fore.GREEN}📋 可用任务列表:{Style.RESET_ALL}\n")

        for i, task in enumerate(tasks, 1):
            config = task['config']
            task_info = config.get('task_info', {})
            schedule_config = config.get('schedule', {})

            name = task_info.get('name', task['name'])
            description = task_info.get('description', '无描述')
            dependencies = task_info.get('dependencies', [])

            print(f"{Fore.YELLOW}[{i}]{Style.RESET_ALL} 📊 {name} ({task['file']})")
            print(f"    描述: {description}")

            if dependencies:
                deps_str = ', '.join(dependencies)
                print(f"    依赖: {deps_str}")

            # 显示定时信息
            if schedule_config.get('enabled', False):
                cron_expr = schedule_config.get('cron', '')
                print(f"    ⏰ 定时: {Fore.GREEN}{cron_expr}{Style.RESET_ALL} (已启用)")

                # 计算下次运行时间
                try:
                    cron_dict = CronParser.parse_cron(cron_expr)
                    next_run = self._get_next_run_time(cron_dict)
                    if next_run:
                        print(f"    📅 下次运行: {Fore.BLUE}{next_run}{Style.RESET_ALL}")
                except:
                    pass

            print()

    def _get_next_run_time(self, cron_dict):
        """计算下次运行时间"""
        now = datetime.now()

        # 检查未来24小时内的运行时间
        for minutes in range(1, 24 * 60):
            future_time = now + timedelta(minutes=minutes)
            if CronParser.should_run(cron_dict, future_time):
                return future_time.strftime('%Y-%m-%d %H:%M:%S')

        return "未来24小时内无执行计划"

    def _get_user_selection(self, tasks):
        """获取用户选择"""
        while True:
            try:
                prompt = f"请选择要执行的任务 [1-{len(tasks)}] (输入 q 退出): "
                choice = input(prompt).strip()
                
                if choice.lower() == 'q':
                    print("👋 退出程序")
                    return None
                
                choice_num = int(choice)
                if 1 <= choice_num <= len(tasks):
                    selected = tasks[choice_num - 1]
                    task_name = selected['config'].get('task_info', {}).get('name', selected['name'])
                    print(f"\n{Fore.GREEN}✅ 已选择: {task_name}{Style.RESET_ALL}")
                    return selected
                else:
                    print(f"{Fore.RED}❌ 请输入 1-{len(tasks)} 之间的数字{Style.RESET_ALL}")
                    
            except ValueError:
                print(f"{Fore.RED}❌ 请输入有效的数字{Style.RESET_ALL}")
            except KeyboardInterrupt:
                print(f"\n👋 退出程序")
                return None
    
    def _execute_task(self, task):
        """执行选中的任务"""
        task_name = task['name']
        config = task['config']
        schedule_config = config.get('schedule', {})

        # 检查是否有定时配置
        if schedule_config.get('enabled', False):
            cron_expr = schedule_config.get('cron', '')

            print(f"\n{Fore.YELLOW}⏰ 该任务已配置定时执行: {cron_expr}{Style.RESET_ALL}")

            # 计算下次运行时间
            try:
                cron_dict = CronParser.parse_cron(cron_expr)
                next_run = self._get_next_run_time(cron_dict)
                print(f"{Fore.BLUE}📅 下次运行时间: {next_run}{Style.RESET_ALL}")
            except:
                pass

            # 询问用户选择
            while True:
                try:
                    choice = input(f"\n请选择执行方式:\n"
                                 f"{Fore.YELLOW}[1]{Style.RESET_ALL} 立即执行\n"
                                 f"{Fore.YELLOW}[2]{Style.RESET_ALL} 按定时执行 (启动调度器)\n"
                                 f"{Fore.YELLOW}[q]{Style.RESET_ALL} 取消\n"
                                 f"请选择 [1/2/q]: ").strip()

                    if choice.lower() == 'q':
                        print("👋 取消执行")
                        return
                    elif choice == '1':
                        # 立即执行
                        self._run_task_immediately(task_name)
                        return
                    elif choice == '2':
                        # 启动调度器
                        self._run_task_scheduled(task_name, cron_expr)
                        return
                    else:
                        print(f"{Fore.RED}❌ 请输入 1、2 或 q{Style.RESET_ALL}")

                except KeyboardInterrupt:
                    print(f"\n👋 取消执行")
                    return
        else:
            # 没有定时配置，直接执行
            self._run_task_immediately(task_name)

    def _run_task_immediately(self, task_name):
        """立即执行任务"""
        try:
            print(f"\n{Fore.CYAN}🚀 立即执行任务...{Style.RESET_ALL}")

            # 执行任务
            success = self.task_manager.run_single_task(task_name)

            if success:
                print(f"\n{Fore.GREEN}🎉 任务执行完成！{Style.RESET_ALL}")
            else:
                print(f"\n{Fore.RED}💥 任务执行失败！{Style.RESET_ALL}")
                sys.exit(1)

        except Exception as e:
            print(f"{Fore.RED}❌ 任务执行异常: {e}{Style.RESET_ALL}")
            sys.exit(1)

    def _run_task_scheduled(self, task_name, cron_expr):
        """按定时执行任务"""
        print(f"\n{Fore.CYAN}⏰ 启动单任务定时调度器...{Style.RESET_ALL}")

        try:
            # 创建单任务调度器
            scheduler = SingleTaskScheduler(task_name, cron_expr)

            print(f"{Fore.GREEN}✅ 任务 {task_name} 已加入调度队列{Style.RESET_ALL}")
            print(f"{Fore.BLUE}📅 定时规则: {cron_expr}{Style.RESET_ALL}")

            # 显示下次运行时间
            next_run = scheduler.get_next_run_time()
            if next_run:
                print(f"{Fore.BLUE}⏰ 下次运行: {next_run}{Style.RESET_ALL}")

            print(f"\n{Fore.GREEN}🚀 调度器运行中... (按 Ctrl+C 停止){Style.RESET_ALL}")
            print(f"{Fore.LIGHTBLACK_EX}💡 调度器每分钟检查一次定时任务{Style.RESET_ALL}")

            # 启动调度器
            scheduler.start()

            # 保持运行，等待定时执行
            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                print(f"\n{Fore.YELLOW}🛑 停止调度器...{Style.RESET_ALL}")
                scheduler.stop()
                print(f"{Fore.GREEN}✅ 调度器已停止{Style.RESET_ALL}")

        except Exception as e:
            print(f"{Fore.RED}❌ 调度器启动失败: {e}{Style.RESET_ALL}")
            sys.exit(1)


def main():
    """主函数"""
    try:
        selector = TaskSelector()
        selector.start()
    except KeyboardInterrupt:
        print(f"\n👋 程序被用户中断")
    except Exception as e:
        print(f"{Fore.RED}💥 程序异常: {e}{Style.RESET_ALL}")
        sys.exit(1)


if __name__ == "__main__":
    main()
