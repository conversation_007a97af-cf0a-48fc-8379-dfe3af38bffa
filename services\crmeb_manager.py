#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
CRMEB商城管理器 - Pythonic风格重构版本
功能：提供CRMEB商城平台的标准化操作接口
"""

import sys
import os
import requests
import json
import random
from datetime import datetime
from decimal import Decimal
from typing import Dict, List
from pathlib import Path

sys.path.insert(0, str(Path(__file__).parent.parent))
from core import logger, G

# ==================== 异常类定义 ====================

class CrmebManagerError(Exception):
    """CRMEB管理器异常"""
    pass


class CrmebAuthError(CrmebManagerError):
    """CRMEB认证异常"""
    pass


class CrmebApiError(CrmebManagerError):
    """CRMEB API异常"""
    pass


class CrmebValidationError(CrmebManagerError):
    """CRMEB数据验证异常"""
    pass


# ==================== 基础服务类 ====================

class CrmebApiClient:
    """CRMEB API客户端 - 提供基础HTTP请求服务"""

    # === 1. 类常量 ===
    DEFAULT_TIMEOUT = 10
    TOKEN_BUFFER_TIME = 3600  # token缓冲时间（秒）

    # === 2. 初始化 ===
    def __init__(self, config: Dict[str, str]):
        """初始化API客户端

        Args:
            config: API配置字典，包含main_url、appid、appsecret
        """
        self._main_url = config.get('main_url')
        self._appid = config.get('appid')
        self._appsecret = config.get('appsecret')
        self._outapi_token = None
        self._outapi_token_expire_time = None

        if not all([self._main_url, self._appid, self._appsecret]):
            raise CrmebValidationError("CRMEB配置不完整，缺少main_url、appid或appsecret")

    # === 3. 主要业务方法 ===
    def get_token(self) -> str:
        """获取访问令牌"""
        if self._is_token_valid():
            return self._outapi_token

        return self._fetch_new_token()

    def make_request(self, method: str, endpoint: str, data: Dict = None,
                    headers: Dict = None) -> Dict:
        """发起HTTP请求"""
        url = f"{self._main_url}{endpoint}"
        request_headers = self._build_headers(headers)

        try:
            if method.upper() == 'GET':
                response = requests.get(url, headers=request_headers,
                                      timeout=self.DEFAULT_TIMEOUT)
            elif method.upper() == 'POST':
                response = requests.post(url, headers=request_headers,
                                       data=json.dumps(data) if data else None,
                                       timeout=self.DEFAULT_TIMEOUT)
            elif method.upper() == 'PUT':
                response = requests.put(url, headers=request_headers,
                                      data=json.dumps(data) if data else None,
                                      timeout=self.DEFAULT_TIMEOUT)
            else:
                raise CrmebValidationError(f"不支持的HTTP方法: {method}")

            return self._handle_response(response)

        except requests.RequestException as e:
            raise CrmebApiError(f"HTTP请求失败: {e}")

    # === 4. 辅助方法 ===
    def _is_token_valid(self) -> bool:
        """检查token是否有效"""
        if not self._outapi_token or not self._outapi_token_expire_time:
            return False

        current_timestamp = int(datetime.now().timestamp())
        return current_timestamp + self.TOKEN_BUFFER_TIME < self._outapi_token_expire_time

    def _fetch_new_token(self) -> str:
        """获取新的访问令牌"""
        url = f"{self._main_url}/outapi/get_token"
        data = {
            "appid": self._appid,
            "appsecret": self._appsecret
        }

        try:
            response = requests.post(url, data=data, timeout=self.DEFAULT_TIMEOUT)
            response.raise_for_status()

            response_data = response.json()
            if response_data.get('status') != 200:
                raise CrmebAuthError(f"获取token失败: {response_data.get('msg', '未知错误')}")

            self._outapi_token = response_data['data']['token']
            self._outapi_token_expire_time = response_data['data']['exp_time']

            return self._outapi_token

        except requests.RequestException as e:
            raise CrmebApiError(f"获取token请求失败: {e}")
        except (KeyError, TypeError) as e:
            raise CrmebApiError(f"token响应数据格式错误: {e}")

    def _build_headers(self, custom_headers: Dict = None) -> Dict:
        """构建请求头"""
        token = self.get_token()
        headers = {
            'authori-zation': f'Bearer {token}',
            'Content-Type': 'application/json'
        }

        if custom_headers:
            headers.update(custom_headers)

        return headers

    def _handle_response(self, response: requests.Response) -> Dict:
        """处理HTTP响应"""
        if response.status_code != 200:
            try:
                error_data = response.json()
                raise CrmebApiError(f"HTTP {response.status_code}: {error_data.get('msg', '未知错误')}")
            except json.JSONDecodeError:
                raise CrmebApiError(f"HTTP {response.status_code}: {response.text[:200]}...")

        try:
            result = response.json()
        except json.JSONDecodeError as e:
            raise CrmebApiError(f"响应JSON解析失败: {e}")

        if result.get('status') != 200:
            raise CrmebApiError(f"API业务错误: {result.get('msg', '未知业务错误')}")

        return result


# ==================== 业务服务类 ====================

class CrmebProductManager:
    """CRMEB商品管理器 - 标准4分组结构"""

    # === 1. 类常量 ===
    SPEC_TYPE_SINGLE = 0  # 单规格
    SPEC_TYPE_MULTI = 1   # 多规格

    # 规格模板ID常量
    SPEC_TEMPLATE_ID = 16  # 规格模板ID

    # 虚拟销量范围
    VIRTUAL_SALES_MIN = 50  # 最小虚拟销量
    VIRTUAL_SALES_MAX = 500  # 最大虚拟销量

    # 保障服务配置
    DEFAULT_ENSURE_IDS = [9, 8, 6]  # 默认保障服务
    DELAY_REPAIR_ENSURE_ID = 7  # 延时修护额外保障服务

    # 重量单位转换常量
    WEIGHT_CONVERSION_FACTOR = 1000  # 1kg = 1000g

    # 商品标签映射
    PRODUCT_LABELS = {
        '延时修护': 18,
        '安全套套': 19,
        '男神玩具': 20,
        '女神玩具': 21,
        '快感润滑': 22,
        '情趣内衣': 23,
        '情侣互动': 24,
        '后庭开发': 25,
        '玩具搭配': 26,
    }

    # 分类模板头图映射
    CATEGORY_TEMPLATES = {
        '男神玩具': 'https://qiniu.shikejk.com/templates/xq/nszp.jpg',
        '女神玩具': 'https://qiniu.shikejk.com/templates/xq/nvszp.jpg',
        '情趣内衣': 'https://qiniu.shikejk.com/templates/xq/nyzp.jpg',
        '情侣互动': 'https://qiniu.shikejk.com/templates/xq/rhyzp.jpg',
        '后庭开发': 'https://qiniu.shikejk.com/templates/xq/rhyzp.jpg',
        '安全套套': 'https://qiniu.shikejk.com/templates/xq/bytsm.jpg',
    }

    # 关联用户标签映射
    USER_LABELS = {
        '延时修护': 44,
        '安全套套': 45,
        '男神玩具': 46,
        '女神玩具': 47,
        '快感润滑': 48,
        '情趣内衣': 5,
        '情侣互动': 42,
        '后庭开发': 49,
        '玩具搭配': None,  # 没有对应标签
    }

    # 商品单位映射
    PRODUCT_UNITS = {
        '延时修护': '瓶',
        '安全套套': '盒',
        '快感润滑': '盒',
        '情趣内衣': '件',
        'default': '个',  # 默认单位
    }

    # 分类运费模板映射
    # 默认运费模板：temp_id=1（包邮）
    # 支持动态配置：只需在此映射表中添加新的分类和对应的运费模板ID即可
    # 处理优先级：一级分类 > 二级分类 > 默认模板
    CATEGORY_SHIPPING = {
        '安全套套': 3,  # 运费模板ID：3=安全套套专用运费模板
        # '延时修护': 4,  # 示例：如需为延时修护设置特殊运费模板，取消注释并设置正确的模板ID
        # '情趣内衣': 5,  # 示例：如需为情趣内衣设置特殊运费模板，取消注释并设置正确的模板ID
        # '男神玩具': 6,  # 示例：如需为男神玩具设置特殊运费模板，取消注释并设置正确的模板ID
        # '女神玩具': 7,  # 示例：如需为女神玩具设置特殊运费模板，取消注释并设置正确的模板ID
        'default': 1,  # 默认运费模板ID：1=包邮
    }

    # 品牌映射
    BRAND_MAPPING = {
        "霏慕": 7,
        "热恋": 85,
        "魅动": 84,
        "君岛爱": 83,
        "云曼": 76,
        "愉兔": 75,
        "索迹": 74,
        "食也": 73,
        "神奈": 72,
        "趣儿": 71,
        "罗格": 70,
        "可魅儿": 69,
        "久爱": 68,
        "赫兹少女": 67,
        "荷尔先生": 66,
        "告白兔": 65,
        "丁丁好医": 64,
        "得音": 63,
        "ZALO": 62,
        "Viotec": 61,
        "TENGA": 60,
        "Sweety": 59,
        "Sameyo": 58,
        "ROOMFUN": 57,
        "OROK": 56,
        "MRB": 55,
        "LOMA": 54,
        "ICHOKER": 53,
        "EasyLive": 52,
        "Cokelife": 51,
        "Cachito": 50,
        "beU": 49,
        "AK": 48,
        "爱世界": 47,
        "龙水": 46,
        "黑豹": 45,
        "斯汉德": 44,
        "享要": 43,
        "枕木恋": 42,
        "GluGlu": 41,
        "撸撸杯": 39,
        "三生爱": 34,
        "Galaku": 33,
        "安太医": 32,
        "A-ONE": 30,
        "WILDONE": 29,
        "久兴": 28,
        "Kisstoy": 27,
        "网易春风": 26,
        "雅润": 25,
        "交悦": 24,
        "爱威康": 23,
        "私享玩趣": 22,
        "RoseLEX": 21,
        "LELO": 20,
        "独爱": 18,
        "欧亚思": 17,
        "涩井": 16,
        "OIX": 15,
        "Rends": 14,
        "司沃康": 8,
        "夜劲": 6,
        "羞羞哒": 5,
        "雷霆": 4,
        "谜姬": 3,
        "尚牌": 86,
        "倍力乐": 36,
        "冈本": 37,
        "名流": 35,
        "大象": 80,
        "威尔乐": 81,
        "捷古斯": 82,
        "杜蕾斯": 38,
        "杰士邦": 40,
        "私激": 87,
        "第六感": 79,
        "赤尾": 31,
    }

    # 规格图片配置
    MAIN_SPEC_ADD_PIC = 1  # 主规格是否添加图片：1-添加，0-不添加（副规格固定为0）

    # === 2. 初始化 ===
    def __init__(self, config: Dict[str, str]):
        """初始化商品管理器

        Args:
            config: 配置字典，包含main_url、appid、appsecret
        """
        self._api_client = CrmebApiClient(config)
        self._category_mapping = self.get_categories()


    # === 3. 主要业务方法 ===
    def search_product_by_keyword(self, keyword: str, return_all: bool = False):
        """通过关键词搜索获取商品ID

        Args:
            keyword: 搜索关键词
            return_all: 是否返回全部结果，False时返回ID最大的商品

        Returns:
            int: 单个商品ID（return_all=False时）
            List[int]: 商品ID列表（return_all=True时）
        """
        if not keyword:
            raise CrmebValidationError("搜索关键词不能为空")

        endpoint = f"/api/pc/get_products?page=1&limit=20&keyword={keyword}"
        result = self._api_client.make_request('GET', endpoint)

        products = result.get('data', {}).get('list', [])
        if not products:
            raise CrmebApiError(f"未找到商品: {keyword}")

        if return_all:
            # 返回所有商品的ID列表
            return [product.get('id') for product in products if product.get('id')]
        else:
            # 返回ID最大的商品
            max_product = max(products, key=lambda x: x.get('id', 0))
            return max_product.get('id')

    def convert_product_data_for_upload(self, source_data: Dict, spec_names: List[str] = None) -> Dict:
        """将源数据转换为CRMEB上传商品格式

        Args:
            source_data: 源商品数据（如从database_manager获取的数据）
            spec_names: 规格名称列表，如['主规格', '副规格']，如果不提供则使用默认值

        Returns:
            Dict: 转换后的CRMEB格式数据
        """
        # 动态分析规格维度
        if spec_names is None:
            spec_names = self._analyze_spec_names(source_data)

        # 基础字段映射和转换
        crmeb_data = self._convert_basic_fields(source_data)

        # 处理规格数据
        self._convert_spec_data(source_data, crmeb_data, spec_names)

        # 添加商品规格参数
        crmeb_data['specs'] = self._get_specs(source_data.get('product_code', ''))

        # 设置默认值
        self._set_upload_defaults(crmeb_data, source_data)

        return crmeb_data

    def convert_product_data_for_update(self, source_data: Dict, spec_names: List[str] = None) -> Dict:
        """将源数据转换为CRMEB更新商品格式（参考upload逻辑）

        Args:
            source_data: 源商品数据（如从database_manager获取的数据）
            spec_names: 规格名称列表，如['主规格', '副规格']，如果不提供则使用默认值

        Returns:
            Dict: 转换后的CRMEB更新格式数据
        """
        # 动态分析规格维度
        if spec_names is None:
            spec_names = self._analyze_spec_names(source_data)

        # 基础字段映射和转换（完全参考upload逻辑）
        crmeb_data = self._convert_basic_fields(source_data)

        # 处理规格数据（完全参考upload逻辑）
        self._convert_spec_data(source_data, crmeb_data, spec_names)

        # 添加商品规格参数（完全参考upload逻辑）
        crmeb_data['specs'] = self._get_specs(source_data.get('product_code', ''))

        # 处理销量：获取当前销量并随机增加
        self._update_sales_with_current_data(crmeb_data, source_data)

        # 设置默认值（参考upload逻辑）
        self._set_upload_defaults(crmeb_data, source_data)

        return crmeb_data

    def get_categories(self) -> Dict[str, int]:
        """获取分类列表并转换为字典格式

        Returns:
            Dict[str, int]: 分类名称到ID的映射字典，如 {'情趣内衣': 206, '性感睡裙': 236}
        """
        endpoint = "/outapi/category/list"
        response = self._api_client.make_request('GET', endpoint)

        if response.get('status') != 200:
            return {}

        categories_data = response.get('data', [])
        categories_dict = {}

        for category in categories_data:
            cate_name = category.get('cate_name', '')
            cate_id = category.get('id', 0)
            is_show = category.get('is_show', 1)

            # 只处理有名称、有ID且显示的分类
            if cate_name and cate_id and is_show == 1:
                categories_dict[cate_name] = cate_id

        return categories_dict

    def upload_product(self, product_data: Dict) -> Dict:
        """上传商品到CRMEB商城

        Args:
            product_data: 已转换为CRMEB格式的商品数据
        """
        self._validate_upload_data(product_data)

        endpoint = "/outapi/product"
        return self._api_client.make_request('POST', endpoint, product_data)

    def update_product_status(self, product_id: int, is_show: int) -> bool:
        """更新商品状态（1上架/0下架）"""
        if is_show not in (0, 1):
            raise CrmebValidationError(f"无效的状态值: {is_show}，必须是 0 或 1")

        endpoint = f"/outapi/product/set_show/{product_id}/{is_show}"
        result = self._api_client.make_request('PUT', endpoint)

        return result.get('status') == 200

    def update_product(self, product_id: int, product_data: Dict) -> bool:
        """更新商品信息

        Args:
            product_id: 商品ID
            product_data: 商品数据字典（已转换为CRMEB格式）

        Returns:
            bool: 更新成功返回True，失败抛出异常
        """
        try:
            # 构建请求URL
            endpoint = f"/outapi/product/{product_id}"

            # 根据分类设置is_good值
            is_good_value = self._get_is_good_by_category(product_data)

            # 添加必要的默认值
            default_data = {
                "type": 0,          # 平台商品
                "product_type": 0,  # 普通商品
                "mer_id": 0,        # 总后台管理员
                "is_verify": 1,     # 已审核
                "is_del": 0,        # 未删除
                "mer_use": 0,       # 不可代理
                "is_good": is_good_value,  # 根据分类设置优品推荐
                "is_sub": 0,        # 非单独分佣
                "spec_type": 1,     # 多规格
            }

            # 合并数据（product_data优先级更高）
            data = {**default_data, **product_data}

            # 验证和处理价格数据
            self._validate_and_format_prices(data)

            # 验证更新数据
            self._validate_update_data(data)

            # 使用API客户端发送请求
            self._api_client.make_request('PUT', endpoint, data)

            logger.debug(f"商品 {product_id} 更新成功")
            return True

        except Exception as e:
            logger.debug(f"更新商品失败: {str(e)}")
            raise Exception(f"更新商品失败: {str(e)}")

    # === 4. 辅助方法 ===
    def _get_specs(self, product_code: str) -> List[Dict]:
        """生成商品规格参数"""
        return [
            {
                "specs_id": 1,
                "temp_id": self.SPEC_TEMPLATE_ID,
                "name": "商品编码",
                "value": product_code,
                "sort": 0
            }
        ]

    def _convert_basic_fields(self, source_data: Dict) -> Dict:
        """转换基础字段 - 只保留CRMEB需要的字段"""
        crmeb_fields = {
            # 基本信息
            'store_name': source_data.get('title', ''),  # 商品标题 -> 商品名称
            'code': source_data.get('product_code', ''),  # 商品编码
            'store_info': self._generate_store_info(source_data),  # 商品简介
            'slider_image': self._convert_image_list(source_data.get('main_images', [])),  # 轮播图
            'description': self._convert_detail_images_to_html(source_data),  # 详情HTML（包含模板头图）

            # 分类相关
            'cate_id': self._convert_categories(source_data),  # 分类ID列表

            # 品牌相关
            'brand_id': self._convert_brand(source_data.get('brand', '')),  # 品牌ID

            # 规格类型（强制多规格）
            'spec_type': self.SPEC_TYPE_MULTI,  # 强制多规格

            # 营销相关
            'ficti': self._generate_virtual_sales(),  # 虚拟销量
            'ensure_id': self._get_ensure_services(source_data),  # 保障服务
            'store_label_id': self._get_product_labels(source_data),  # 商品标签
            'label_id': self._get_user_labels(source_data),  # 关联用户标签

            # 其他字段
            'unit_name': self._get_product_unit(source_data),  # 单位名称
            'keyword': self._generate_keywords(source_data),  # 商品关键字
        }

        # 过滤掉空值和不需要的字段
        return {k: v for k, v in crmeb_fields.items() if v is not None and v != '' and v != []}

    def _convert_image_list(self, images) -> List[str]:
        """转换图片列表格式"""
        if isinstance(images, str):
            # 如果是逗号分隔的字符串，转为列表
            return [img.strip() for img in images.split(',') if img.strip()]
        elif isinstance(images, list):
            return images
        else:
            return []

    def _convert_detail_images_to_html(self, source_data: Dict) -> str:
        """将详情图片列表转换为HTML格式，包含分类模板头图"""
        detail_images = source_data.get('detail_images', [])
        if not detail_images:
            return ""

        # 处理图片数据格式
        if isinstance(detail_images, str):
            # 如果是逗号分隔的字符串，转为列表
            detail_images = [img.strip() for img in detail_images.split(',') if img.strip()]
        elif not isinstance(detail_images, list):
            detail_images = []

        html_parts = []

        # 添加分类模板头图
        template_img = self._get_category_template(source_data)
        if template_img:
            html_parts.append(f'<img src="{template_img}" style="width:100%;height:auto;" />')

        # 添加详情图片
        for img_url in detail_images:
            if img_url.strip():
                html_parts.append(f'<img src="{img_url.strip()}" style="width:100%;height:auto;" />')

        return ''.join(html_parts)

    def _convert_categories(self, source_data: Dict) -> List[int]:
        """转换分类信息为分类ID列表"""
        cate_ids = []

        # 处理一级分类
        level1_categories = source_data.get('category_level1', [])
        if isinstance(level1_categories, str):
            # 如果是字符串，转为列表
            level1_categories = [level1_categories]
        if isinstance(level1_categories, list):
            for cat in level1_categories:
                if cat in self._category_mapping:
                    cate_ids.append(self._category_mapping[cat])

        # 处理二级分类
        level2_categories = source_data.get('category_level2', [])
        if isinstance(level2_categories, str):
            # 如果是字符串，转为列表
            level2_categories = [level2_categories]
        if isinstance(level2_categories, list):
            for cat in level2_categories:
                if cat in self._category_mapping:
                    cate_ids.append(self._category_mapping[cat])

        # 如果没有找到分类，返回默认分类
        return cate_ids if cate_ids else [206]  # 默认分类ID

    def _convert_brand(self, brand_name: str) -> List[str]:
        """转换品牌名称为品牌ID列表"""
        if brand_name in self.BRAND_MAPPING:
            return [self.BRAND_MAPPING[brand_name]]
        else:
            return []  # 没有品牌时返回空列表

    def _analyze_spec_names(self, source_data: Dict) -> List[str]:
        """动态分析规格名称

        Args:
            source_data: 源商品数据

        Returns:
            List[str]: 规格名称列表
        """
        skus = source_data.get('skus', [])
        if not skus:
            return ['规格']  # 默认单规格

        # 检查是否有副规格（sub_spec_value）
        has_sub_spec = any(sku.get('sub_spec_value', '').strip() for sku in skus)

        if has_sub_spec:
            return ['规格', '副规格']  # 双规格：主规格 + 副规格
        else:
            return ['规格']  # 单规格：只有主规格

    def _convert_spec_data(self, source_data: Dict, crmeb_data: Dict, spec_names: List[str]) -> None:
        """转换规格数据"""
        # 根据源数据中的SKU信息转换为CRMEB格式
        skus = source_data.get('skus', [])

        # 获取当前商品的SKU顺序并重新排列
        ordered_skus = self._get_ordered_skus(source_data, skus)

        # 过滤SKU数据，只保留需要的字段
        filtered_skus = []
        for sku in ordered_skus:
            filtered_sku = {
                'sku_code': sku.get('sku_code', ''),
                'spec_value': sku.get('spec_value', ''),
                'sub_spec_value': sku.get('sub_spec_value', ''),
                'spec_image': sku.get('spec_image', ''),
                'sale_price': sku.get('sale_price', 0),
                'cost_price': sku.get('cost_price', 0),
                'weight': sku.get('weight', 0),
                'stock_quantity': sku.get('stock_quantity', 0),
            }
            filtered_skus.append(filtered_sku)

        # 强制使用多规格（按您的要求）
        crmeb_data['spec_type'] = self.SPEC_TYPE_MULTI
        crmeb_data['items'] = self._convert_skus_to_items(filtered_skus, spec_names)
        crmeb_data['attrs'] = self._convert_skus_to_attrs(filtered_skus, spec_names)
        crmeb_data['attr'] = self._generate_default_attr_from_skus(filtered_skus)

    def _get_ordered_skus(self, source_data: Dict, skus: List[Dict]) -> List[Dict]:
        """获取按照CRMEB商品详情顺序排列的SKU列表

        Args:
            source_data: 源商品数据
            skus: 数据库中的SKU列表

        Returns:
            List[Dict]: 按照CRMEB顺序排列的SKU列表
        """
        try:
            # 获取商品ID
            shop_product_id = source_data.get('shop_product_id')
            if not shop_product_id:
                logger.debug("无法获取商品ID，使用原始SKU顺序")
                return skus

            # 获取当前商品详情中的SKU顺序
            current_product = self.get_product_detail(shop_product_id)
            if not current_product:
                logger.debug("无法获取商品详情，使用原始SKU顺序")
                return skus

            current_attrs = current_product.get('productInfo', {}).get('attrs', [])
            if not current_attrs:
                logger.debug("商品详情中没有SKU信息，使用原始SKU顺序")
                return skus

            # 创建SKU映射表（通过规格值匹配）
            sku_map = {}
            for sku in skus:
                spec_value = sku.get('spec_value', '').strip()
                if spec_value:
                    sku_map[spec_value] = sku

            # 按照CRMEB中的顺序重新排列SKU
            ordered_skus = []
            for attr in current_attrs:
                detail = attr.get('detail', {})
                if detail:
                    # 获取主规格值
                    spec_value = list(detail.values())[0] if detail else ''
                    if spec_value in sku_map:
                        ordered_skus.append(sku_map[spec_value])
                        del sku_map[spec_value]  # 避免重复添加

            # 添加剩余的SKU（如果有新增的）
            for remaining_sku in sku_map.values():
                ordered_skus.append(remaining_sku)

            logger.debug(f"SKU顺序调整完成: {len(ordered_skus)} 个SKU")
            return ordered_skus

        except Exception as e:
            logger.debug(f"获取SKU顺序失败: {e}，使用原始顺序")
            return skus

    def _convert_single_sku_to_attr(self, sku: Dict) -> Dict:
        """将单个SKU转换为CRMEB attr格式"""
        return {
            "price": float(sku.get('sale_price', 0)),
            "cost": float(sku.get('cost_price', 0)),
            "stock": int(sku.get('stock_quantity', 0)),
            "code": sku.get('sku_code', ''),
            "weight": self._convert_weight_to_kg(sku.get('weight', 0)),
            "is_show": 1,
            "is_default_select": 1
        }

    def _convert_weight_to_kg(self, weight_grams) -> float:
        """将重量从克转换为千克

        Args:
            weight_grams: 重量（克）

        Returns:
            float: 转换后的重量（千克）
        """
        try:
            weight = float(weight_grams) if weight_grams is not None else 0
            
            if weight <= 0:
                return 0
                
            # 转换为千克
            weight_kg = weight / self.WEIGHT_CONVERSION_FACTOR
            

                
            return round(weight_kg, 3)  # 保留3位小数
            
        except (ValueError, TypeError) as e:
            logger.debug(f"重量转换失败: {weight_grams}g, 错误: {e}")
            return 0

    def _convert_skus_to_items(self, skus: List[Dict], spec_names: List[str]) -> List[Dict]:
        """将SKU列表转换为CRMEB items格式"""
        if not skus:
            return []

        items = []

        # 第一个规格：主规格（对应spec_value）
        if len(spec_names) >= 1:
            # 创建spec_value到图片的映射
            spec_value_to_pic = {}
            for sku in skus:
                spec_val = sku.get('spec_value', '')
                spec_pic = sku.get('spec_image', '')
                if spec_val and spec_pic:
                    spec_value_to_pic[spec_val] = spec_pic

            # 保持SKU原始顺序的去重
            spec_values = []
            seen = set()
            for sku in skus:
                spec_val = sku.get('spec_value', '')
                if spec_val and spec_val not in seen:
                    spec_values.append(spec_val)
                    seen.add(spec_val)

            if spec_values:
                # 构建detail列表
                detail_list = []
                for val in spec_values:
                    detail_item = {"value": val}
                    # 只有当MAIN_SPEC_ADD_PIC为1时才添加pic字段
                    if self.MAIN_SPEC_ADD_PIC == 1:
                        detail_item["pic"] = spec_value_to_pic.get(val, "")
                    detail_list.append(detail_item)

                items.append({
                    "value": spec_names[0],  # 主规格
                    "add_pic": self.MAIN_SPEC_ADD_PIC,
                    "detail": detail_list
                })

        # 第二个规格：副规格（对应sub_spec_value）
        # sub_spec_value在所有SKU中相同，随便取一个即可
        if len(spec_names) >= 2:
            # 从任意一个SKU中获取sub_spec_value
            sub_spec_value = next((sku.get('sub_spec_value', '').strip() for sku in skus if sku.get('sub_spec_value', '').strip()), '')
            if sub_spec_value:
                items.append({
                    "value": spec_names[1],  # 副规格
                    "add_pic": 0,  # 副规格固定为0，不添加图片
                    "detail": [{"value": sub_spec_value}]  # 副规格不包含pic字段
                })

        return items

    def _find_first_in_stock_sku_index(self, skus: List[Dict]) -> int:
        """找到按顺序第一个有库存的SKU索引
        
        Args:
            skus: SKU列表
            
        Returns:
            int: 第一个有库存的SKU索引，如果都没有库存则返回0（第一个SKU）
        """
        for index, sku in enumerate(skus):
            stock = int(sku.get('stock_quantity', 0))
            if stock > 0:
                return index
        
        # 如果都没有库存，返回第一个SKU的索引
        return 0

    def _convert_skus_to_attrs(self, skus: List[Dict], spec_names: List[str]) -> List[Dict]:
        """将SKU列表转换为CRMEB attrs格式"""
        if not skus:
            return []

        attrs = []
        # 找到按顺序第一个有库存的SKU索引
        first_in_stock_index = self._find_first_in_stock_sku_index(skus)

        for sku_index, sku in enumerate(skus):
            spec_value = sku.get('spec_value', '').strip()
            sub_spec_value = sku.get('sub_spec_value', '').strip()
            sale_price = float(sku.get('sale_price', 0))

            # 构建attr_arr和detail
            attr_arr = []
            detail = {}

            # 主规格（spec_value）
            if len(spec_names) >= 1 and spec_value:
                attr_arr.append(spec_value)
                detail[spec_names[0]] = spec_value

            # 副规格（sub_spec_value）- 只有当确实有副规格时才添加
            if len(spec_names) >= 2 and sub_spec_value:
                attr_arr.append(sub_spec_value)
                detail[spec_names[1]] = sub_spec_value

            # 构建attrs项
            attr_item = {
                "attr_arr": attr_arr,
                "detail": detail,
                "title": spec_names[1] if len(spec_names) >= 2 else spec_names[0],  # 固定副规格
                "key": spec_names[1] if len(spec_names) >= 2 else spec_names[0],    # 固定副规格
                "price": sale_price,
                "pic": sku.get('spec_image', ''),
                "cost": float(sku.get('cost_price', 0)),
                "ot_price": round(sale_price * 1.2, 2),  # 售价乘以1.2
                "stock": int(sku.get('stock_quantity', 0)),
                "code": sku.get('sku_code', ''),
                "weight": self._convert_weight_to_kg(sku.get('weight', 0)),
                "volume": 0,  # 默认体积
                "is_show": 1,
                "is_default_select": 1 if sku_index == first_in_stock_index else 0,  # 按顺序第一个有库存的设为默认选中
                "index": sku_index + 1  # 保持原始顺序，从1开始
            }

            attrs.append(attr_item)

        # index已经在循环中设置，无需重新设置
        return attrs

    def _generate_default_attr_from_skus(self, skus: List[Dict]) -> Dict:
        """从SKU列表生成默认attr - 选择按顺序第一个有库存的SKU"""
        if not skus:
            return {}

        # 选择按顺序第一个有库存的SKU
        selected_sku = self._select_best_sku(skus)
        total_stock = sum(int(sku.get('stock_quantity', 0)) for sku in skus)

        return {
            "price": float(selected_sku.get('sale_price', 0)),
            "cost": float(selected_sku.get('cost_price', 0)),
            "stock": total_stock,
            "code": selected_sku.get('sku_code', ''),
            "weight": self._convert_weight_to_kg(selected_sku.get('weight', 0)),
            "is_show": 1,
            "is_default_select": 0
        }

    def _select_best_sku(self, skus: List[Dict]) -> Dict:
        """选择最佳SKU：按顺序第一个有库存的SKU，如果都没有库存则选择第一个

        Args:
            skus: SKU列表

        Returns:
            Dict: 选中的SKU
        """
        if not skus:
            return {}

        # 按顺序查找第一个有库存的SKU
        for sku in skus:
            stock = int(sku.get('stock_quantity', 0))
            if stock > 0:
                logger.debug(f"选择按顺序第一个有库存的SKU: 规格={sku.get('spec_value')}, 价格={sku.get('sale_price')}, 库存={stock}")
                return sku

        # 如果都没有库存，选择第一个SKU
        selected_sku = skus[0]
        logger.debug(f"所有SKU无库存，选择第一个SKU: 规格={selected_sku.get('spec_value')}, 价格={selected_sku.get('sale_price')}, 库存={selected_sku.get('stock_quantity', 0)}")
        return selected_sku

    def _set_upload_defaults(self, crmeb_data: Dict, source_data: Dict = None) -> None:
        """设置上传时的默认值
        
        Args:
            crmeb_data: 正在构建的CRMEB数据
            source_data: 源商品数据（用于获取分类信息，可选）
        """
        defaults = {
            'delivery_type': ["1"],  # 默认快递配送
            'is_show': 1,  # 默认上架
            'unit_name': '件',  # 默认单位
            'freight': 3,  # 默认指定模板
            'temp_id': 1,  # 默认通用模板
            'postage': 0,  # 邮费0元
            'type': 0,  # 平台商品
            'is_support_refund': 1,  # 支持退款
            'product_type': 0,  # 普通商品
            'is_presale_product': 0,  # 非预售
            'presale_time': [],
            'presale_day': 0,
            'presale_status': 1,
            'specs_id': self.SPEC_TEMPLATE_ID,  # 商品参数模板ID
            'system_form_id': 0  # 系统表单ID
        }

        # 设置基础默认值
        for key, value in defaults.items():
            if key not in crmeb_data:
                crmeb_data[key] = value

        # 根据分类设置运费策略
        if source_data:
            self._set_category_shipping(crmeb_data, source_data)

    def _set_category_shipping(self, crmeb_data: Dict, source_data: Dict) -> None:
        """根据分类设置运费模板ID - 动态遍历所有分类

        Args:
            crmeb_data: 正在构建的CRMEB数据
            source_data: 源商品数据
        """
        # 获取分类列表（处理字符串格式）
        categories = []
        level1 = source_data.get('category_level1', [])
        level2 = source_data.get('category_level2', [])

        if isinstance(level1, str):
            categories.append(level1)
        elif isinstance(level1, list):
            categories.extend(level1)

        if isinstance(level2, str):
            categories.append(level2)
        elif isinstance(level2, list):
            categories.extend(level2)

        # 遍历分类查找特殊运费模板（优先级：一级分类 > 二级分类）
        for category in categories:
            if category in self.CATEGORY_SHIPPING:
                temp_id = self.CATEGORY_SHIPPING[category]
                crmeb_data['temp_id'] = temp_id
                logger.debug(f"使用分类 '{category}' 的运费模板: {temp_id}")
                return

        # 如果没有找到特殊运费模板，使用默认
        temp_id = self.CATEGORY_SHIPPING['default']
        crmeb_data['temp_id'] = temp_id
        logger.debug(f"使用默认运费模板: {temp_id}")

    def _add_random_sales(self, crmeb_data: Dict, source_data: Dict = None) -> None:
        """根据分类随机增加虚拟销量

        Args:
            crmeb_data: 正在构建的CRMEB数据
            source_data: 源商品数据（用于获取分类信息）
        """
        import random

        # 根据分类确定销量增长范围
        if source_data:
            category_level1 = source_data.get('category_level1', '')
            if category_level1 in ['安全套套', '延时修护']:
                # 安全套套和延时修护：5-10
                sales_increase = random.randint(5, 10)
                category_info = f"({category_level1})"
            else:
                # 其他分类：0-5
                sales_increase = random.randint(0, 5)
                category_info = f"({category_level1})"
        else:
            # 默认：0-5
            sales_increase = random.randint(0, 5)
            category_info = "(默认)"

        # 获取当前虚拟销量，如果没有则从0开始
        current_ficti = crmeb_data.get('ficti', 0)
        new_ficti = current_ficti + sales_increase

        crmeb_data['ficti'] = new_ficti

        logger.debug(f"随机增加销量: {sales_increase} {category_info}，虚拟销量设为: {new_ficti}")

    def _update_sales_with_current_data(self, crmeb_data: Dict, source_data: Dict) -> None:
        """获取当前商品销量并随机增加

        Args:
            crmeb_data: 正在构建的CRMEB数据
            source_data: 源商品数据（包含shop_product_id）
        """
        import random

        try:
            # 获取商品ID
            shop_product_id = source_data.get('shop_product_id')
            if not shop_product_id:
                logger.debug("无法获取商品ID，使用默认销量逻辑")
                self._add_random_sales(crmeb_data, source_data)
                return

            # 获取当前商品详情
            logger.debug(f"获取商品 {shop_product_id} 的当前销量...")
            current_product = self.get_product_detail(shop_product_id)

            if not current_product:
                logger.debug("无法获取商品详情，使用默认销量逻辑")
                self._add_random_sales(crmeb_data, source_data)
                return

            # 获取当前销量数据
            product_info = current_product.get('productInfo', {})
            current_ficti = int(product_info.get('ficti', 0))      # 虚拟销量

            # 根据分类确定销量增长范围
            category_level1 = source_data.get('category_level1', '')
            if category_level1 in ['安全套套', '延时修护']:
                # 安全套套和延时修护：5-10
                sales_increase = random.randint(5, 10)
            else:
                # 其他分类：0-5
                sales_increase = random.randint(0, 5)

            # 计算新的虚拟销量
            new_ficti = current_ficti + sales_increase
            crmeb_data['ficti'] = new_ficti


        except Exception as e:
            logger.debug(f"获取当前销量失败: {e}")
            logger.debug("使用默认销量逻辑")
            self._add_random_sales(crmeb_data, source_data)

    def _validate_and_format_prices(self, data: Dict) -> None:
        """验证和格式化价格数据"""
        if 'attrs' in data and isinstance(data['attrs'], list):
            for attr in data['attrs']:
                # 转换价格为浮点数并格式化
                vip_price = float(attr.get('vip_price', 0))
                price = float(attr.get('price', 0))
                ot_price = float(attr.get('ot_price', 0))

                # 格式化价格为字符串（保留两位小数）
                attr['vip_price'] = f"{vip_price:.2f}"
                attr['price'] = f"{price:.2f}"
                attr['ot_price'] = f"{ot_price:.2f}"

    def _validate_update_data(self, data: Dict) -> None:
        """验证更新数据"""
        # 验证必填字段
        if not data.get('store_name'):
            raise Exception("商品名称不能为空")

        logger.debug(f"数据验证通过，包含 {len(data)} 个字段")

    def _get_is_good_by_category(self, product_data: Dict) -> int:
        """根据分类设置是否为优品推荐

        Args:
            product_data: 商品数据

        Returns:
            int: 1为优品推荐，0为非优品推荐
        """
        # 从分类ID反推分类名称
        cate_ids = product_data.get('cate_id', [])
        if not cate_ids:
            return 0

        # 检查是否包含安全套套(227)或延时修护的分类ID
        # 通过分类映射反向查找
        for cate_name, cate_id in self._category_mapping.items():
            if cate_id in cate_ids and cate_name in ['安全套套', '延时修护']:
                logger.debug(f"检测到优品分类: {cate_name}，设置为优品推荐")
                return 1

        logger.debug(f"普通分类，设置为非优品推荐")
        return 0

    def _set_update_defaults(self, crmeb_data: Dict) -> None:
        """设置更新时的默认值"""
        # 更新时可能不需要所有字段，只设置必要的默认值
        # 暂时不需要特殊处理
        _ = crmeb_data  # 避免未使用参数警告

    def _generate_virtual_sales(self, base_sales: int = 0) -> int:
        """生成虚拟销量"""
        if base_sales > 0:
            # 更新模式：在原销量基础上增加
            additional = random.randint(1, 20)
            return base_sales + additional
        else:
            # 新增模式：随机生成
            return random.randint(self.VIRTUAL_SALES_MIN, self.VIRTUAL_SALES_MAX)

    def _get_ensure_services(self, source_data: Dict) -> List[int]:
        """获取保障服务列表"""
        ensure_ids = self.DEFAULT_ENSURE_IDS.copy()

        # 获取分类列表（处理字符串格式）
        categories = []
        level1 = source_data.get('category_level1', [])
        level2 = source_data.get('category_level2', [])

        if isinstance(level1, str):
            categories.append(level1)
        elif isinstance(level1, list):
            categories.extend(level1)

        if isinstance(level2, str):
            categories.append(level2)
        elif isinstance(level2, list):
            categories.extend(level2)

        # 检查是否是延时修护类商品
        if '延时修护' in categories:
            ensure_ids.append(self.DELAY_REPAIR_ENSURE_ID)

        return ensure_ids

    def _get_product_labels(self, source_data: Dict) -> List[int]:
        """获取商品标签列表"""
        labels = []

        # 获取分类列表（处理字符串格式）
        categories = []
        level1 = source_data.get('category_level1', [])
        level2 = source_data.get('category_level2', [])

        if isinstance(level1, str):
            categories.append(level1)
        elif isinstance(level1, list):
            categories.extend(level1)

        if isinstance(level2, str):
            categories.append(level2)
        elif isinstance(level2, list):
            categories.extend(level2)

        for category in categories:
            if category in self.PRODUCT_LABELS:
                labels.append(self.PRODUCT_LABELS[category])

        return labels

    def _get_user_labels(self, source_data: Dict) -> List[int]:
        """获取关联用户标签列表"""
        labels = []

        # 获取分类列表（处理字符串格式）
        categories = []
        level1 = source_data.get('category_level1', [])
        level2 = source_data.get('category_level2', [])

        if isinstance(level1, str):
            categories.append(level1)
        elif isinstance(level1, list):
            categories.extend(level1)

        if isinstance(level2, str):
            categories.append(level2)
        elif isinstance(level2, list):
            categories.extend(level2)

        for category in categories:
            if category in self.USER_LABELS and self.USER_LABELS[category] is not None:
                labels.append(self.USER_LABELS[category])

        return labels

    def _get_category_template(self, source_data: Dict) -> str:
        """获取分类模板头图"""
        categories = []

        # 处理一级分类
        level1 = source_data.get('category_level1', [])
        if isinstance(level1, str):
            categories.append(level1)
        elif isinstance(level1, list):
            categories.extend(level1)

        # 处理二级分类
        level2 = source_data.get('category_level2', [])
        if isinstance(level2, str):
            categories.append(level2)
        elif isinstance(level2, list):
            categories.extend(level2)

        for category in categories:
            if category in self.CATEGORY_TEMPLATES:
                return self.CATEGORY_TEMPLATES[category]

        return ""

    def _get_product_unit(self, source_data: Dict) -> str:
        """获取商品单位"""
        # 获取分类列表（处理字符串格式）
        categories = []
        level1 = source_data.get('category_level1', [])

        if isinstance(level1, str):
            categories.append(level1)
        elif isinstance(level1, list):
            categories.extend(level1)

        for category in categories:
            if category in self.PRODUCT_UNITS:
                return self.PRODUCT_UNITS[category]

        return self.PRODUCT_UNITS['default']

    def _generate_store_info(self, source_data: Dict) -> str:
        """生成商品简介：品牌+标题+商品编号"""
        parts = []

        brand = source_data.get('brand', '')
        if brand:
            parts.append(brand)

        title = source_data.get('title', '')
        if title:
            parts.append(title)

        product_code = source_data.get('product_code', '')
        if product_code:
            parts.append(product_code)

        return ' '.join(parts)

    def _generate_keywords(self, source_data: Dict) -> str:
        """生成商品关键字：品牌+标题+商品编号+所有sku的货号+一级和二级分类"""
        parts = []

        # 品牌
        brand = source_data.get('brand', '')
        if brand:
            parts.append(brand)

        # 标题
        title = source_data.get('title', '')
        if title:
            parts.append(title)

        # 商品编号
        product_code = source_data.get('product_code', '')
        if product_code:
            parts.append(product_code)

        # 所有SKU的货号
        skus = source_data.get('skus', [])
        for sku in skus:
            sku_code = sku.get('sku_code', '')
            if sku_code:
                parts.append(str(sku_code))  # 确保转换为字符串

        # 一级分类
        level1_categories = source_data.get('category_level1', [])
        parts.extend(level1_categories)

        # 二级分类
        level2_categories = source_data.get('category_level2', [])
        parts.extend(level2_categories)

        return ' '.join(parts)

    def _validate_upload_data(self, product_data: Dict) -> None:
        """验证上传数据"""
        if not isinstance(product_data, dict):
            raise CrmebValidationError("product_data 必须是字典类型")

        # 验证必填字段
        required_fields = ['store_name', 'slider_image']
        for field in required_fields:
            if not product_data.get(field):
                raise CrmebValidationError(f"必填字段不能为空: {field}")

        # 验证轮播图
        if not product_data.get('slider_image') or len(product_data.get('slider_image', [])) == 0:
            raise CrmebValidationError("轮播图列表不能为空，至少需要一张图片")

    def get_product_detail(self, product_id: int) -> dict:
        """获取商品详情信息

        Args:
            product_id: 商品ID，如 1

        Returns:
            dict: 商品详情数据，包含以下字段：
                - id: 商品ID
                - image: 主图
                - slider_image: 轮播图列表
                - store_name: 商品名称
                - store_info: 商品简介
                - keyword: 关键词
                - cate_id: 分类ID列表
                - price: 售价
                - ot_price: 市场价
                - postage: 邮费
                - unit_name: 单位
                - spec_type: 规格类型
                - items: 规格项列表
                - attrs: 规格属性列表
                等...

        Raises:
            Exception: 如果获取失败

        Example:
            >>> crmeb = CrmebProductManager(config)
            >>> detail = crmeb.get_product_detail(1)
            >>> logger.debug(f"商品名称: {detail['store_name']}")
        """
        try:
            # 构建请求URL
            endpoint = f"/outapi/product/{product_id}"

            # 使用API客户端发送请求（会自动处理token和错误）
            response_data = self._api_client.make_request('GET', endpoint)

            # 获取商品数据
            product_data = response_data.get('data', {})
            if not product_data:
                raise Exception("未获取到商品数据")

            return product_data

        except Exception as e:
            logger.debug(f"获取商品详情失败: {str(e)}")
            raise Exception(f"获取商品详情失败: {str(e)}")


# ==================== 前端请求和自动化 ==============
import requests
from typing import Dict, Any

class CrmebRequestManager:
    """CRMEB前端请求管理器"""

    def __init__(self, domain: str, account: str, password: str):
        """初始化请求管理器并自动登录

        Args:
            domain: 域名（如：shop.shikejk.com）
            account: 登录账号
            password: 登录密码

        Raises:
            Exception: 登录失败时抛出异常
        """
        self.base_url = f"https://{domain}"
        self.session = requests.Session()
        self.token = None

        # 设置默认请求头
        self.session.headers.update({
            'Accept': 'application/json, text/plain, */*',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Sec-Ch-Ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            'Sec-Ch-Ua-Mobile': '?0',
            'Sec-Ch-Ua-Platform': '"Windows"',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'Priority': 'u=1, i',
            'Referer': f'{self.base_url}/admin/product/product_list'
        })

        # 自动登录
        self._auto_login(account, password)

    def _auto_login(self, account: str, password: str):
        """自动登录获取认证令牌

        Args:
            account: 账号
            password: 密码

        Raises:
            Exception: 登录失败时抛出异常
        """
        url = f"{self.base_url}/adminapi/login"

        # 设置登录请求的特殊请求头
        login_headers = {
            'Content-Type': 'application/json;charset=UTF-8',
            'Origin': self.base_url,
            'Referer': f'{self.base_url}/admin/login'
        }

        # 请求数据
        login_data = {
            "account": account,
            "pwd": password,
            "captchaType": "",
            "captchaVerification": ""
        }

        try:
            response = self.session.post(url, json=login_data, headers=login_headers)
            response.raise_for_status()

            result = response.json()

            # 检查登录结果
            if result.get('status') == 200 and 'data' in result:
                data = result['data']
                if 'token' in data:
                    # 设置认证令牌
                    self.token = data['token']
                    self.session.headers['Authori-zation'] = f'Bearer {self.token}'
                    logger.debug(f"CRMEB登录成功: {account}")
                else:
                    raise Exception("登录响应中未找到token")
            else:
                raise Exception(f"登录失败: {result.get('msg', '未知错误')}")

        except requests.RequestException as e:
            raise Exception(f"登录请求失败: {e}")

    def get_product_attr_value_info(self, product_id: int, info_type: int = 2) -> Dict[str, Any]:
        """获取商品属性值（SKU信息）

        Args:
            product_id: 商品ID
            info_type: 信息类型，默认为2

        Returns:
            Dict[str, Any]: 商品属性值数据（attrValue部分）

        Raises:
            requests.RequestException: 请求失败时抛出
        """
        url = f"{self.base_url}/adminapi/product/other_info/{product_id}/{info_type}"

        try:
            response = self.session.get(url)
            response.raise_for_status()

            # 获取完整响应数据
            result = response.json()

            # 提取attrValue部分
            if result.get('status') == 200 and 'data' in result:
                data = result['data']
                attr_value = data.get('attrValue', {})
                return attr_value
            else:
                raise requests.RequestException(f"API返回错误: {result.get('msg', '未知错误')}")

        except requests.RequestException as e:
            raise requests.RequestException(f"获取商品属性值失败: {e}")

    def get_product_full_info(self, product_id: int, info_type: int = 2) -> Dict[str, Any]:
        """获取商品完整信息（包含storeInfo、level_list、attrValue等）

        Args:
            product_id: 商品ID
            info_type: 信息类型，默认为2

        Returns:
            Dict[str, Any]: 商品完整信息数据

        Raises:
            requests.RequestException: 请求失败时抛出
        """
        url = f"{self.base_url}/adminapi/product/other_info/{product_id}/{info_type}"

        try:
            response = self.session.get(url)
            response.raise_for_status()

            # 获取完整响应数据
            result = response.json()

            # 返回完整的data部分
            if result.get('status') == 200 and 'data' in result:
                return result['data']
            else:
                raise requests.RequestException(f"API返回错误: {result.get('msg', '未知错误')}")

        except requests.RequestException as e:
            raise requests.RequestException(f"获取商品完整信息失败: {e}")

    def get_product_price_dict(self, product_id: int, info_type: int = 2) -> Dict[str, str]:
        """获取商品价格字典

        Args:
            product_id: 商品ID
            info_type: 信息类型，默认为2

        Returns:
            Dict[str, str]: 价格字典，格式为 {code: price}

        Raises:
            requests.RequestException: 请求失败时抛出

        Example:
            price_dict = crmeb.get_product_price_dict(1270)
            # 返回: {
            #     "6971787074178": "6.60",
            #     "6971787079418": "6.60",
            #     "6971787074116": "11.00",
            #     ...
            # }
        """
        try:
            # 获取商品属性值
            attr_value = self.get_product_attr_value_info(product_id, info_type)

            # 构建价格字典
            price_dict = {}
            for sku_info in attr_value.values():
                code = sku_info.get('code', '')
                price = sku_info.get('price', '0.00')
                if code:  # 只有当code存在时才添加
                    price_dict[code] = str(price)

            return price_dict

        except Exception as e:
            raise requests.RequestException(f"获取商品价格字典失败: {e}")

    def update_product_attr_value(self, product_id: int, attr_value_list: list, is_vip: int = 0, level_type: int = 2, info_type: int = 2) -> Dict[str, Any]:
        """更新商品属性值（SKU信息）

        Args:
            product_id: 商品ID
            attr_value_list: 属性值列表，包含所有SKU的完整信息
            is_vip: 是否VIP商品，默认为0
            level_type: 等级类型，默认为2
            info_type: 信息类型，默认为2

        Returns:
            Dict[str, Any]: 更新响应数据

        Raises:
            requests.RequestException: 请求失败时抛出

        Example:
            attr_value_list = [
                {
                    "id": 4292,
                    "unique": "46f3661d",
                    "cost": "6.60",
                    "price": "6.60",
                    "stock": 873,
                    "code": "6971787074178",
                    # ... 其他必需字段
                },
                # ... 其他SKU
            ]
            result = crmeb.update_product_attr_value(1270, attr_value_list)
        """
        url = f"{self.base_url}/adminapi/product/other_update/{product_id}/{info_type}"

        # 构建请求数据
        update_data = {
            "is_vip": is_vip,
            "level_type": level_type,
            "attr_value": attr_value_list
        }

        # 设置请求头
        headers = {
            'Content-Type': 'application/json;charset=UTF-8'
        }

        try:
            response = self.session.post(url, json=update_data, headers=headers)
            response.raise_for_status()

            # 获取响应数据
            result = response.json()

            # 检查更新结果
            if result.get('status') == 200:
                return result
            else:
                raise requests.RequestException(f"API返回错误: {result.get('msg', '未知错误')}")

        except requests.RequestException as e:
            raise requests.RequestException(f"更新商品属性值失败: {e}")

    def update_product_prices(self, product_id: int, price_updates: Dict[str, str], info_type: int = 2) -> Dict[str, Any]:
        """批量更新商品价格

        Args:
            product_id: 商品ID
            price_updates: 价格更新字典，格式为 {code: new_price}
            info_type: 信息类型，默认为2

        Returns:
            Dict[str, Any]: 更新响应数据

        Raises:
            requests.RequestException: 请求失败时抛出

        Example:
            price_updates = {
                "6971787074178": "7.00",  # 新价格
                "6971787079418": "7.50",
                "6971787074116": "12.00"
            }
            result = crmeb.update_product_prices(1270, price_updates)
        """
        try:
            # 先获取当前的属性值
            current_attr_value = self.get_product_attr_value_info(product_id, info_type)

            # 构建更新的属性值列表
            attr_value_list = []
            for sku_info in current_attr_value.values():
                # 复制当前SKU信息
                updated_sku = dict(sku_info)

                # 如果这个SKU的code在更新列表中，则更新价格
                sku_code = sku_info.get('code', '')
                if sku_code in price_updates:
                    new_price = price_updates[sku_code]
                    updated_sku['price'] = str(new_price)

                    # 同时更新ot_price（原价，通常比售价高20%）
                    try:
                        ot_price = float(new_price) * 1.2
                        updated_sku['ot_price'] = f"{ot_price:.2f}"
                    except (ValueError, TypeError):
                        pass  # 如果转换失败，保持原值

                attr_value_list.append(updated_sku)

            # 调用更新方法
            return self.update_product_attr_value(product_id, attr_value_list, info_type=info_type)

        except Exception as e:
            raise requests.RequestException(f"批量更新商品价格失败: {e}")

    def sync_member_prices_with_database(self, product_id: int, database_skus: list, info_type: int = 2) -> Dict[str, Any]:
        """同步会员价格：将数据库SKU的售价设置为所有会员等级的价格

        重要：此方法使用智能匹配算法，确保即使CRMEB内部顺序不一致也能正确同步价格

        Args:
            product_id: 商品ID
            database_skus: 数据库SKU列表，包含sku_code和sale_price字段
            info_type: 信息类型，默认为2

        Returns:
            Dict[str, Any]: 更新响应数据

        Raises:
            requests.RequestException: 请求失败时抛出

        Example:
            database_skus = [
                {'sku_code': '4547691239136', 'sale_price': Decimal('19.50')},
                {'sku_code': '4547691439611', 'sale_price': Decimal('69.50')},
                # ... 其他SKU
            ]
            result = crmeb.sync_member_prices_with_database(1280, database_skus)
        """
        try:
            # 获取当前的CRMEB会员价属性值
            current_attr_value = self.get_product_attr_value_info(product_id, info_type)


            # 创建数据库SKU价格映射字典
            db_price_map = {}
            for sku in database_skus:
                sku_code = sku.get('sku_code', '')
                sale_price = str(sku.get('sale_price', '0.00'))
                if sku_code:
                    db_price_map[sku_code] = sale_price

            # 智能匹配：通过SKU代码精确匹配，忽略顺序问题
            attr_value_list = []
            updated_count = 0
            matched_codes = set()

            # 遍历当前会员价属性值，通过代码匹配更新价格
            for key, sku_info in current_attr_value.items():
                sku_code = sku_info.get('code', '')

                # 复制当前SKU信息
                updated_sku = dict(sku_info)

                # 如果这个SKU在数据库中存在，则同步价格
                if sku_code in db_price_map:
                    db_price = db_price_map[sku_code]
                    old_price = updated_sku.get('price', 0)

                    # 更新主价格
                    updated_sku['price'] = db_price

                    # 更新所有会员等级价格为数据库价格
                    if 'level_price' in updated_sku and isinstance(updated_sku['level_price'], list):
                        for level_price in updated_sku['level_price']:
                            level_price['price'] = db_price
                    updated_count += 1
                    matched_codes.add(sku_code)

                attr_value_list.append(updated_sku)

            # 检查是否有未匹配的数据库SKU
            unmatched_codes = set(db_price_map.keys()) - matched_codes
            if unmatched_codes:
                logger.debug(f"以下数据库SKU在会员价中未找到: {unmatched_codes}")

            if updated_count == 0:
                return {"status": 200, "msg": "没有需要同步的SKU"}

            # 调用更新方法
            result = self.update_product_attr_value(product_id, attr_value_list, info_type=info_type)


            return result

        except Exception as e:
            raise requests.RequestException(f"同步会员价格失败: {e}")

    def get_product_detail(self, product_id: int) -> dict:
        """获取商品详情信息

        Args:
            product_id: 商品ID

        Returns:
            dict: 商品详情数据

        Raises:
            requests.RequestException: 请求失败时抛出
        """
        url = f"{self.base_url}/adminapi/product/product/{product_id}"

        try:
            response = self.session.get(url)
            response.raise_for_status()

            result = response.json()

            if result.get('status') == 200 and 'data' in result:
                return result['data']
            else:
                raise requests.RequestException(f"API返回错误: {result.get('msg', '未知错误')}")

        except requests.RequestException as e:
            raise requests.RequestException(f"获取商品详情失败: {e}")

    def delete_product(self, product_id: int) -> bool:
        """删除商品（移动到回收站）

        Args:
            product_id: 商品ID

        Returns:
            bool: 删除成功返回True，失败返回False

        Raises:
            requests.RequestException: 请求失败时抛出
        """
        url = f"{self.base_url}/adminapi/product/product/{product_id}"

        try:
            response = self.session.delete(url)
            response.raise_for_status()

            # CRMEB删除接口通常返回200状态码表示成功
            if response.status_code == 200:
                return True
            else:
                return False

        except requests.RequestException as e:
            raise requests.RequestException(f"删除商品 {product_id} 失败: {e}")

skcrmeb_config = G.skcrmeb
crmeb_product_manager = CrmebProductManager(skcrmeb_config)

# 测试数据（去掉时间字段）
product = {
    'product_code': 'G67DE63AF8886A',
    'brand': '霏慕',
    'category_level1': ['情趣内衣','延时修护'],
    'category_level2': ['性感睡裙'],
    'title': '法式纯欲含钢圈胸垫睡裙5003 法式清冷感纯欲蕾丝外袍5004',
    'description': '',
    'main_images': [
        'https://qiniu.shikejk.com/products/G67DE63AF8886A/main//主图_00_216361.jpg',
        'https://qiniu.shikejk.com/products/G67DE63AF8886A/main//主图_00_1_151577.jpg'
    ],
    'detail_images': [
        'https://qiniu.shikejk.com/products/G67DE63AF8886A/detail//详情_01_539869.jpg',
        'https://qiniu.shikejk.com/products/G67DE63AF8886A/detail//详情_02_379134.jpg'
    ],
    'source': '伊性坊',
    'product_status': '正常',
    'skus': [
        {
            'sku_code': '6977890235024',
            'product_code': 'G67DE63AF8886A',
            'spec_value': '◆心醉冷香 5003蓝色◆',
            'sub_spec_value': '【单件睡裙】',
            'spec_image': 'https://qiniu.shikejk.com/products/G67DE63AF8886A/spec//6977890235024.jpg',
            'sale_price': Decimal('62.10'),
            'cost_price': Decimal('37.00'),
            'weight': Decimal('134.000'),
            'stock_quantity': 59
        },
        {
            'sku_code': '6977890235031',
            'product_code': 'G67DE63AF8886A',
            'spec_value': '◆心醉冷香 5004蓝色◆',
            'sub_spec_value': '【单件睡袍】',
            'spec_image': 'https://qiniu.shikejk.com/products/G67DE63AF8886A/spec//6977890235031.jpg',
            'sale_price': Decimal('71.10'),
            'cost_price': Decimal('41.50'),
            'weight': Decimal('134.000'),
            'stock_quantity': 46
        },
        {
            'sku_code': 'NY1SYT003397',
            'product_code': 'G67DE63AF8886A',
            'spec_value': '◆心醉冷香 蓝色 ◆ +睡袍◆',
            'sub_spec_value': '【单件睡裙】',
            'spec_image': 'https://qiniu.shikejk.com/products/G67DE63AF8886A/spec//NY1SYT003397.jpg',
            'sale_price': Decimal('133.20'),
            'cost_price': Decimal('78.50'),
            'weight': Decimal('134.000'),
            'stock_quantity': 43
        }
    ]
}