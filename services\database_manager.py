#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
数据库管理器 - 重构版本
功能：提供符合Pythonic风格的数据库操作接口
"""

import sys
import os
import pymysql
import yaml
import json
from typing import Dict, List, Optional, Any, Union
from pathlib import Path
from contextlib import contextmanager
sys.path.insert(0, str(Path(__file__).parent.parent))
# 导入字段转换器
from services.field_converter import convert_for_database, convert_from_database


class DatabaseManager:
    """数据库管理器 - 单例模式"""
    
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        if not self._initialized:
            self._connection = None
            self._config = self._load_config()
            self._initialized = True
    
    @staticmethod
    def _load_config() -> Dict:
        """加载数据库配置"""
        config_path = Path(__file__).parent.parent / "config" / "global.yaml"
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            return config['database']
        except Exception as e:
            raise RuntimeError(f"数据库配置加载失败: {e}")
    
    def _get_connection(self):
        """获取数据库连接"""
        if self._connection is None or not self._connection.open:
            self._connection = pymysql.connect(
                host=self._config['host'],
                port=self._config['port'],
                user=self._config['username'],
                password=self._config['password'],
                database=self._config['database'],
                charset='utf8mb4',
                autocommit=False,
                cursorclass=pymysql.cursors.DictCursor
            )

        return self._connection
    
    @contextmanager
    def _get_cursor(self):
        """获取数据库游标"""
        connection = self._get_connection()
        cursor = connection.cursor()
        try:
            yield cursor
        finally:
            cursor.close()
    
    def execute_query(self, sql: str, params: tuple = None) -> List[Dict]:
        """执行查询语句"""
        with self._get_cursor() as cursor:
            cursor.execute(sql, params)
            results = cursor.fetchall()
            return results
    
    def execute_update(self, sql: str, params: tuple = None) -> int:
        """执行更新语句"""
        connection = self._get_connection()
        with self._get_cursor() as cursor:
            affected_rows = cursor.execute(sql, params)
            connection.commit()
            return affected_rows
    
    def close(self):
        """关闭数据库连接"""
        if self._connection and self._connection.open:
            self._connection.close()
            self._connection = None


class ProductManager:
    """商品管理器"""

    def __init__(self):
        self._db = DatabaseManager()

    # ==================== 私有方法 ====================

    def _normalize_nan_value(self, value):
        """统一NaN值处理：将所有形式的NaN转为空字符串"""
        if value is None:
            return ''
        elif isinstance(value, float):
            import math
            if math.isnan(value):
                return ''
            else:
                return value
        elif isinstance(value, str) and value.strip().lower() in ['nan', 'na', '<na>', 'none', 'null']:
            return ''
        else:
            # 检查pandas NA
            try:
                import pandas as pd
                if pd.isna(value):
                    return ''
                else:
                    return value
            except (ImportError, TypeError):
                return value

    def _deep_equal(self, obj1, obj2):
        """深度比较两个对象是否相等，避免数组比较问题"""
        try:
            if type(obj1) != type(obj2):
                return False

            if isinstance(obj1, list):
                if len(obj1) != len(obj2):
                    return False
                return all(self._deep_equal(a, b) for a, b in zip(obj1, obj2))

            if isinstance(obj1, dict):
                if set(obj1.keys()) != set(obj2.keys()):
                    return False
                return all(self._deep_equal(obj1[k], obj2[k]) for k in obj1.keys())

            return obj1 == obj2
        except Exception:
            return False

    @staticmethod
    def _parse_list_fields(product: Dict) -> Dict:
        """解析列表字段（从数据库字符串转为列表）"""
        return convert_from_database(product)

    def _validate_product_create_data(self, product_data: Dict) -> None:
        """验证商品创建数据"""
        if not product_data.get('product_code'):
            raise ValueError(f"商品编号不能为空")

        # 验证必需字段
        required_fields = ['product_code', 'title']
        missing_fields = [field for field in required_fields if not product_data.get(field)]
        if missing_fields:
            raise ValueError(f"缺少必需字段: {missing_fields}")

        # 验证枚举字段
        self._validate_product_enum_fields(product_data)

    def _validate_product_enum_fields(self, product_data: Dict) -> None:
        """验证商品枚举字段"""
        if 'product_status' in product_data:
            valid_statuses = ['正常', '部分缺货', '全部缺货', '已下架']
            if product_data['product_status'] not in valid_statuses:
                raise ValueError(f"商品状态无效: {product_data['product_status']}, 必须是{valid_statuses}")

        if 'source' in product_data:
            valid_sources = ['伊性坊', '自有', '梦瑶']
            if product_data['source'] not in valid_sources:
                raise ValueError(f"商品来源无效: {product_data['source']}, 必须是{valid_sources}")

    def _prepare_product_data(self, product_data: Dict) -> Dict:
        """准备商品数据（处理列表字段和默认值）"""
        # 使用字段转换器处理列表字段
        processed_data = convert_for_database(product_data)

        # 设置默认值
        if 'product_status' not in processed_data:
            processed_data['product_status'] = '已下架'
        if 'source' not in processed_data:
            processed_data['source'] = '伊性坊'

        return processed_data

    def _insert_product(self, processed_data: Dict) -> None:
        """插入商品数据到数据库"""
        fields = list(processed_data.keys())
        placeholders = ', '.join(['%s'] * len(fields))
        field_names = ', '.join(fields)

        sql = f"INSERT INTO products ({field_names}) VALUES ({placeholders})"

        # 统一NaN处理：所有NaN值转为空字符串
        params = []
        for field in fields:
            value = processed_data[field]
            params.append(self._normalize_nan_value(value))

        self._db.execute_update(sql, tuple(params))

    def _calculate_statistics(self, skus: List[Dict]) -> Dict:
        """计算商品统计信息"""
        if not skus:
            return {
                'min_price': 0,
                'max_price': 0,
                'total_stock': 0,
                'active_sku_count': 0
            }

        prices = [float(sku['sale_price']) for sku in skus]
        return {
            'min_price': min(prices),
            'max_price': max(prices),
            'total_stock': sum(sku['stock_quantity'] for sku in skus),
            'active_sku_count': len([sku for sku in skus if sku['sku_status'] == '正常'])
        }
    
    def _validate_product_update_params(self, product_code: str, update_data: Dict[str, Any]) -> None:
        """验证更新参数"""
        if not product_code:
            raise ValueError(f"商品编号不能为空")

        if not update_data:
            raise ValueError(f"更新数据不能为空")

        if not self.product_exists(product_code):
            raise RuntimeError(f"商品不存在: {product_code}")

    def _validate_product_update_fields(self, update_data: Dict[str, Any]) -> None:
        """验证更新字段"""
        allowed_fields = {
            'brand', 'category_level1', 'category_level2', 'title', 'description',
            'main_images', 'detail_images', 'source', 'source_url',
            'image_package_url', 'product_status'
        }

        # 检查字段是否允许更新
        invalid_fields = set(update_data.keys()) - allowed_fields
        if invalid_fields:
            raise ValueError(f"不允许更新的字段: {invalid_fields}")

        # 验证枚举字段
        self._validate_product_enum_fields(update_data)

    def _prepare_product_update_data(self, update_data: Dict[str, Any]) -> Dict[str, Any]:
        """准备更新数据（处理列表字段）"""
        # 使用字段转换器处理列表字段
        return convert_for_database(update_data)

    def _execute_product_update(self, product_code: str, processed_data: Dict[str, Any]) -> None:
        """执行商品更新"""
        set_clauses = []
        params = []

        for field, value in processed_data.items():
            set_clauses.append(f"{field} = %s")
            # 统一NaN处理
            params.append(self._normalize_nan_value(value))

        params.append(product_code)

        sql = f"UPDATE products SET {', '.join(set_clauses)} WHERE product_code = %s"
        self._db.execute_update(sql, tuple(params))

    # ==================== 公有方法 ====================

    def create_product(self, product_data: Dict) -> bool:
        """创建商品（如果包含SKU数据则同时创建SKU）"""
        product_code = product_data.get('product_code', 'Unknown')

        # 提取SKU数据，避免传入商品表
        skus = product_data.pop('skus', None)

        self._validate_product_create_data(product_data)
        processed_data = self._prepare_product_data(product_data)

        try:
            self._insert_product(processed_data)

            # 检查是否有SKU数据需要创建
            if skus and isinstance(skus, list) and len(skus) > 0:
                sku_manager.create_sku(product_code, skus)

            return True

        except Exception as e:
            raise RuntimeError(f"创建商品失败: {e}")

    def get_product_by_code(self, product_code: str, include_skus: bool = False) -> Optional[Dict]:
        """根据商品编号获取商品"""
        if not product_code:
            return None

        sql = "SELECT * FROM products WHERE product_code = %s"
        results = self._db.execute_query(sql, (product_code,))
        if not results:
            return None

        product = self._parse_list_fields(results[0])

        if include_skus:
            # 获取SKU数据
            skus = sku_manager.get_skus_by_product(product_code)
            product['skus'] = skus
            product['sku_count'] = len(skus)
            product.update(self._calculate_statistics(skus))

        return product
    
    def update_product(self, product_data: Dict[str, Any]) -> bool:
        """更新商品数据（支持完整商品数据字典）

        智能更新逻辑：
        - 只更新传入字典中包含的字段（增量更新，非覆盖）
        - 如果包含 'skus' 字段则更新SKU数据，不包含则不更新
        - product_code 作为标识符，不参与字段更新

        Args:
            product_data: 商品数据字典，必须包含product_code，其他字段按需包含

        Returns:
            bool: 更新成功返回True

        Raises:
            RuntimeError: 更新失败时抛出

        Example:
            # 只更新商品状态
            product_manager.update_product({
                'product_code': 'P001',
                'product_status': '正常'
            })

            # 更新商品状态和SKU数据
            product_manager.update_product({
                'product_code': 'P001',
                'product_status': '正常',
                'skus': [...]
            })
        """
        # 提取商品编号
        product_code = product_data.get('product_code')
        if not product_code:
            raise ValueError("商品数据中缺少product_code字段")

        # 检查是否包含SKU数据
        has_skus = 'skus' in product_data
        skus_data = product_data.get('skus', []) if has_skus else None

        # 定义不参与更新的字段
        excluded_fields = {
            'product_code',  # 标识符，不更新
            'created_at', 'updated_at',  # 系统时间字段
            'sku_count', 'min_price', 'max_price', 'total_stock', 'active_sku_count'  # 统计字段
        }

        # 准备商品基本信息更新数据（排除系统字段和统计字段）
        update_data = {k: v for k, v in product_data.items()
                      if k not in excluded_fields}

        # 如果包含skus字段，从商品更新数据中移除（SKU单独处理）
        if has_skus:
            update_data.pop('skus', None)

        try:
            # 更新商品基本信息（只有传入的字段才会被更新）
            if update_data:
                self._validate_product_update_params(product_code, update_data)
                self._validate_product_update_fields(update_data)
                processed_data = self._prepare_product_update_data(update_data)
                self._execute_product_update(product_code, processed_data)

            # 更新SKU数据（只有明确包含skus字段时才更新）
            if has_skus and skus_data:
                sku_manager.update_skus(product_code, skus_data)

            return True

        except Exception as e:
            raise RuntimeError(f"更新商品失败: {e}")

    def update_product_basic(self, product_code: str, update_data: Dict[str, Any]) -> bool:
        """更新商品基本数据（原有方法，保持向后兼容）

        Args:
            product_code: 商品编号
            update_data: 更新数据字典

        Returns:
            bool: 更新成功返回True

        Raises:
            RuntimeError: 更新失败时抛出
        """
        # 移除不允许更新的字段
        if 'product_code' in update_data:
            update_data.pop('product_code')
        if 'skus' in update_data:
            update_data.pop('skus')

        self._validate_product_update_params(product_code, update_data)
        self._validate_product_update_fields(update_data)
        processed_data = self._prepare_product_update_data(update_data)

        try:
            self._execute_product_update(product_code, processed_data)
            return True

        except Exception as e:
            raise RuntimeError(f"更新商品失败: {e}")

    def delete_product(self, product_code: str) -> bool:
        """删除商品（连同SKU和店铺关联一起删除）"""
        if not product_code:
            raise ValueError(f"商品编号不能为空")

        # 先检查商品是否存在
        if not self.product_exists(product_code):
            raise RuntimeError(f"商品不存在: {product_code}")

        try:
            # 1. 先删除shop_products表中的所有关联记录
            shop_products_sql = "DELETE FROM shop_products WHERE product_code = %s"
            shop_affected = self._db.execute_update(shop_products_sql, (product_code,))
            print(f"删除店铺关联记录: {shop_affected} 条")

            # 2. 删除商品（由于数据库设置了外键约束 ON DELETE CASCADE，会自动删除相关的SKU）
            product_sql = "DELETE FROM products WHERE product_code = %s"
            product_affected = self._db.execute_update(product_sql, (product_code,))

            if product_affected == 0:
                raise RuntimeError(f"删除商品失败，未知错误: {product_code}")

            print(f"删除商品成功: {product_code}，同时删除了 {shop_affected} 条店铺关联记录")
            return True

        except Exception as e:
            raise RuntimeError(f"删除商品失败: {e}")

    def product_exists(self, product_code: str) -> bool:
        """检查商品是否存在"""
        return self.get_product_by_code(product_code) is not None

    def get_all_products(self, include_skus: bool = False) -> List[Dict]:
        """获取所有商品数据"""
        try:
            sql = "SELECT * FROM products ORDER BY created_at DESC"
            products = self._db.execute_query(sql)

            result = []
            for product in products:
                product = self._parse_list_fields(product)

                if include_skus:
                    # 获取SKU数据
                    skus = sku_manager.get_skus_by_product(product['product_code'])
                    product['skus'] = skus
                    product['sku_count'] = len(skus)
                    product.update(self._calculate_statistics(skus))

                result.append(product)

            return result

        except Exception as e:
            raise RuntimeError(f"获取所有商品失败: {e}")

    def get_products_by_filter(self, filters: Dict[str, Any] = None, include_skus: bool = False) -> List[Dict]:
        """根据条件过滤获取商品数据（支持JSON字段智能查询）

        Args:
            filters: 过滤条件，字典格式，键为字段名，值为过滤值或列表
            include_skus: 是否包含SKU数据

        Returns:
            List[Dict]: 商品数据列表

        Example:
            filters = {
                'product_code': 'P001',
                'product_status': ['正常', '部分缺货'],
                'category_level1': '情趣内衣',  # 支持JSON字段查询
                'category_level2': ['性感睡衣', '角色扮演']  # 支持JSON数组查询
            }
            products = product_manager.get_products_by_filter(filters, include_skus=True)
        """


        try:
            # 构建WHERE条件
            where_conditions = []
            params = []

            if filters:
                for field, value in filters.items():
                    if value is not None:
                        # 检查是否为列表字段（现在存储为逗号分隔字符串）
                        if field in ['category_level1', 'category_level2', 'main_images', 'detail_images']:
                            condition, field_params = self._build_string_list_condition(field, value)
                            where_conditions.append(condition)
                            params.extend(field_params)
                        elif isinstance(value, list):
                            # 处理IN查询
                            placeholders = ','.join(['%s'] * len(value))
                            where_conditions.append(f"{field} IN ({placeholders})")
                            params.extend(value)
                        else:
                            where_conditions.append(f"{field} = %s")
                            params.append(value)

            # 构建SQL
            sql = "SELECT * FROM products"
            if where_conditions:
                sql += " WHERE " + " AND ".join(where_conditions)
            sql += " ORDER BY created_at DESC"

            products = self._db.execute_query(sql, tuple(params))

            result = []
            for product in products:
                product = self._parse_list_fields(product)

                if include_skus:
                    # 获取SKU数据
                    skus = sku_manager.get_skus_by_product(product['product_code'])
                    product['skus'] = skus
                    product['sku_count'] = len(skus)
                    product.update(self._calculate_statistics(skus))

                result.append(product)

            return result

        except Exception as e:
            raise RuntimeError(f"按条件获取商品失败: {e}")

    def _build_string_list_condition(self, field: str, value) -> tuple:
        """构建字符串列表字段的查询条件（逗号分隔）

        Args:
            field: 字段名
            value: 查询值，可以是字符串或列表

        Returns:
            tuple: (condition_string, params_list)
        """
        if isinstance(value, str):
            # 单个值查询：精确匹配或包含在逗号分隔列表中
            condition = f"({field} = %s OR {field} LIKE %s OR {field} LIKE %s OR {field} LIKE %s)"
            params = [value, f"{value},%", f"%,{value}", f"%,{value},%"]
            return condition, params
        elif isinstance(value, list):
            # 多个值查询：查询包含列表中任一元素的记录
            sub_conditions = []
            params = []
            for val in value:
                sub_conditions.append(f"({field} = %s OR {field} LIKE %s OR {field} LIKE %s OR {field} LIKE %s)")
                params.extend([val, f"{val},%", f"%,{val}", f"%,{val},%"])
            condition = f"({' OR '.join(sub_conditions)})"
            return condition, params
        else:
            # 其他类型转为字符串处理
            str_value = str(value)
            condition = f"({field} = %s OR {field} LIKE %s OR {field} LIKE %s OR {field} LIKE %s)"
            params = [str_value, f"{str_value},%", f"%,{str_value}", f"%,{str_value},%"]
            return condition, params


class SKUManager:
    """SKU管理器"""

    def __init__(self):
        self._db = DatabaseManager()

    # ==================== 私有方法 ====================

    def create_sku(self, product_code: str, sku_data: Union[Dict, List[Dict]]) -> bool:
        """创建SKU（支持单个或批量创建）"""
        sku_list = self._normalize_sku_data(sku_data)

        self._validate_sku_create_params(product_code, sku_data)

        try:
            for sku in sku_list:
                prepared_sku = self._prepare_sku_data(product_code, sku)
                self._insert_sku(prepared_sku)

            return True

        except Exception as e:
            raise RuntimeError(f"创建SKU失败: {e}")

    def _validate_sku_create_params(self, product_code: str, sku_data: Union[Dict, List[Dict]]) -> None:
        """验证SKU创建参数"""
        if not product_code:
            raise ValueError(f"商品编号不能为空")

        if not product_manager.product_exists(product_code):
            raise RuntimeError(f"商品编号不存在: {product_code}")

        # 统一处理为列表后检查
        sku_list = [sku_data] if isinstance(sku_data, dict) else sku_data
        if not sku_list:
            raise ValueError(f"SKU数据不能为空")

    def _normalize_sku_data(self, sku_data: Union[Dict, List[Dict]]) -> List[Dict]:
        """标准化SKU数据为列表"""
        return [sku_data] if isinstance(sku_data, dict) else sku_data

    def _prepare_sku_data(self, product_code: str, sku: Dict) -> Dict:
        """准备单个SKU数据"""
        # 确保每个SKU都有商品编号
        sku['product_code'] = product_code

        # 验证必需字段
        if not sku.get('sku_code'):
            raise ValueError(f"SKU编号不能为空: {sku}")
        if not sku.get('sale_price'):
            raise ValueError(f"销售价格不能为空: {sku}")

        # 验证枚举字段
        if 'sku_status' in sku:
            valid_statuses = ['正常', '缺货', '已下架']
            if sku['sku_status'] not in valid_statuses:
                raise ValueError(f"SKU状态无效: {sku['sku_status']}, 必须是{valid_statuses}")

        # 设置默认值
        if 'sku_status' not in sku:
            sku['sku_status'] = '正常'
        if 'stock_quantity' not in sku:
            sku['stock_quantity'] = 0

        return sku

    def _insert_sku(self, sku: Dict) -> None:
        """插入单个SKU到数据库"""
        # 获取表的实际字段
        table_fields = self._get_sku_table_fields()

        # 过滤掉不存在的字段
        filtered_sku = {}
        for field, value in sku.items():
            if field in table_fields:
                filtered_sku[field] = self._normalize_nan_value(value)

        fields = list(filtered_sku.keys())
        placeholders = ', '.join(['%s'] * len(fields))
        field_names = ', '.join(fields)

        sql = f"INSERT INTO product_skus ({field_names}) VALUES ({placeholders})"
        params = [filtered_sku[field] for field in fields]

        self._db.execute_update(sql, tuple(params))

    def _get_sku_table_fields(self) -> List[str]:
        """获取SKU表的实际字段列表"""
        try:
            results = self._db.execute_query("DESCRIBE product_skus")
            return [field['Field'] for field in results]
        except Exception:
            # 返回基础字段作为后备
            return [
                'sku_code', 'product_code', 'spec_value', 'sub_spec_value',
                'spec_image', 'sale_price', 'cost_price', 'weight',
                'stock_quantity', 'sku_status'
            ]

    def _normalize_nan_value(self, value):
        """统一NaN值处理：将所有形式的NaN转为空字符串或0"""
        if value is None:
            return ''
        elif isinstance(value, float):
            import math
            if math.isnan(value):
                return 0  # 对于数值字段，NaN转为0
            else:
                return value
        elif isinstance(value, str) and value.strip().lower() in ['nan', 'na', '<na>', 'none', 'null']:
            return ''
        else:
            # 检查pandas NA
            try:
                import pandas as pd
                if pd.isna(value):
                    return ''
                else:
                    return value
            except (ImportError, TypeError):
                return value

    def _validate_sku_update_params(self, sku_code: str, update_data: Dict[str, Any]) -> None:
        """验证SKU更新参数"""
        if not sku_code:
            raise ValueError(f"SKU编号不能为空")

        if not update_data:
            raise ValueError(f"更新数据不能为空")

        # 检查SKU是否存在
        if not self.get_sku_by_code(sku_code):
            raise RuntimeError(f"SKU不存在: {sku_code}")

    def _validate_sku_update_fields(self, update_data: Dict[str, Any]) -> None:
        """验证SKU更新字段"""
        allowed_fields = {
            'spec_value', 'sub_spec_value', 'spec_image', 'sale_price',
            'cost_price', 'weight', 'stock_quantity', 'sku_status'
        }

        # 自动过滤掉不允许更新的字段（系统字段）
        forbidden_fields = {
            'sku_code', 'product_code', 'created_at', 'updated_at'
        }

        # 移除禁止更新的字段
        for field in forbidden_fields:
            update_data.pop(field, None)

        # 检查剩余字段是否允许更新
        invalid_fields = set(update_data.keys()) - allowed_fields
        if invalid_fields:
            raise ValueError(f"不允许更新的字段: {invalid_fields}")

        # 在验证字段值之前进行类型转换
        for field, value in update_data.items():
            update_data[field] = self._convert_sku_field_value(field, value)

        # 验证字段值
        self._validate_sku_field_values(update_data)

    def _validate_sku_field_values(self, update_data: Dict[str, Any]) -> None:
        """验证SKU字段值"""
        if 'sku_status' in update_data:
            valid_statuses = ['正常', '缺货', '已下架']
            if update_data['sku_status'] not in valid_statuses:
                raise ValueError(f"SKU状态无效: {update_data['sku_status']}, 必须是{valid_statuses}")

        if 'stock_quantity' in update_data:
            if not isinstance(update_data['stock_quantity'], (int, float)) or update_data['stock_quantity'] < 0:
                raise ValueError(f"库存数量无效: {update_data['stock_quantity']}, 必须是非负数")

        if 'sale_price' in update_data:
            if not isinstance(update_data['sale_price'], (int, float)) or update_data['sale_price'] < 0:
                raise ValueError(f"销售价格无效: {update_data['sale_price']}, 必须是非负数")

        if 'cost_price' in update_data:
            if not isinstance(update_data['cost_price'], (int, float)) or update_data['cost_price'] < 0:
                raise ValueError(f"成本价格无效: {update_data['cost_price']}, 必须是非负数")

    def _execute_sku_update(self, sku_code: str, update_data: Dict[str, Any]) -> None:
        """执行SKU更新"""
        set_clauses = []
        params = []

        for field, value in update_data.items():
            set_clauses.append(f"{field} = %s")
            # 数据在验证阶段已经转换过类型，这里直接使用
            params.append(value)

        params.append(sku_code)

        sql = f"UPDATE product_skus SET {', '.join(set_clauses)} WHERE sku_code = %s"
        self._db.execute_update(sql, tuple(params))

    def _convert_sku_field_value(self, field: str, value: Any) -> Any:
        """转换SKU字段值为正确的数据类型

        Args:
            field: 字段名
            value: 原始值

        Returns:
            Any: 转换后的值
        """
        # 整数字段
        if field in ['stock_quantity']:
            return self._safe_convert_to_int(value)

        # 浮点数字段
        elif field in ['sale_price', 'cost_price', 'weight']:
            return self._safe_convert_to_float(value)

        # 字符串字段
        else:
            return str(value) if value is not None else ''

    def _safe_convert_to_int(self, value: Any) -> int:
        """安全转换为整数"""
        try:
            if isinstance(value, str):
                return int(value)
            elif isinstance(value, (int, float)):
                return int(value)
            else:
                return 0
        except (ValueError, TypeError):
            return 0

    def _safe_convert_to_float(self, value: Any) -> float:
        """安全转换为浮点数"""
        try:
            if value is None:
                return 0.0
            elif isinstance(value, str):
                return float(value)
            elif isinstance(value, (int, float)):
                return float(value)
            elif hasattr(value, '__float__'):  # 处理 Decimal 等可转换为 float 的类型
                return float(value)
            else:
                return 0.0
        except (ValueError, TypeError):
            return 0.0

    def _verify_sku_update_result(self, sku_code: str, update_data: Dict[str, Any]) -> None:
        """验证SKU更新结果"""
        updated_sku = self.get_sku_by_code(sku_code)

        for field, expected_value in update_data.items():
            actual_value = updated_sku.get(field)

            # 数值字段类型转换比较
            if field in ['sale_price', 'cost_price', 'weight']:
                if float(actual_value) != float(expected_value):
                    raise RuntimeError(f"更新失败，字段 {field} 最终值不匹配: 期望 {expected_value}, 实际 {actual_value}")
            elif field == 'stock_quantity':
                if int(actual_value) != int(expected_value):
                    raise RuntimeError(f"更新失败，字段 {field} 最终值不匹配: 期望 {expected_value}, 实际 {actual_value}")
            else:
                # 字符串字段直接比较
                if actual_value != expected_value:
                    raise RuntimeError(f"更新失败，字段 {field} 最终值不匹配: 期望 {expected_value}, 实际 {actual_value}")
    def _validate_skus_update_params(self, product_code: str, skus_data: List[Dict[str, Any]]) -> None:
        """验证批量更新参数"""
        if not product_code:
            raise ValueError(f"商品编号不能为空")

        if not skus_data:
            raise ValueError(f"SKU数据不能为空")

        if not product_manager.product_exists(product_code):
            raise RuntimeError(f"商品编号不存在: {product_code}")

    def _update_single_sku_in_batch(self, product_code: str, sku_data: Dict[str, Any]) -> None:
        """在批量更新中处理单个SKU"""
        if not sku_data.get('sku_code'):
            raise ValueError(f"SKU编号不能为空: {sku_data}")

        sku_code = sku_data['sku_code']

        # 验证SKU归属
        self._validate_sku_ownership(sku_code, product_code)

        # 准备更新数据
        update_data = {k: v for k, v in sku_data.items() if k != 'sku_code'}
        if not update_data:
            return  # 没有要更新的字段，跳过

        # 验证字段和值（这里已经包含了类型转换）
        self._validate_batch_sku_update_data(sku_code, update_data)

        # 执行更新
        self._execute_sku_update(sku_code, update_data)

        # 验证结果
        self._verify_batch_sku_update_result(sku_code, update_data)

    def _validate_sku_ownership(self, sku_code: str, product_code: str) -> None:
        """验证SKU归属"""
        existing_sku = self.get_sku_by_code(sku_code)
        if not existing_sku:
            raise RuntimeError(f"SKU不存在: {sku_code}")
        if existing_sku['product_code'] != product_code:
            raise RuntimeError(f"SKU {sku_code} 不属于商品 {product_code}")

    def _validate_batch_sku_update_data(self, sku_code: str, update_data: Dict[str, Any]) -> None:
        """验证批量更新中的SKU数据"""
        allowed_fields = {
            'spec_value', 'sub_spec_value', 'spec_image', 'sale_price',
            'cost_price', 'weight', 'stock_quantity', 'sku_status'
        }

        # 自动过滤掉不允许更新的字段（系统字段）
        forbidden_fields = {
            'sku_code', 'product_code', 'created_at', 'updated_at'
        }

        # 移除禁止更新的字段
        for field in forbidden_fields:
            update_data.pop(field, None)

        # 检查剩余字段是否允许更新
        invalid_fields = set(update_data.keys()) - allowed_fields
        if invalid_fields:
            raise ValueError(f"SKU {sku_code} 不允许更新的字段: {invalid_fields}")

        # 在验证字段值之前进行类型转换
        for field, value in update_data.items():
            update_data[field] = self._convert_sku_field_value(field, value)

        # 验证字段值（带SKU编号的错误信息）
        self._validate_batch_sku_field_values(sku_code, update_data)

    def _validate_batch_sku_field_values(self, sku_code: str, update_data: Dict[str, Any]) -> None:
        """验证批量更新中的SKU字段值"""
        if 'sku_status' in update_data:
            valid_statuses = ['正常', '缺货', '已下架']
            if update_data['sku_status'] not in valid_statuses:
                raise ValueError(f"SKU {sku_code} 状态无效: {update_data['sku_status']}, 必须是{valid_statuses}")

        if 'stock_quantity' in update_data:
            if not isinstance(update_data['stock_quantity'], (int, float)) or update_data['stock_quantity'] < 0:
                raise ValueError(f"SKU {sku_code} 库存数量无效: {update_data['stock_quantity']}, 必须是非负数")

        if 'sale_price' in update_data:
            if not isinstance(update_data['sale_price'], (int, float)) or update_data['sale_price'] < 0:
                raise ValueError(f"SKU {sku_code} 销售价格无效: {update_data['sale_price']}, 必须是非负数")

        if 'cost_price' in update_data:
            if not isinstance(update_data['cost_price'], (int, float)) or update_data['cost_price'] < 0:
                raise ValueError(f"SKU {sku_code} 成本价格无效: {update_data['cost_price']}, 必须是非负数")

    def _verify_batch_sku_update_result(self, sku_code: str, update_data: Dict[str, Any]) -> None:
        """验证批量更新中的SKU结果"""
        updated_sku = self.get_sku_by_code(sku_code)

        for field, expected_value in update_data.items():
            actual_value = updated_sku.get(field)

            # 数值字段类型转换比较
            if field in ['sale_price', 'cost_price', 'weight']:
                if float(actual_value) != float(expected_value):
                    raise RuntimeError(f"更新失败，SKU {sku_code} 字段 {field} 最终值不匹配: 期望 {expected_value}, 实际 {actual_value}")
            elif field == 'stock_quantity':
                if int(actual_value) != int(expected_value):
                    raise RuntimeError(f"更新失败，SKU {sku_code} 字段 {field} 最终值不匹配: 期望 {expected_value}, 实际 {actual_value}")
            else:
                if actual_value != expected_value:
                    raise RuntimeError(f"更新失败，SKU {sku_code} 字段 {field} 最终值不匹配: 期望 {expected_value}, 实际 {actual_value}")

    # ==================== 公有方法 ====================

    def get_skus_by_product(self, product_code: str) -> List[Dict]:
        """获取商品的所有SKU"""
        if not product_code:
            return []

        sql = "SELECT * FROM product_skus WHERE product_code = %s ORDER BY sku_code"
        results = self._db.execute_query(sql, (product_code,))
        return results if results else []

    def get_sku_by_code(self, sku_code: str) -> Optional[Dict]:
        """根据SKU编号获取SKU数据"""
        if not sku_code:
            return None

        sql = "SELECT * FROM product_skus WHERE sku_code = %s"
        results = self._db.execute_query(sql, (sku_code,))
        if results:
            return results[0]
        else:
            return None
    
    def update_sku(self, sku_code: str, update_data: Dict[str, Any]) -> bool:
        """更新SKU数据"""
        self._validate_sku_update_params(sku_code, update_data)
        self._validate_sku_update_fields(update_data)  # 这里已经包含了类型转换

        try:
            self._execute_sku_update(sku_code, update_data)
            self._verify_sku_update_result(sku_code, update_data)
            return True

        except Exception as e:
            raise RuntimeError(f"更新SKU失败: {e}")


    def delete_sku(self, sku_code: str) -> bool:
        """删除SKU"""
        if not sku_code:
            raise ValueError(f"SKU编号不能为空")

        # 先检查SKU是否存在
        if not self.get_sku_by_code(sku_code):
            raise RuntimeError(f"SKU不存在: {sku_code}")

        try:
            sql = "DELETE FROM product_skus WHERE sku_code = %s"
            affected = self._db.execute_update(sql, (sku_code,))

            if affected == 0:
                raise RuntimeError(f"删除SKU失败，未知错误: {sku_code}")

            return True

        except Exception as e:
            raise RuntimeError(f"删除SKU失败: {e}")

    def update_skus(self, product_code: str, skus_data: List[Dict[str, Any]]) -> bool:
        """批量更新SKU（根据商品编号）"""
        self._validate_skus_update_params(product_code, skus_data)

        try:
            for sku_data in skus_data:
                self._update_single_sku_in_batch(product_code, sku_data)
            return True

        except Exception as e:
            raise RuntimeError(f"批量更新SKU失败: {e}")

# 全局实例 - 简化使用
# 操作失败直接抛出异常
product_manager = ProductManager()  # 商品管理器
sku_manager = SKUManager()  # SKU管理器
