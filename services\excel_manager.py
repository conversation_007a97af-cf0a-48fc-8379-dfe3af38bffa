#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Excel数据处理和导出管理器
负责将采集的商品数据格式化并导出到Excel文件
"""

import sys
import os
import yaml
import json
import ast
from pathlib import Path
from typing import Dict, List, Any, Tuple
from decimal import Decimal, ROUND_HALF_UP
import pandas as pd

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))
from services import module
from services.field_converter import list_to_string, convert_from_excel

class ExcelManager:
    """
    Excel数据处理和导出管理器

    这个类提供了完整的商品数据处理流程，包括：
    - 品牌标准化和映射
    - 标题清理和美化
    - 自动分类处理
    - SKU规格处理
    - VIP利润计算
    - Excel文件读写操作
    """

    # 类常量定义
    CONFIG_PATH = Path(__file__).parent.parent / "config" / "global.yaml"

    def __init__(self, file_path: str, sheet_name: str = None, debug_vip_calculation: bool = False):
        """
        初始化Excel管理器

        Args:
            file_path: Excel文件路径
            sheet_name: 工作表名称，默认为"商品数据"
            debug_vip_calculation: 是否显示VIP计算的详细调试信息
        """
        self.file_path = Path(file_path)
        self.debug_vip_calculation = debug_vip_calculation

        # 延迟加载配置，使用属性访问
        self._config_cache = None

        # 设置工作表名称，避免循环依赖
        if sheet_name is None:
            # 先加载配置，再获取默认名称
            config = self.config
            constants = config.get('vip_profit_config', {}).get('constants', {})
            self.sheet_name = constants.get('default_sheet_name', '商品数据')
        else:
            self.sheet_name = sheet_name

    def _normalize_nan_values(self, row_dict):
        """标准化行数据中的NaN值为空字符串"""
        normalized_row = {}
        for key, value in row_dict.items():
            if value is None:
                normalized_row[key] = ''
            elif isinstance(value, float):
                import math
                if math.isnan(value):
                    normalized_row[key] = ''
                else:
                    normalized_row[key] = value
            elif isinstance(value, str) and value.strip().lower() in ['nan', 'na', '<na>', 'none', 'null']:
                normalized_row[key] = ''
            else:
                # 检查pandas NA
                try:
                    import pandas as pd
                    if pd.isna(value):
                        normalized_row[key] = ''
                    else:
                        normalized_row[key] = value
                except (ImportError, TypeError):
                    normalized_row[key] = value
        return normalized_row

    # ==================== 配置管理属性 ====================

    @property
    def config(self) -> Dict[str, Any]:
        """
        统一的配置访问属性，使用缓存机制避免重复读取文件

        Returns:
            Dict[str, Any]: 完整的配置字典
        """
        if self._config_cache is None:
            self._config_cache = self._load_config()
        return self._config_cache

    @property
    def brand_mapping(self) -> Dict[str, List[str]]:
        """品牌映射配置"""
        return self.config.get('brand_mapping', {})

    @property
    def deepseek_config(self) -> Dict[str, str]:
        """DeepSeek AI配置"""
        return self.config.get('deepseek', {})

    @property
    def vip_profit_config(self) -> Dict[str, Any]:
        """VIP利润率配置"""
        return self.config.get('vip_profit_config', {})

    @property
    def constants(self) -> Dict[str, Any]:
        """系统常量配置"""
        return self.vip_profit_config.get('constants', {})

    @property
    def DEFAULT_SHEET_NAME(self) -> str:
        """默认工作表名称"""
        return self.constants.get('default_sheet_name', '商品数据')

    @property
    def DEFAULT_DISCOUNT_RATE(self) -> float:
        """默认折扣率"""
        return self.constants.get('default_discount_rate', 0.9)

    @property
    def DEFAULT_WEIGHT(self) -> float:
        """默认商品重量"""
        return self.constants.get('default_weight', 500.0)

    def _load_config(self) -> Dict[str, Any]:
        """
        统一的配置加载方法

        Returns:
            Dict[str, Any]: 配置字典，加载失败时返回空字典

        Raises:
            FileNotFoundError: 配置文件不存在
            yaml.YAMLError: YAML格式错误
        """
        try:
            with open(self.CONFIG_PATH, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            return config or {}
        except FileNotFoundError:
            return {}
        except yaml.YAMLError as e:
            raise RuntimeError(f"配置文件格式错误: {e}")
        except Exception as e:
            raise RuntimeError(f"配置加载失败: {e}")

    # ==================== 废弃的配置加载方法（保持向后兼容） ====================

    def _load_brand_mapping(self) -> Dict[str, List[str]]:
        """
        加载品牌映射配置（废弃方法，保持向后兼容）

        .. deprecated::
            使用 brand_mapping 属性替代
        """
        brand_mapping = self.brand_mapping
        return brand_mapping



    def _load_deepseek_config(self) -> Dict[str, str]:
        """
        加载DeepSeek AI配置（废弃方法，保持向后兼容）

        .. deprecated::
            使用 deepseek_config 属性替代
        """
        deepseek_config = self.deepseek_config
        return deepseek_config

    def _load_vip_profit_config(self) -> Dict[str, Any]:
        """
        加载VIP利润率配置（废弃方法，保持向后兼容）

        .. deprecated::
            使用 vip_profit_config 属性替代
        """
        vip_profit_config = self.vip_profit_config
        return vip_profit_config

    # ==================== 品牌和标题处理方法 ====================

    def _normalize_brand(self, original_brand: str) -> str:
        """
        标准化品牌名称，根据配置的品牌映射表进行转换

        Args:
            original_brand: 原始品牌名称

        Returns:
            str: 标准化后的品牌名称，如果没有找到映射则返回原始名称

        Example:
            >>> manager._normalize_brand("Magic Motion/魅动")
            "Magic Motion"
        """
        if not original_brand:
            return ""
        
        # 遍历品牌映射表
        for standard_brand, variants in self.brand_mapping.items():
            if original_brand in variants:
                return standard_brand

        # 如果没有找到映射，返回原始品牌名
        return original_brand
    
    def _clean_title(self, title: str, brand: str = '') -> str:
        """
        清理商品标题，去除开头的【】标记和品牌名称

        Args:
            title: 原始标题
            brand: 品牌名称（用于从标题中去除）

        Returns:
            str: 清理后的标题
        """
        if not title:
            return ""

        import re

        # 第一步：去除开头的所有【xxx】格式（支持多个连续的）
        cleaned_title = re.sub(r'^(【[^】]*】\s*)+', '', title)

        # 第二步：如果标题开头包含品牌名称，则去除品牌名称和前面的空格
        if brand and brand.strip():
            brand_clean = brand.strip()
            # 检查标题是否以品牌名称开头（忽略大小写）
            if cleaned_title.lower().startswith(brand_clean.lower()):
                # 去除品牌名称
                cleaned_title = cleaned_title[len(brand_clean):]
                # 去除前面的空格、特殊字符等
                cleaned_title = re.sub(r'^[\s\-_\|\/\\]+', '', cleaned_title)

        return cleaned_title

    def _beautify_title(self, title: str, skus: List[Dict]) -> str:
        """
        使用AI美化商品标题

        Args:
            title: 清理后的商品标题
            skus: SKU列表

        Returns:
            str: 美化后的标题
        """
        if not title or not skus or not self.deepseek_config.get('api_key'):
            return title

        # 构建规格值字符串（只提取spec_value）
        spec_values = []
        for sku in skus:
            spec_value = sku.get('spec_value', '').strip()
            if spec_value:
                spec_values.append(spec_value)

        if not spec_values:
            return title

        # 构建AI输入，最多取前3个规格值避免过长
        spec_text = "，".join(spec_values[:3])
        ai_input = f'原标题：{title}，规格值：{spec_text}'

        # 获取系统提示词并检查
        system_content = self.deepseek_config.get('title_beautify_system_content', '')

        try:
            # 调用DeepSeek AI（使用标题美化功能的系统提示词）
            response = module.deepseek_chat(
                user_content=ai_input,
                model=self.deepseek_config.get('model', 'deepseek-chat'),
                api_key=self.deepseek_config['api_key'],
                system_content=system_content
            )

            if not response:
                return title

            # 验证返回的新标题是否包含清理后的标题
            beautified_title = response.strip()

            # 如果返回的新标题包含清理后的标题，则使用新标题
            if title in beautified_title:
                return beautified_title
            else:
                return title

        except Exception:
            return title

    # ==================== AI处理方法 ====================

    def _auto_classify_product(self, title: str) -> Dict[str, Any]:
        """
        使用AI自动分类商品

        Args:
            title: 原始商品标题（未处理的）

        Returns:
            Dict[str, Any]: 分类结果，包含category_level1和category_level2
        """
        if not title or not self.deepseek_config.get('api_key'):
            return {}

        # 构建AI输入，只使用商品标题
        ai_input = f'商品标题"{title}"'

        try:
            # 调用DeepSeek AI（使用分类功能的系统提示词）
            response = module.deepseek_chat(
                user_content=ai_input,
                model=self.deepseek_config.get('model', 'deepseek-chat'),
                api_key=self.deepseek_config['api_key'],
                system_content=self.deepseek_config.get('classification_system_content', '')
            )

            if not response:
                return {}

            # 解析AI返回的分类结果
            classification = self._parse_classification_result(response)

            if classification:
                return classification
            else:
                return {}

        except Exception:
            return {}

    def _parse_classification_result(self, ai_response: str) -> Dict[str, Any]:
        """
        解析AI返回的分类结果

        Args:
            ai_response: AI返回的字符串

        Returns:
            Dict[str, Any]: 解析后的分类结果
        """
        try:
            # 尝试直接解析JSON格式
            import json
            import re

            # 提取JSON部分（可能包含在其他文本中）
            json_pattern = r'\{[^{}]*"category_level1"[^{}]*\}'
            json_match = re.search(json_pattern, ai_response)

            if json_match:
                json_str = json_match.group()
                classification = json.loads(json_str)

                # 验证必要字段
                if 'category_level1' in classification and 'category_level2' in classification:
                    return classification
                else:
                    return {}
            else:
                return {}

        except json.JSONDecodeError:
            return {}
        except Exception:
            return {}

    # ==================== VIP利润计算方法 ====================

    def _calculate_vip_profit_margin(self, original_price: float, cost_price: float, category_level1: str, weight: float = 0) -> Tuple[float, float, float, float]:
        """
        计算VIP利润和利润率

        Args:
            original_price: 原价
            cost_price: 成本价
            category_level1: 一级分类
            weight: 商品重量（克）

        Returns:
            tuple: (vip_profit, vip_profit_margin, vip_cost_price, vip_sale_price) VIP利润金额、利润率（百分比）、VIP成本价格、VIP实际支付价格
        """
        try:
            if not self.vip_profit_config:
                return 0.0, 0.0, 0.0, 0.0

            # 1. 计算实际支付价格：原价 * 默认折扣率 * vip_discount
            vip_discount = self.vip_profit_config.get('vip_discount', 0.88)
            actual_payment = original_price * self.DEFAULT_DISCOUNT_RATE * vip_discount

            # 2. 计算成本
            # 基础成本价
            total_cost = cost_price

            # 快递费用（根据实际支付金额和重量选择快递方式）
            shipping_cost = self._calculate_shipping_cost_with_weight(actual_payment, weight)
            total_cost += shipping_cost

            # 赠品费用
            gift_cost = self._calculate_gift_cost(actual_payment, category_level1)
            total_cost += gift_cost

            # 人工费用：实际支付价格的5%
            labor_rate = self.vip_profit_config.get('labor_rate', 0.05)
            labor_cost = actual_payment * labor_rate
            total_cost += labor_cost

            # 3. 计算利润和利润率
            if actual_payment <= 0:
                return 0.0, 0.0, 0.0, 0.0

            profit = actual_payment - total_cost
            profit_margin = (profit / actual_payment) * 100

            return round(profit, 2), round(profit_margin, 2), round(total_cost, 2), round(actual_payment, 2)

        except Exception:
            return 0.0, 0.0, 0.0, 0.0

    def _calculate_shipping_cost_with_weight(self, actual_payment: float, weight: float) -> float:
        """
        根据支付金额和重量计算快递费用

        Args:
            actual_payment: 实际支付价格
            weight: 商品重量（克）

        Returns:
            float: 快递费用
        """
        try:
            shipping_config = self.vip_profit_config.get('shipping', {})

            # 1. 检查是否超过3kg，强制使用极兔快递
            jt_config = shipping_config.get('jt', {})
            weight_threshold = jt_config.get('weight_threshold', 3000)

            if weight > weight_threshold:
                # 使用极兔快递
                shipping_cost = self._calculate_jt_shipping_cost(weight, jt_config)
                return shipping_cost

            # 2. 根据支付金额选择普通快递或顺丰快递
            sf_config = shipping_config.get('sf', {})
            sf_threshold = sf_config.get('threshold', 66)

            if actual_payment >= sf_threshold:
                # 使用顺丰快递
                shipping_cost = self._calculate_sf_shipping_cost(weight, sf_config)
            else:
                # 使用普通快递
                normal_config = shipping_config.get('normal', {})
                shipping_cost = self._calculate_normal_shipping_cost(weight, normal_config)

            return shipping_cost

        except Exception:
            return 6.0  # 默认使用顺丰快递费用

    def _calculate_normal_shipping_cost(self, weight: float, config: dict) -> float:
        """计算普通快递费用"""
        base_cost = config.get('base_cost', 2.5)
        additional_cost = config.get('additional_cost', 1)
        base_weight = self.vip_profit_config.get('shipping', {}).get('base_weight', 1000)

        if weight <= base_weight:
            return base_cost

        # 计算超重费用（每超1kg续重1元）
        excess_weight = weight - base_weight
        excess_kg = (excess_weight + 999) // 1000  # 向上取整到kg
        total_cost = base_cost + (excess_kg * additional_cost)

        return total_cost

    def _calculate_sf_shipping_cost(self, weight: float, config: dict) -> float:
        """计算顺丰快递费用"""
        base_cost = config.get('base_cost', 6)
        additional_cost = config.get('additional_cost', 1)
        base_weight = self.vip_profit_config.get('shipping', {}).get('base_weight', 1000)

        if weight <= base_weight:
            return base_cost

        # 计算超重费用（每超1000g续重1元）
        excess_weight = weight - base_weight
        excess_units = (excess_weight + 999) // 1000  # 向上取整到1000g单位
        total_cost = base_cost + (excess_units * additional_cost)

        return total_cost

    def _calculate_jt_shipping_cost(self, weight: float, config: dict) -> float:
        """计算极兔快递费用"""
        base_cost = config.get('base_cost', 2.3)
        additional_cost = config.get('additional_cost', 0.5)

        if weight <= 1000:
            return base_cost

        # 计算超重费用（每超1kg续重0.5元）
        excess_weight = weight - 1000
        excess_kg = (excess_weight + 999) // 1000  # 向上取整到kg
        total_cost = base_cost + (excess_kg * additional_cost)

        return total_cost

    def _calculate_gift_cost(self, actual_payment: float, category_level1: str) -> float:
        """
        计算赠品费用

        Args:
            actual_payment: 实际支付价格
            category_level1: 一级分类

        Returns:
            float: 赠品费用
        """
        try:
            gift_config = self.vip_profit_config.get('gift_prices', {})
            category_config = gift_config.get(category_level1, {})

            # 检查是否启用赠品计算
            if not category_config.get('enabled', False):
                return 0.0

            # 检查是否有价格阈值
            if 'price_threshold' in category_config:
                threshold = category_config['price_threshold']
                if actual_payment < threshold:
                    gift_cost = category_config.get('gift_cost_low', 0)
                else:
                    gift_cost = category_config.get('gift_cost_high', 0)
            else:
                # 固定赠品费用
                gift_cost = category_config.get('gift_cost', 0)

            return float(gift_cost)

        except Exception:
            return 0.0

    def _process_skus_with_ai(self, title: str, skus: List[Dict]) -> List[Tuple[str, str]]:
        """
        使用DeepSeek AI处理SKU规格，生成标准的spec_value和sub_spec_value

        Args:
            title: 商品标题
            skus: SKU列表

        Returns:
            List[Tuple[str, str]]: 处理后的(spec_value, sub_spec_value)列表
        """
        if not skus or not self.deepseek_config.get('api_key'):
            return [(sku.get('spec_value', ''), '') for sku in skus]

        # 构建AI输入格式
        spec_values = [sku.get('spec_value', '') for sku in skus]
        ai_input = f"原标题：{title}\nsku：{json.dumps(spec_values, ensure_ascii=False)}"

        try:
            # 调用DeepSeek AI（使用SKU处理功能的系统提示词）
            ai_response = module.deepseek_chat(
                user_content=ai_input,
                model=self.deepseek_config.get('model', 'deepseek-chat'),
                api_key=self.deepseek_config['api_key'],
                system_content=self.deepseek_config.get('sku_processing_system_content', '')
            )

            if not ai_response:
                return [(spec, '') for spec in spec_values]

            # 解析AI返回的二维列表
            processed_specs = self._parse_ai_response(ai_response, len(skus))

            if processed_specs is None or len(processed_specs) != len(skus):
                return [(spec, '') for spec in spec_values]

            # 对sub_spec_value进行统一处理（少数服从多数）
            unified_specs = self._unify_sub_spec_values(processed_specs)
            return unified_specs

        except Exception:
            return [(spec, '') for spec in spec_values]

    def _unify_sub_spec_values(self, processed_specs):
        """
        统一sub_spec_value值，采用少数服从多数原则

        Args:
            processed_specs: [(spec_value, sub_spec_value), ...] 格式的列表

        Returns:
            统一后的规格列表
        """
        if not processed_specs or len(processed_specs) <= 1:
            return processed_specs

        try:
            # 提取所有sub_spec_value
            sub_spec_values = [sub_spec for _, sub_spec in processed_specs if sub_spec.strip()]

            if not sub_spec_values:
                # 如果没有有效的sub_spec_value，直接返回
                return processed_specs

            # 统计每个sub_spec_value的出现次数
            from collections import Counter
            value_counts = Counter(sub_spec_values)

            # 找出出现次数最多的值
            most_common_value = value_counts.most_common(1)[0][0]

            # 如果所有值都相同，直接返回
            if len(value_counts) == 1:
                return processed_specs

            # 统一所有sub_spec_value为最常见的值
            unified_specs = []

            for spec_value, sub_spec_value in processed_specs:
                if sub_spec_value.strip() and sub_spec_value != most_common_value:
                    unified_specs.append((spec_value, most_common_value))
                else:
                    unified_specs.append((spec_value, sub_spec_value))

            return unified_specs

        except Exception:
            return processed_specs

    def _process_skus_by_category(self, title, skus, classification):
        """
        根据商品分类决定如何处理SKU规格

        Args:
            title: 商品标题
            skus: SKU列表
            classification: 自动分类结果

        Returns:
            处理后的规格列表 [(spec_value, sub_spec_value), ...]
        """
        try:
            # 获取分类信息
            category_level1 = classification.get('category_level1', '')

            # 处理分类为列表的情况
            if isinstance(category_level1, list):
                categories = category_level1
            else:
                categories = [category_level1] if category_level1 else []

            # 根据配置决定如何处理副规格
            sub_spec_value = self._determine_sub_spec_value(categories)

            if sub_spec_value is None:
                # 使用AI处理（情趣内衣等）
                return self._process_skus_with_ai(title, skus)
            else:
                # 使用固定副规格
                processed_specs = []
                for sku in skus:
                    original_spec = sku.get('spec_value', '原始规格')
                    processed_specs.append((original_spec, sub_spec_value))
                return processed_specs

        except Exception:
            processed_specs = []
            for sku in skus:
                original_spec = sku.get('spec_value', '原始规格')
                processed_specs.append((original_spec, ''))
            return processed_specs

    def _determine_sub_spec_value(self, categories):
        """
        根据商品分类确定副规格值

        Args:
            categories: 分类列表

        Returns:
            str or None: 固定的副规格值，None表示使用AI处理
        """
        try:
            # 获取副规格配置（顶级配置）
            sub_spec_config = self.config.get('sub_spec_config', {})

            # 检查优先级规则
            priority_rules = sub_spec_config.get('priority_rules', [])
            for rule in priority_rules:
                rule_categories = rule.get('categories', [])
                # 检查是否有交集
                if any(cat in categories for cat in rule_categories):
                    sub_spec_value = rule.get('sub_spec_value', '')
                    return sub_spec_value

            # 如果没有匹配优先级规则，检查单个分类配置
            for category in categories:
                if category in sub_spec_config:
                    category_config = sub_spec_config[category]
                    if not category_config.get('use_ai_processing', True):
                        sub_spec_value = category_config.get('fixed_value', '')
                        return sub_spec_value

            # 使用默认配置
            default_config = sub_spec_config.get('default', {})
            if not default_config.get('use_ai_processing', True):
                sub_spec_value = default_config.get('fixed_value', '')
                return sub_spec_value

            # 返回None表示使用AI处理
            return None

        except Exception:
            return None

    def _parse_ai_response(self, ai_response: str, expected_count: int) -> List[Tuple[str, str]]:
        """
        解析AI返回的字符串格式二维列表

        Args:
            ai_response: AI返回的字符串
            expected_count: 期望的SKU数量

        Returns:
            List[Tuple[str, str]]: 解析后的(spec_value, sub_spec_value)列表
        """
        try:
            # 尝试直接解析JSON格式
            parsed_data = json.loads(ai_response)
            if isinstance(parsed_data, list) and len(parsed_data) == expected_count:
                result = []
                for item in parsed_data:
                    if isinstance(item, list) and len(item) >= 2:
                        result.append((str(item[0]), str(item[1])))
                    else:
                        raise ValueError("格式不正确")
                return result
        except (json.JSONDecodeError, ValueError):
            pass

        try:
            # 尝试使用ast.literal_eval解析
            parsed_data = ast.literal_eval(ai_response)
            if isinstance(parsed_data, list) and len(parsed_data) == expected_count:
                result = []
                for item in parsed_data:
                    if isinstance(item, list) and len(item) >= 2:
                        result.append((str(item[0]), str(item[1])))
                    else:
                        raise ValueError("格式不正确")
                return result
        except (ValueError, SyntaxError):
            pass

        # 解析失败时返回None，让上层使用原始规格
        return None

    def _calculate_discount_price(self, original_price: str, discount_rate: float = None) -> str:
        """
        计算折扣价格

        Args:
            original_price: 原始价格字符串
            discount_rate: 折扣率，默认使用类常量DEFAULT_DISCOUNT_RATE（9折）

        Returns:
            str: 折扣后的价格字符串，保留2位小数

        Raises:
            ValueError: 当价格格式无效时
        """
        if discount_rate is None:
            discount_rate = self.DEFAULT_DISCOUNT_RATE
        try:
            # 转换为Decimal进行精确计算
            price_decimal = Decimal(str(original_price))
            discount_decimal = Decimal(str(discount_rate))
            
            # 计算折扣价格并四舍五入到2位小数
            discounted_price = price_decimal * discount_decimal
            rounded_price = discounted_price.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
            
            result = str(rounded_price)
            return result

        except Exception:
            return original_price

    # ==================== 主要数据处理方法 ====================

    def format_product_data(self, raw_data: Dict) -> List[Dict]:
        """
        将采集的原始数据格式化为Excel需要的格式

        Args:
            raw_data: 采集到的原始商品数据

        Returns:
            List[Dict]: 格式化后的数据列表，每个SKU一行
        """
        formatted_rows = []

        # 1. 标准化品牌名称（失败时保持原品牌）
        try:
            normalized_brand = self._normalize_brand(raw_data.get('brand', ''))
        except Exception:
            normalized_brand = raw_data.get('brand', '')

        # 2. 清理商品标题（失败时保持原标题）
        try:
            cleaned_title = self._clean_title(raw_data.get('title', ''), normalized_brand)
        except Exception:
            cleaned_title = raw_data.get('title', '')

        # 3. 标题美化（失败时保持清理后的标题）
        try:
            beautified_title = self._beautify_title(cleaned_title, raw_data.get('skus', []))
        except Exception:
            beautified_title = cleaned_title

        # 4. 自动分类商品（失败时使用空分类）
        try:
            auto_classification = self._auto_classify_product(
                raw_data.get('title', '')  # 使用原始标题
            )
        except Exception:
            auto_classification = {'category_level1': '', 'category_level2': ''}

        # 5. 处理每个SKU
        skus = raw_data.get('skus', [])

        # 6. 根据分类决定如何处理SKU规格
        processed_specs = self._process_skus_by_category(beautified_title, skus, auto_classification)

        # 6. 处理每个SKU
        for i, sku in enumerate(skus):
            try:
                spec_value, sub_spec_value = processed_specs[i]

                # 如果规格为空，使用原始规格
                if not spec_value or not spec_value.strip():
                    spec_value = sku.get('spec_value', f'规格{i+1}')

            except (IndexError, Exception):
                # 如果获取处理后规格失败，使用原始规格
                spec_value = sku.get('spec_value', f'规格{i+1}')
                sub_spec_value = ''

            # 7. 计算9折价格（失败时使用原价）
            try:
                discounted_price = self._calculate_discount_price(sku.get('sale_price', '0'))
            except Exception:
                discounted_price = sku.get('sale_price', '0')

            # 8. 安全获取重量，使用默认重量常量
            weight_value = sku.get('weight', str(int(self.DEFAULT_WEIGHT)))
            if not weight_value or weight_value == '':
                weight_value = str(int(self.DEFAULT_WEIGHT))
            try:
                weight_float = float(weight_value)
            except (ValueError, TypeError):
                weight_float = self.DEFAULT_WEIGHT

            # 9. 计算VIP利润、利润率、成本价格和实际支付价格（失败时使用默认值）
            try:
                vip_profit, vip_profit_margin, vip_cost_price, vip_sale_price = self._calculate_vip_profit_margin(
                    float(sku.get('sale_price', 0)),
                    float(sku.get('cost_price', 0)),
                    auto_classification.get('category_level1', ''),
                    weight_float
                )
            except Exception:
                vip_profit = '0.00'
                vip_profit_margin = '0.00'
                vip_cost_price = '0.00'
                vip_sale_price = '0.00'

            # 10. 构建Excel行数据
            try:
                row_data = {
                    'product_code': raw_data.get('product_code', ''),
                    'brand': normalized_brand,
                    'title': beautified_title,
                    'sku_code': sku.get('sku_code', ''),
                    'spec_value': spec_value,
                    'sub_spec_value': sub_spec_value,
                    'original_price': sku.get('sale_price', ''),
                    'sale_price': discounted_price,
                    'cost_price': sku.get('cost_price', ''),
                    'weight': str(int(weight_float)),  # 使用处理后的weight值
                    'stock_quantity': sku.get('stock_quantity', ''),
                    'sku_status': sku.get('sku_status', ''),
                    'source': raw_data.get('source', ''),
                    'source_url': raw_data.get('source_url', ''),
                    'image_package_url': raw_data.get('image_package_url', ''),
                    'description': raw_data.get('description', ''),
                    'product_status': raw_data.get('product_status', ''),
                    # 添加自动分类结果（转换为Excel格式）
                    'category_level1': list_to_string(auto_classification.get('category_level1', ''), separator=','),
                    'category_level2': list_to_string(auto_classification.get('category_level2', ''), separator=','),
                    # 添加图片字段（转换为Excel格式）
                    'main_images': list_to_string(raw_data.get('main_images', []), separator=','),
                    'detail_images': list_to_string(raw_data.get('detail_images', []), separator=','),
                    # 添加VIP相关字段
                    'vip_sale_price': vip_sale_price,           # VIP实际支付价格
                    'vip_cost_price': vip_cost_price,           # VIP成本价格
                    'vip_profit': vip_profit,                   # VIP利润金额
                    'vip_profit_margin': f"{vip_profit_margin}%" if isinstance(vip_profit_margin, (int, float)) else vip_profit_margin # VIP利润率（百分数格式）
                }

                formatted_rows.append(row_data)

            except Exception:
                continue

        # 确保至少有一行数据（即使所有处理都失败，也要保留基础信息）
        if not formatted_rows:
            basic_row = {
                'product_code': raw_data.get('product_code', ''),
                'brand': raw_data.get('brand', ''),
                'title': raw_data.get('title', ''),
                'sku_code': f"{raw_data.get('product_code', '')}-SKU1",
                'spec_value': '处理失败',
                'sub_spec_value': '',
                'original_price': '0.00',
                'sale_price': '0.00',
                'cost_price': '0.00',
                'weight': str(int(self.DEFAULT_WEIGHT)),
                'stock_quantity': '0',
                'sku_status': '待处理',
                'source': raw_data.get('source', ''),
                'source_url': raw_data.get('source_url', ''),
                'image_package_url': raw_data.get('image_package_url', ''),
                'description': raw_data.get('description', ''),
                'product_status': raw_data.get('product_status', ''),
                'category_level1': '',
                'category_level2': '',
                # 添加图片字段
                'main_images': list_to_string(raw_data.get('main_images', []), separator=','),
                'detail_images': list_to_string(raw_data.get('detail_images', []), separator=','),
                'vip_sale_price': '0.00',
                'vip_cost_price': '0.00',
                'vip_profit': '0.00',
                'vip_profit_margin': '0.00%'
            }
            formatted_rows.append(basic_row)

        return formatted_rows

    # ==================== Excel文件操作方法 ====================

    def write_to_excel(self, data_list: List[Dict], mode: str = 'append') -> bool:
        """
        写入Excel文件
        
        Args:
            data_list: 要写入的数据列表
            mode: 写入模式，'append'追加，'overwrite'覆盖
            
        Returns:
            bool: 写入是否成功
        """
        try:
            if not data_list:
                return False
            
            # 创建DataFrame
            df_new = pd.DataFrame(data_list)
            
            # 确保目录存在
            self.file_path.parent.mkdir(parents=True, exist_ok=True)
            
            if mode == 'append' and self.file_path.exists():
                # 追加模式：读取现有数据并合并
                try:
                    df_existing = pd.read_excel(self.file_path, sheet_name=self.sheet_name)

                    # 检查DataFrame是否为空，避免FutureWarning
                    if df_existing.empty:
                        df_combined = df_new
                    elif df_new.empty:
                        df_combined = df_existing
                    else:
                        # 确保两个DataFrame有相同的列结构
                        df_existing_cols = set(df_existing.columns)
                        df_new_cols = set(df_new.columns)

                        # 添加缺失的列（填充空字符串而不是NA，避免FutureWarning）
                        for col in df_new_cols - df_existing_cols:
                            df_existing[col] = ''
                        for col in df_existing_cols - df_new_cols:
                            df_new[col] = ''

                        # 保持现有文件的列顺序，避免改变列位置
                        existing_cols = list(df_existing.columns)
                        df_new = df_new[existing_cols]

                        # 使用sort=False避免FutureWarning
                        df_combined = pd.concat([df_existing, df_new], ignore_index=True, sort=False)
                except Exception:
                    df_combined = df_new
            else:
                # 覆盖模式或文件不存在
                df_combined = df_new
            
            # 写入Excel文件
            with pd.ExcelWriter(self.file_path, engine='openpyxl', mode='w') as writer:
                df_combined.to_excel(writer, sheet_name=self.sheet_name, index=False)

            return True

        except Exception as e:
            raise RuntimeError(f"Excel文件写入失败: {e}")

    def read_all_products_from_excel(self, include_all_fields: bool = True) -> List[Dict]:
        """
        从Excel文件读取所有商品数据，返回与写入时相同格式的字典列表

        Args:
            include_all_fields: 是否包含所有字段
                - True: 读取Excel中的所有字段（默认）
                - False: 只读取数据库需要的字段，过滤掉Excel特有的字段

        Returns:
            List[Dict]: 商品数据列表，每个商品包含基本信息和skus列表

        Raises:
            FileNotFoundError: Excel文件不存在
            ValueError: Excel文件格式错误
            Exception: 其他读取错误
        """
        if not self.file_path.exists():
            raise FileNotFoundError(f"Excel文件不存在: {self.file_path}")

        # 定义数据库需要的字段
        database_product_fields = {
            'product_code', 'brand', 'title', 'category_level1', 'category_level2',
            'description', 'main_images', 'detail_images', 'source', 'source_url',
            'image_package_url', 'product_status'
        }

        database_sku_fields = {
            'sku_code', 'spec_value', 'sub_spec_value', 'sale_price', 'cost_price',
            'weight', 'stock_quantity', 'sku_status'
        }

        try:
            # 读取Excel数据
            df = pd.read_excel(self.file_path, sheet_name=self.sheet_name)

            if df.empty:
                return []

            # 将DataFrame转换为字典列表，并处理NaN值
            excel_rows = df.to_dict('records')

            # 使用字段转换器将Excel格式转换为标准格式
            converted_rows = []
            for row in excel_rows:
                # 先标准化NaN值
                normalized_row = self._normalize_nan_values(row)
                # 转换列表字段（从Excel的分号分隔字符串转为列表）
                converted_row = convert_from_excel(normalized_row)
                converted_rows.append(converted_row)

            # 按product_code分组，组装成商品+SKU的格式
            products_dict = {}

            for row in converted_rows:
                product_code = row.get('product_code', '')
                if not product_code:
                    continue

                # 如果是新商品，创建商品记录
                if product_code not in products_dict:
                    # 根据include_all_fields决定包含哪些字段
                    if include_all_fields:
                        # 包含所有字段
                        product_info = {
                            'product_code': product_code,
                            'brand': row.get('brand', ''),
                            'title': row.get('title', ''),
                            'source': row.get('source', ''),
                            'source_url': row.get('source_url', ''),
                            'image_package_url': row.get('image_package_url', ''),
                            'description': row.get('description', ''),
                            'product_status': row.get('product_status', ''),
                            'category_level1': row.get('category_level1', []),
                            'category_level2': row.get('category_level2', []),
                            'main_images': row.get('main_images', []),
                            'detail_images': row.get('detail_images', []),
                            'skus': []
                        }
                    else:
                        # 只包含数据库需要的字段
                        product_info = {
                            field: row.get(field, [] if field in ['category_level1', 'category_level2', 'main_images', 'detail_images'] else '')
                            for field in database_product_fields
                        }
                        product_info['skus'] = []

                    products_dict[product_code] = product_info

                # 添加SKU信息
                if include_all_fields:
                    # 包含所有字段
                    sku_info = {
                        'sku_code': row.get('sku_code', ''),
                        'spec_value': row.get('spec_value', ''),
                        'sub_spec_value': row.get('sub_spec_value', ''),
                        'original_price': self._parse_price(row.get('original_price', '0')),
                        'sale_price': self._parse_price(row.get('sale_price', '0')),
                        'cost_price': self._parse_price(row.get('cost_price', '0')),
                        'weight': self._parse_weight(row.get('weight', '0')),
                        'stock_quantity': self._parse_int(row.get('stock_quantity', '0')),
                        'sku_status': row.get('sku_status', ''),
                        'vip_sale_price': self._parse_price(row.get('vip_sale_price', '0')),
                        'vip_cost_price': self._parse_price(row.get('vip_cost_price', '0')),
                        'vip_profit': self._parse_price(row.get('vip_profit', '0')),
                        'vip_profit_margin': row.get('vip_profit_margin', '0%')
                    }
                else:
                    # 只包含数据库需要的字段
                    sku_info = {}
                    for field in database_sku_fields:
                        if field in ['sale_price', 'cost_price']:
                            sku_info[field] = self._parse_price(row.get(field, '0'))
                        elif field == 'weight':
                            sku_info[field] = self._parse_weight(row.get(field, '0'))
                        elif field == 'stock_quantity':
                            sku_info[field] = self._parse_int(row.get(field, '0'))
                        else:
                            sku_info[field] = row.get(field, '')

                products_dict[product_code]['skus'].append(sku_info)

            # 转换为列表
            products_list = list(products_dict.values())

            return products_list

        except FileNotFoundError:
            raise
        except Exception as e:
            raise Exception(f"读取Excel文件失败: {e}") from e

    def _parse_price(self, value) -> float:
        """解析价格字段"""
        try:
            if pd.isna(value) or value == '' or value is None:
                return 0.0
            # 移除可能的货币符号和空格
            str_value = str(value).replace('¥', '').replace('$', '').replace(',', '').strip()
            return float(str_value)
        except (ValueError, TypeError):
            return 0.0

    def _parse_weight(self, value) -> float:
        """解析重量字段"""
        try:
            if pd.isna(value) or value == '' or value is None:
                return 0.0
            # 移除可能的单位
            str_value = str(value).replace('kg', '').replace('g', '').strip()
            return float(str_value)
        except (ValueError, TypeError):
            return 0.0

    def _parse_int(self, value) -> int:
        """解析整数字段"""
        try:
            if pd.isna(value) or value == '' or value is None:
                return 0
            return int(float(str(value)))
        except (ValueError, TypeError):
            return 0
            
        except Exception as e:
            raise RuntimeError(f"Excel文件写入失败: {e}")
    

if __name__ == "__main__":
    # 测试代码
    test_data = {
        'product_code': 'G67DD0BB8A3944',
        'brand': 'Fée et moi/霏慕',
        'title': '【情趣内衣】霏慕 伯爵千金 の透胸修女套装ZY6174',
        'source': '伊性坊',
        'source_url': 'https://www.yxfshop.com/?product-14543.html',
        'image_package_url': 'https://www.yxfshop.com/refund/transit/skip.php?bn=test',
        'description': '',
        'product_status': '',
        'skus': [
            {
                'sku_code': '6977890233686',
                'spec_value': '◆伯爵千金◆6174黑+白【头纱+吊颈连衣裙+披肩+T裤】',
                'sale_price': '58.00',
                'cost_price': '31.30',
                'weight': '225',
                'stock_quantity': '426',
                'sku_status': '正常'
            }
        ]
    }
    
    excel_manager = ExcelManager("test.xlsx", "测试数据")
    formatted_data = excel_manager.format_product_data(test_data)
