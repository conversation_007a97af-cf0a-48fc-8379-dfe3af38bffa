#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
字段转换器 - 处理列表和字符串之间的转换
将JSON格式的列表字段改为逗号分隔的字符串格式，简化数据处理
"""

import sys
import os
from typing import List, Union, Any, Optional
from pathlib import Path

sys.path.insert(0, str(Path(__file__).parent.parent))
from core import logger


class FieldConverter:
    """字段转换器 - 处理列表和字符串的相互转换"""
    
    # 需要转换的列表字段
    LIST_FIELDS = [
        'main_images',      # 主图列表
        'detail_images',    # 详情图列表
        'category_level1',  # 一级分类（支持多分类）
        'category_level2'   # 二级分类（支持多分类）
    ]
    
    @classmethod
    def list_to_string(cls, value: Union[List, str, None], separator: str = ',') -> str:
        """
        将列表转换为逗号分隔的字符串

        Args:
            value: 输入值，可以是列表、字符串或None
            separator: 分隔符，默认为逗号

        Returns:
            str: 转换后的字符串
        """
        if value is None:
            return ''

        # 检查是否为NaN值
        if cls._is_nan(value):
            return ''

        if isinstance(value, list):
            # 过滤空值、NaN值并转换为字符串
            filtered_list = []
            for item in value:
                if item is not None and not cls._is_nan(item):
                    item_str = str(item).strip()
                    if item_str and item_str.lower() != 'nan':
                        filtered_list.append(item_str)
            return separator.join(filtered_list)

        if isinstance(value, str):
            # 如果已经是字符串，检查是否为'nan'
            if value.strip().lower() == 'nan':
                return ''
            return value.strip()

        # 其他类型转为字符串，但要检查NaN
        str_value = str(value).strip()
        if str_value.lower() == 'nan':
            return ''
        return str_value

    @classmethod
    def _is_nan(cls, value) -> bool:
        """检查值是否为NaN或类似的空值"""
        try:
            import math
            import pandas as pd

            # 检查pandas的NA值
            if pd.isna(value):
                return True

            # 检查数学上的NaN
            return math.isnan(float(value))
        except (TypeError, ValueError, ImportError):
            # 如果pandas不可用或转换失败，检查字符串表示
            str_value = str(value).strip().lower()
            return str_value in ['nan', 'na', '<na>', 'none', 'null']
    
    @classmethod
    def string_to_list(cls, value: Union[str, List, None], separator: str = ',') -> List[str]:
        """
        将逗号分隔的字符串转换为列表
        
        Args:
            value: 输入值，可以是字符串、列表或None
            separator: 分隔符，默认为逗号
            
        Returns:
            List[str]: 转换后的列表
        """
        if value is None:
            return []
        
        if isinstance(value, list):
            # 如果已经是列表，过滤空值并返回
            return [str(item).strip() for item in value if item and str(item).strip()]
        
        if isinstance(value, str):
            if not value.strip():
                return []
            # 分割字符串并过滤空值
            items = [item.strip() for item in value.split(separator)]
            return [item for item in items if item]
        
        # 其他类型转为单元素列表
        return [str(value).strip()] if str(value).strip() else []
    
    @classmethod
    def prepare_data_for_database(cls, data: dict) -> dict:
        """
        准备数据写入数据库（列表字段转为字符串，清理NaN值）

        Args:
            data: 原始数据字典

        Returns:
            dict: 转换后的数据字典
        """
        converted_data = data.copy()

        # 处理预定义的列表字段
        for field in cls.LIST_FIELDS:
            if field in converted_data:
                original_value = converted_data[field]
                converted_value = cls.list_to_string(original_value)
                converted_data[field] = converted_value

        # 处理所有其他字段的NaN值
        for field, value in converted_data.items():
            if field not in cls.LIST_FIELDS:  # 跳过已处理的列表字段
                if cls._is_nan(value):
                    converted_data[field] = ''
                elif isinstance(value, str) and value.strip().lower() == 'nan':
                    converted_data[field] = ''

        return converted_data
    
    @classmethod
    def prepare_data_from_database(cls, data: dict) -> dict:
        """
        准备数据从数据库读取（字符串字段转为列表）
        
        Args:
            data: 数据库读取的数据字典
            
        Returns:
            dict: 转换后的数据字典
        """
        converted_data = data.copy()
        
        for field in cls.LIST_FIELDS:
            if field in converted_data:
                original_value = converted_data[field]
                converted_value = cls.string_to_list(original_value)
                converted_data[field] = converted_value

        return converted_data
    
    @classmethod
    def prepare_data_for_excel(cls, data: dict) -> dict:
        """
        准备数据写入Excel（列表字段转为字符串）
        
        Args:
            data: 原始数据字典
            
        Returns:
            dict: 转换后的数据字典
        """
        converted_data = data.copy()
        
        for field in cls.LIST_FIELDS:
            if field in converted_data:
                original_value = converted_data[field]
                # Excel中使用逗号分隔，与数据库保持一致
                converted_value = cls.list_to_string(original_value, separator=',')
                converted_data[field] = converted_value

        return converted_data
    
    @classmethod
    def prepare_data_from_excel(cls, data: dict) -> dict:
        """
        准备数据从Excel读取（字符串字段转为列表）
        
        Args:
            data: Excel读取的数据字典
            
        Returns:
            dict: 转换后的数据字典
        """
        converted_data = data.copy()
        
        for field in cls.LIST_FIELDS:
            if field in converted_data:
                original_value = converted_data[field]
                # 尝试多种分隔符
                if isinstance(original_value, str):
                    if '; ' in original_value:
                        converted_value = cls.string_to_list(original_value, separator='; ')
                    elif ',' in original_value:
                        converted_value = cls.string_to_list(original_value, separator=',')
                    else:
                        converted_value = cls.string_to_list(original_value)
                else:
                    converted_value = cls.string_to_list(original_value)
                
                converted_data[field] = converted_value

        return converted_data
    
    @classmethod
    def validate_list_field(cls, field_name: str, value: Any) -> bool:
        """
        验证列表字段的有效性
        
        Args:
            field_name: 字段名
            value: 字段值
            
        Returns:
            bool: 是否有效
        """
        if field_name not in cls.LIST_FIELDS:
            return True  # 非列表字段不验证
        
        if value is None:
            return True  # 允许为空
        
        if isinstance(value, (list, str)):
            return True
        
        logger.debug(f"字段 {field_name} 的值类型无效: {type(value)}, 值: {value}")
        return False
    
    @classmethod
    def get_list_fields(cls) -> List[str]:
        """
        获取所有列表字段名
        
        Returns:
            List[str]: 列表字段名列表
        """
        return cls.LIST_FIELDS.copy()

# 创建全局实例
field_converter = FieldConverter()

# 便捷函数
def convert_for_database(data: dict) -> dict:
    """便捷函数：准备数据写入数据库"""
    return FieldConverter.prepare_data_for_database(data)

def convert_from_database(data: dict) -> dict:
    """便捷函数：准备数据从数据库读取"""
    return FieldConverter.prepare_data_from_database(data)

def convert_for_excel(data: dict) -> dict:
    """便捷函数：准备数据写入Excel"""
    return FieldConverter.prepare_data_for_excel(data)

def convert_from_excel(data: dict) -> dict:
    """便捷函数：准备数据从Excel读取"""
    return FieldConverter.prepare_data_from_excel(data)

def list_to_string(value: Union[List, str, None], separator: str = ',') -> str:
    """便捷函数：列表转字符串"""
    return FieldConverter.list_to_string(value, separator)

def string_to_list(value: Union[str, List, None], separator: str = ',') -> List[str]:
    """便捷函数：字符串转列表"""
    return FieldConverter.string_to_list(value, separator)

