#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图片包处理器
功能：解压缩各种格式的图片包，支持密码保护的压缩包
创建时间：2025-01-21
"""

import os
import sys
import zipfile
import rarfile
import py7zr
import tarfile
import gzip
import shutil
from pathlib import Path
from typing import Optional, List, Union
from PIL import Image

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))
from core import logger, G

class ImagePackageProcessor:
    """图片包处理器类"""
    
    def __init__(self):
        """初始化图片包处理器"""
        self.supported_formats = {
            '.zip': self._extract_zip,
            '.rar': self._extract_rar,
            '.7z': self._extract_7z,
            '.tar': self._extract_tar,
            '.tar.gz': self._extract_tar_gz,
            '.tgz': self._extract_tar_gz,
            '.tar.bz2': self._extract_tar_bz2,
            '.gz': self._extract_gz
        }

        # 从配置文件获取默认密码
        try:
            password_config = G.yixingfang.image_package_password
            # 确保密码是字符串类型
            self.default_password = str(password_config) if password_config is not None else None
            logger.debug(f"已加载默认密码配置")
        except AttributeError as e:
            self.default_password = None
            logger.debug(f"未找到密码配置，将尝试无密码解压: {e}")
        except Exception as e:
            self.default_password = None
            logger.debug(f"配置读取异常，将尝试无密码解压: {e}")

    def _extract_rar_with_winrar(self, package_path: Path, extract_to: Path, password: Optional[str]) -> bool:
        """使用WinRAR解压RAR文件"""
        try:
            import subprocess

            # 查找WinRAR工具
            winrar_paths = [
                Path("C:/Program Files/WinRAR/WinRAR.exe"),
                Path("C:/Program Files (x86)/WinRAR/WinRAR.exe"),
                Path.cwd() / "WinRAR.exe",
                Path.cwd() / "winrar.exe"
            ]

            winrar_path = None
            for path in winrar_paths:
                if path.exists():
                    winrar_path = str(path)
                    break

            if not winrar_path:
                return False

            logger.debug(f"使用WinRAR解压: {winrar_path}")

            # 构建命令
            cmd = [winrar_path, 'x', '-ibck', '-y']  # x=解压, -ibck=后台, -y=全部确认
            if password:
                cmd.append(f'-p{password}')
            cmd.extend([str(package_path), str(extract_to) + "\\"])

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)

            if result.returncode == 0:
                self._process_extracted_folders(extract_to)
                return True
            else:
                logger.debug(f"WinRAR解压失败，返回码: {result.returncode}")
                return False

        except Exception as e:
            logger.debug(f"WinRAR解压异常: {e}")
            return False

    def _extract_rar_with_7z(self, package_path: Path, extract_to: Path, password: Optional[str]) -> bool:
        """使用7z工具解压RAR文件"""
        try:
            import subprocess

            # 检查7z是否可用
            result = subprocess.run(['where', '7z.exe'], capture_output=True, text=True)
            if result.returncode != 0:
                return False

            logger.debug("使用7z解压RAR文件")

            # 构建命令
            cmd = ['7z', 'x', str(package_path), f'-o{extract_to}', '-y']
            if password:
                cmd.append(f'-p{password}')

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)

            if result.returncode == 0:
                logger.debug("7z解压成功")
                self._process_extracted_folders(extract_to)
                return True
            else:
                logger.debug(f"7z解压失败，返回码: {result.returncode}")
                return False

        except Exception as e:
            logger.debug(f"7z解压异常: {e}")
            return False


    def _process_extracted_folders(self, root_path: Path):
        """处理解压后的文件夹结构，将深层文件夹移动到根目录"""
        try:
            # 获取根目录下的所有文件夹
            folders = [item for item in root_path.iterdir() if item.is_dir()]

            # 如果只有一个文件夹，继续递归查找
            while len(folders) == 1:
                current_folder = folders[0]
                sub_items = list(current_folder.iterdir())

                # 将所有项目移动到根目录
                for item in sub_items:
                    dst_path = root_path / item.name

                    # 如果目标路径已存在，添加数字后缀
                    if dst_path.exists():
                        counter = 1
                        stem = dst_path.stem
                        suffix = dst_path.suffix
                        while dst_path.exists():
                            dst_path = root_path / f"{stem}_{counter}{suffix}"
                            counter += 1

                    try:
                        shutil.move(str(item), str(dst_path))
                    except Exception as e:
                        logger.debug(f"移动文件失败 {item}: {e}")

                # 删除空文件夹
                try:
                    current_folder.rmdir()
                except Exception as e:
                    logger.debug(f"删除文件夹失败 {current_folder}: {e}")

                # 重新获取文件夹列表
                folders = [item for item in root_path.iterdir() if item.is_dir()]

        except Exception as e:
            logger.debug(f"处理文件夹结构失败: {e}")

    def extract_package(self, package_path: Union[str, Path],
                       password: Optional[str] = None,
                       delete_original: bool = False) -> Optional[str]:
        """
        解压图片包

        Args:
            package_path: 压缩包路径
            password: 解压密码，如果为None则使用配置文件中的默认密码
            delete_original: 是否删除原压缩包

        Returns:
            str: 解压成功返回解压目录路径，失败返回None
        """
        try:
            package_path = Path(package_path)
            logger.debug("开始减压缩", file=package_path)

            # 检查压缩包是否存在
            if not package_path.exists():
                logger.debug("压缩包不存在", file=package_path)
                return None

            # 自动生成解压目录：压缩包同目录下，以压缩包名称命名（去掉扩展名）
            extract_to = package_path.parent / package_path.stem

            # 创建解压目录（默认覆盖）
            extract_to.mkdir(parents=True, exist_ok=True)

            # 确定压缩包格式
            file_format = self._get_file_format(package_path)
            if not file_format:
                logger.debug(f"不支持的压缩包格式: {package_path.suffix}")
                return None

            # 使用指定密码或默认密码
            extract_password = password or self.default_password

            # 调用对应的解压方法
            extract_method = self.supported_formats[file_format]
            success = extract_method(package_path, extract_to, extract_password)

            if success:
                logger.debug(f"解压成功: {package_path.name}")
                # 统计解压出的文件数量
                file_count = sum(1 for _ in extract_to.rglob('*') if _.is_file())
                logger.debug(f"解压出 {file_count} 个文件")

                # 删除原压缩包（如果需要）
                if delete_original:
                    try:
                        package_path.unlink()
                        logger.debug(f"已删除原压缩包: {package_path.name}")
                    except Exception as e:
                        logger.debug(f"删除原压缩包失败: {e}")

                logger.debug('减压缩成功', file=str(extract_to))
                return str(extract_to)
            else:
                logger.debug(f"解压失败: {package_path.name}")
                return None

        except Exception as e:
            logger.debug(f"解压过程发生异常: {e}")
            return None
    
    def _get_file_format(self, file_path: Path) -> Optional[str]:
        """
        获取文件格式
        
        Args:
            file_path: 文件路径
            
        Returns:
            str: 文件格式，如果不支持则返回None
        """
        file_path_str = str(file_path).lower()
        
        # 检查复合扩展名
        for format_ext in ['.tar.gz', '.tar.bz2']:
            if file_path_str.endswith(format_ext):
                return format_ext
        
        # 检查单一扩展名
        suffix = file_path.suffix.lower()
        if suffix == '.tgz':
            return '.tar.gz'
        
        return suffix if suffix in self.supported_formats else None
    
    def _extract_zip(self, package_path: Path, extract_to: Path, password: Optional[str]) -> bool:
        """解压ZIP文件 - 智能密码处理"""
        try:
            with zipfile.ZipFile(package_path, 'r') as zip_ref:
                # 检查是否有加密文件和压缩方法
                encrypted_files = []
                unsupported_compression = []

                for info in zip_ref.infolist():
                    if info.flag_bits & 0x1:  # 加密文件
                        encrypted_files.append(info)
                    if info.compress_type == 99:  # WinZip AES加密
                        unsupported_compression.append(info)

                if unsupported_compression:
                    logger.debug(f"ZIP文件使用了不支持的加密方法（WinZip AES），建议使用7Z格式: {package_path}")
                    logger.debug(f"不支持的文件数量: {len(unsupported_compression)}")
                    return False

                # 智能密码处理：先用密码，失败后尝试无密码
                if password:
                    logger.debug(f"使用密码解压ZIP文件")
                    try:
                        zip_ref.setpassword(password.encode('utf-8'))
                        zip_ref.extractall(extract_to)
                        self._process_extracted_folders(extract_to)
                        return True
                    except Exception as e:
                        logger.debug(f"使用密码解压失败，尝试无密码解压: {e}")

                # 尝试无密码解压
                zip_ref.extractall(extract_to)
                self._process_extracted_folders(extract_to)
            return True
        except zipfile.BadZipFile:
            logger.debug(f"ZIP文件损坏: {package_path}")
            return False
        except Exception as e:
            logger.debug(f"ZIP解压失败: {e}")
            return False
    
    def _extract_rar(self, package_path: Path, extract_to: Path, password: Optional[str]) -> bool:
        """解压RAR文件 - 多工具支持"""
        # 1. 优先尝试WinRAR
        if self._extract_rar_with_winrar(package_path, extract_to, password):
            return True

        # 2. 尝试7z工具
        if self._extract_rar_with_7z(package_path, extract_to, password):
            return True

        # 3. 最后尝试rarfile库
        try:
            with rarfile.RarFile(package_path, 'r') as rar_ref:
                # 先尝试用密码
                if password:
                    logger.debug(f"使用密码解压RAR文件")
                    try:
                        rar_ref.setpassword(password)
                        rar_ref.extractall(extract_to)
                        self._process_extracted_folders(extract_to)
                        return True
                    except Exception as e:
                        logger.debug(f"使用密码解压失败，尝试无密码解压: {e}")

                # 尝试无密码解压
                rar_ref.extractall(extract_to)
                self._process_extracted_folders(extract_to)
            return True
        except Exception as e:
            logger.debug(f"RAR解压失败: {e}")
            return False
    
    def _extract_7z(self, package_path: Path, extract_to: Path, password: Optional[str]) -> bool:
        """解压7Z文件 - 智能密码处理"""
        try:
            # 先尝试用密码解压
            if password:
                try:
                    with py7zr.SevenZipFile(package_path, mode="r", password=password) as archive:
                        archive.extractall(path=extract_to)
                    self._process_extracted_folders(extract_to)
                    return True
                except Exception as e:
                    logger.debug(f"使用密码解压失败，尝试无密码解压: {e}")

            # 尝试无密码解压
            with py7zr.SevenZipFile(package_path, mode="r") as archive:
                archive.extractall(path=extract_to)
            self._process_extracted_folders(extract_to)
            return True
        except py7zr.Bad7zFile:
            logger.debug(f"7Z文件损坏: {package_path}")
            return False
        except Exception as e:
            logger.debug(f"7Z解压失败: {e}")
            return False
    
    def _extract_tar(self, package_path: Path, extract_to: Path, password: Optional[str]) -> bool:
        """解压TAR文件"""
        try:
            with tarfile.open(package_path, 'r') as tar_ref:
                tar_ref.extractall(extract_to)
            return True
        except tarfile.TarError as e:
            logger.debug(f"TAR解压错误: {e}")
            return False
        except Exception as e:
            logger.debug(f"TAR解压异常: {e}")
            return False
    
    def _extract_tar_gz(self, package_path: Path, extract_to: Path, password: Optional[str]) -> bool:
        """解压TAR.GZ文件"""
        try:
            with tarfile.open(package_path, 'r:gz') as tar_ref:
                tar_ref.extractall(extract_to)
            return True
        except tarfile.TarError as e:
            logger.debug(f"TAR.GZ解压错误: {e}")
            return False
        except Exception as e:
            logger.debug(f"TAR.GZ解压异常: {e}")
            return False
    
    def _extract_tar_bz2(self, package_path: Path, extract_to: Path, password: Optional[str]) -> bool:
        """解压TAR.BZ2文件"""
        try:
            with tarfile.open(package_path, 'r:bz2') as tar_ref:
                tar_ref.extractall(extract_to)
            return True
        except tarfile.TarError as e:
            logger.debug(f"TAR.BZ2解压错误: {e}")
            return False
        except Exception as e:
            logger.debug(f"TAR.BZ2解压异常: {e}")
            return False
    
    def _extract_gz(self, package_path: Path, extract_to: Path, password: Optional[str]) -> bool:
        """解压GZ文件"""
        try:
            output_file = extract_to / package_path.stem
            with gzip.open(package_path, 'rb') as gz_file:
                with open(output_file, 'wb') as out_file:
                    shutil.copyfileobj(gz_file, out_file)
            return True
        except Exception as e:
            logger.debug(f"GZ解压异常: {e}")
            return False
    
    def get_supported_formats(self) -> List[str]:
        """获取支持的压缩格式列表"""
        return list(self.supported_formats.keys())
    
    def is_supported_format(self, file_path: Union[str, Path]) -> bool:
        """检查文件格式是否支持"""
        file_path = Path(file_path)
        return self._get_file_format(file_path) is not None

    def process_extracted_folder(self, folder_path: Union[str, Path]) -> str:
        """
        处理已解压的文件夹，将深层文件夹移动到根目录

        Args:
            folder_path: 已解压的文件夹路径

        Returns:
            str: 处理后的文件夹路径
        """
        try:
            folder_path = Path(folder_path)

            if not folder_path.exists() or not folder_path.is_dir():
                logger.debug(f"文件夹不存在或不是目录: {folder_path}")
                return str(folder_path)

            logger.debug(f"开始处理文件夹结构: {folder_path.name}")

            # 调用内部处理方法
            self._process_extracted_folders(folder_path)

            logger.debug(f"文件夹结构处理完成: {folder_path.name}")
            return str(folder_path)

        except Exception as e:
            logger.debug(f"处理文件夹结构失败: {e}")
            return str(folder_path)

    def organize_image_package_folder(self, folder_path: Union[str, Path]) -> bool:
        """
        整理图片包文件夹

        功能：
        1. 创建【资料】子文件夹
        2. 将空子文件夹和零散文件移入资料文件夹
        3. 将包含特定关键词的子文件夹移入资料文件夹

        Args:
            folder_path: 图片包文件夹路径

        Returns:
            bool: 整理成功返回True，失败返回False
        """
        try:
            folder_path = Path(folder_path)

            if not folder_path.exists() or not folder_path.is_dir():
                logger.debug(f"文件夹不存在或不是目录: {folder_path}")
                return False

            logger.debug(f"开始整理图片包文件夹: {folder_path.name}")

            # 创建【资料】文件夹
            data_folder = self._create_data_folder(folder_path)
            if not data_folder:
                return False

            # 获取关键词配置
            keywords = self._get_organize_keywords()

            # 整理文件夹内容
            success = self._organize_folder_contents(folder_path, data_folder, keywords)

            if success:
                # 进行分组优先级过滤
                filter_success = self._filter_folders_by_priority(folder_path, data_folder, keywords)
                if filter_success:
                    logger.debug(f"图片包文件夹整理和过滤完成: {folder_path.name}")
                else:
                    logger.debug(f"图片包文件夹整理完成，但优先级过滤失败: {folder_path.name}")
            else:
                logger.debug(f"图片包文件夹整理失败: {folder_path.name}")

            return success

        except Exception as e:
            logger.debug(f"整理图片包文件夹异常: {e}")
            return False

    def _create_data_folder(self, parent_folder: Path) -> Optional[Path]:
        """
        创建【资料】文件夹

        Args:
            parent_folder: 父文件夹路径

        Returns:
            Path: 创建的资料文件夹路径，失败返回None
        """
        try:
            data_folder = parent_folder / "资料"
            data_folder.mkdir(exist_ok=True)
            logger.debug(f"创建资料文件夹: {data_folder.name}")
            return data_folder

        except Exception as e:
            logger.debug(f"创建资料文件夹失败: {e}")
            return None

    def _get_organize_keywords(self) -> dict:
        """
        从配置文件获取整理关键词

        Returns:
            dict: 包含各类关键词的字典
        """
        try:
            config = G.folder_organize_keywords
            keywords = {
                'move_to_data': getattr(config, 'move_to_data_keywords', ["无遮挡", "无码"]),
                'main_image': getattr(config, 'main_image_keywords', ["主图"]),
                'detail_image': getattr(config, 'detail_image_keywords', ["详情"]),
                'spec_image': getattr(config, 'spec_image_keywords', ["规格"])
            }

            return keywords

        except AttributeError as e:
            logger.debug(f"未找到关键词配置，使用默认关键词: {e}")
            return {
                'move_to_data': ["无遮挡", "无码"],
                'main_image': ["主图"],
                'detail_image': ["详情"],
                'spec_image': ["规格"]
            }
        except Exception as e:
            logger.debug(f"获取关键词配置异常，使用默认关键词: {e}")
            return {
                'move_to_data': ["无遮挡", "无码"],
                'main_image': ["主图"],
                'detail_image': ["详情"],
                'spec_image': ["规格"]
            }

    def _organize_folder_contents(self, source_folder: Path, data_folder: Path, keywords: dict) -> bool:
        """
        整理文件夹内容

        Args:
            source_folder: 源文件夹
            data_folder: 资料文件夹
            keywords: 关键词字典

        Returns:
            bool: 整理成功返回True
        """
        try:
            moved_count = 0
            kept_count = 0

            # 获取所有项目（文件和文件夹）
            all_items = list(source_folder.iterdir())

            for item in all_items:
                # 跳过资料文件夹本身
                if item.name == "资料":
                    continue

                should_move = False
                move_reason = ""

                if item.is_file():
                    # 零散文件直接移动
                    should_move = True
                    move_reason = "零散文件"

                elif item.is_dir():
                    # 检查是否为空文件夹
                    if self._is_empty_folder(item):
                        should_move = True
                        move_reason = "空文件夹"
                    else:
                        # 检查文件夹类型
                        folder_type = self._classify_folder(item.name, keywords)

                        if folder_type == "move_to_data":
                            should_move = True
                            move_reason = f"包含敏感关键词: {item.name}"
                        elif folder_type == "keep":
                            should_move = False
                            kept_count += 1
                        else:
                            # 不包含任何已知关键词的文件夹移入资料
                            should_move = True
                            move_reason = f"未识别类型的文件夹: {item.name}"

                # 执行移动操作
                if should_move:
                    success = self._move_item_to_data_folder(item, data_folder, move_reason)
                    if success:
                        moved_count += 1

            logger.debug(f"整理完成，共移动 {moved_count} 个项目到资料文件夹，保留 {kept_count} 个有效文件夹")
            return True

        except Exception as e:
            logger.debug(f"整理文件夹内容失败: {e}")
            return False

    def _is_empty_folder(self, folder_path: Path) -> bool:
        """
        检查文件夹是否为空

        Args:
            folder_path: 文件夹路径

        Returns:
            bool: 空文件夹返回True
        """
        try:
            return not any(folder_path.iterdir())
        except Exception:
            return False

    def _classify_folder(self, folder_name: str, keywords: dict) -> str:
        """
        分类文件夹类型

        Args:
            folder_name: 文件夹名称
            keywords: 关键词字典

        Returns:
            str: 文件夹类型 ('main_image', 'detail_image', 'spec_image', 'move_to_data', 'unknown')
        """
        folder_name_lower = folder_name.lower()

        # 检查是否包含敏感关键词（优先级最高）
        for keyword in keywords.get('move_to_data', []):
            if keyword.lower() in folder_name_lower:
                return "move_to_data"

        # 检查主图关键词
        for keyword in keywords.get('main_image', []):
            if keyword.lower() in folder_name_lower:
                return "keep"  # 主图文件夹保留

        # 检查详情关键词
        for keyword in keywords.get('detail_image', []):
            if keyword.lower() in folder_name_lower:
                return "keep"  # 详情文件夹保留

        # 检查规格关键词
        for keyword in keywords.get('spec_image', []):
            if keyword.lower() in folder_name_lower:
                return "keep"  # 规格文件夹保留

        # 未匹配任何关键词
        return "unknown"

    def _contains_keywords(self, folder_name: str, keywords: List[str]) -> bool:
        """
        检查文件夹名是否包含关键词（保留兼容性）

        Args:
            folder_name: 文件夹名称
            keywords: 关键词列表

        Returns:
            bool: 包含关键词返回True
        """
        folder_name_lower = folder_name.lower()
        for keyword in keywords:
            if keyword.lower() in folder_name_lower:
                return True
        return False

    def _move_item_to_data_folder(self, item: Path, data_folder: Path, reason: str) -> bool:
        """
        将项目移动到资料文件夹

        Args:
            item: 要移动的项目路径
            data_folder: 资料文件夹路径
            reason: 移动原因

        Returns:
            bool: 移动成功返回True
        """
        try:
            dst_path = data_folder / item.name

            # 如果目标路径已存在，添加数字后缀
            if dst_path.exists():
                counter = 1
                stem = dst_path.stem
                suffix = dst_path.suffix
                while dst_path.exists():
                    if dst_path.is_dir():
                        dst_path = data_folder / f"{stem}_{counter}"
                    else:
                        dst_path = data_folder / f"{stem}_{counter}{suffix}"
                    counter += 1

            # 执行移动
            shutil.move(str(item), str(dst_path))
            logger.debug(f"移动成功 [{reason}]: {item.name} -> 资料/{dst_path.name}")
            return True

        except Exception as e:
            logger.debug(f"移动失败 [{reason}]: {item.name} - {e}")
            return False

    def _filter_folders_by_priority(self, source_folder: Path, data_folder: Path, keywords: dict) -> bool:
        """
        按优先级过滤文件夹，确保每个组（主图、详情、规格）只保留一个最优文件夹

        Args:
            source_folder: 源文件夹
            data_folder: 资料文件夹
            keywords: 关键词字典

        Returns:
            bool: 过滤成功返回True
        """
        try:
            # 获取优先级配置
            priority_config = self._get_priority_config()

            # 分组收集文件夹
            folder_groups = self._group_folders_by_type(source_folder, keywords)

            # 对每个组进行优先级过滤
            total_moved = 0
            for group_type, folders in folder_groups.items():
                if len(folders) > 0:  # 有文件夹时进行处理
                    if group_type == 'detail_image':
                        # 详情组特殊处理
                        moved_count = self._filter_detail_group_special(
                            folders, data_folder, priority_config
                        )
                    elif len(folders) > 1:
                        # 其他组正常处理
                        moved_count = self._filter_group_by_priority(
                            folders, group_type, data_folder, priority_config
                        )
                    else:
                        moved_count = 0
                    total_moved += moved_count

            # 重命名保留的文件夹
            self._rename_final_folders(source_folder)

            logger.debug(f"优先级过滤完成，共移动 {total_moved} 个低优先级文件夹到资料文件夹")
            return True

        except Exception as e:
            logger.debug(f"分组优先级过滤失败: {e}")
            return False

    def _get_priority_config(self) -> dict:
        """
        获取优先级配置

        Returns:
            dict: 优先级配置字典
        """
        try:
            config = G.folder_organize_keywords.priority_filter_keywords
            priority_config = {
                'main_image': getattr(config, 'main_image_priority', []),
                'detail_image': getattr(config, 'detail_image_priority', []),
                'spec_image': getattr(config, 'spec_image_priority', [])
            }

            for key, value in priority_config.items():
                logger.debug(f"  {key}: {value}")

            return priority_config

        except Exception as e:
            logger.debug(f"获取优先级配置失败，使用默认配置: {e}")
            return {
                'main_image': ["无水印", "800", "", "有水印", "750"],
                'detail_image': ["无水印", "", "有水印"],
                'spec_image': ["", "无水印", "实拍"]
            }

    def _group_folders_by_type(self, source_folder: Path, keywords: dict) -> dict:
        """
        按类型分组文件夹

        Args:
            source_folder: 源文件夹
            keywords: 关键词字典

        Returns:
            dict: 分组后的文件夹字典
        """
        groups = {
            'main_image': [],
            'detail_image': [],
            'spec_image': []
        }

        for item in source_folder.iterdir():
            if item.is_dir() and item.name != "资料":
                folder_type = self._get_detailed_folder_type(item.name, keywords)
                if folder_type in groups:
                    groups[folder_type].append(item)

        return groups

    def _get_detailed_folder_type(self, folder_name: str, keywords: dict) -> str:
        """
        获取详细的文件夹类型

        Args:
            folder_name: 文件夹名称
            keywords: 关键词字典

        Returns:
            str: 详细的文件夹类型
        """
        folder_name_lower = folder_name.lower()

        # 检查主图关键词
        for keyword in keywords.get('main_image', []):
            if keyword.lower() in folder_name_lower:
                return "main_image"

        # 检查详情关键词
        for keyword in keywords.get('detail_image', []):
            if keyword.lower() in folder_name_lower:
                return "detail_image"

        # 检查规格关键词
        for keyword in keywords.get('spec_image', []):
            if keyword.lower() in folder_name_lower:
                return "spec_image"

        return "unknown"

    def _filter_group_by_priority(self, folders: List[Path], group_type: str,
                                 data_folder: Path, priority_config: dict) -> int:
        """
        对单个组内的文件夹按优先级进行过滤

        Args:
            folders: 文件夹列表
            group_type: 组类型
            data_folder: 资料文件夹
            priority_config: 优先级配置

        Returns:
            int: 移动的文件夹数量
        """
        if len(folders) <= 1:
            return 0

        logger.debug(f"开始过滤 {group_type} 组，共 {len(folders)} 个文件夹")

        # 获取该组的优先级规则
        priority_keywords = priority_config.get(group_type, [])
        if not priority_keywords:
            logger.debug(f"未找到 {group_type} 的优先级规则，跳过过滤")
            return 0

        # 为每个文件夹计算优先级分数
        folder_scores = []
        for folder in folders:
            score = self._calculate_priority_score_simple(folder.name, priority_keywords)
            folder_scores.append((folder, score))
            logger.debug(f"  {folder.name}: 优先级分数 {score}")

        # 按优先级分数排序（分数越小优先级越高）
        folder_scores.sort(key=lambda x: x[1])

        # 保留优先级最高的文件夹，移动其他文件夹
        best_folder = folder_scores[0][0]
        folders_to_move = [item[0] for item in folder_scores[1:]]

        logger.debug(f"保留最高优先级文件夹: {best_folder.name}")

        moved_count = 0
        for folder in folders_to_move:
            success = self._move_item_to_data_folder(
                folder, data_folder, f"低优先级{group_type}文件夹"
            )
            if success:
                moved_count += 1

        return moved_count

    def _calculate_priority_score_simple(self, folder_name: str, priority_keywords: List[str]) -> int:
        """
        计算文件夹的优先级分数（简化版本）

        Args:
            folder_name: 文件夹名称
            priority_keywords: 优先级关键词列表（按优先级从高到低排列）

        Returns:
            int: 优先级分数（越小优先级越高）
        """
        folder_name_lower = folder_name.lower()

        # 遍历优先级关键词列表
        for index, keyword in enumerate(priority_keywords):
            if keyword == "":
                # 空字符串表示无关键词，检查是否不包含其他关键词
                has_other_keywords = False
                for other_keyword in priority_keywords:
                    if other_keyword != "" and other_keyword.lower() in folder_name_lower:
                        has_other_keywords = True
                        break

                if not has_other_keywords:
                    return index  # 返回在列表中的位置作为优先级分数
            else:
                # 检查是否包含当前关键词
                if keyword.lower() in folder_name_lower:
                    return index  # 返回在列表中的位置作为优先级分数

        # 如果没有匹配任何规则，返回最低优先级
        return 999

    def _filter_detail_group_special(self, folders: List[Path], data_folder: Path, priority_config: dict) -> int:
        """
        详情组特殊处理：先保留包含"实拍"的文件夹，再对其他文件夹按优先级处理

        Args:
            folders: 详情文件夹列表
            data_folder: 资料文件夹
            priority_config: 优先级配置

        Returns:
            int: 移动的文件夹数量
        """
        if len(folders) == 0:
            return 0

        # 查找包含"实拍"关键词的文件夹
        shiPai_folders = []
        other_folders = []

        for folder in folders:
            if "实拍" in folder.name:
                shiPai_folders.append(folder)
            else:
                other_folders.append(folder)

        moved_count = 0

        # 如果有多个实拍文件夹，只保留第一个
        if len(shiPai_folders) > 1:
            keep_shiPai = shiPai_folders[0]
            move_shiPai = shiPai_folders[1:]

            for folder in move_shiPai:
                success = self._move_item_to_data_folder(
                    folder, data_folder, "多余的实拍文件夹"
                )
                if success:
                    moved_count += 1

        # 对其他详情文件夹按优先级处理
        if len(other_folders) > 1:
            priority_keywords = priority_config.get('detail_image', [])
            if priority_keywords:
                # 计算优先级并排序
                folder_scores = []
                for folder in other_folders:
                    score = self._calculate_priority_score_simple(folder.name, priority_keywords)
                    folder_scores.append((folder, score))
                    logger.debug(f"  {folder.name}: 优先级分数 {score}")

                # 按优先级分数排序（分数越小优先级越高）
                folder_scores.sort(key=lambda x: x[1])

                # 保留优先级最高的文件夹，移动其他文件夹
                best_folder = folder_scores[0][0]
                folders_to_move = [item[0] for item in folder_scores[1:]]

                logger.debug(f"保留最高优先级详情文件夹: {best_folder.name}")

                for folder in folders_to_move:
                    success = self._move_item_to_data_folder(
                        folder, data_folder, "低优先级详情文件夹"
                    )
                    if success:
                        moved_count += 1

        return moved_count

    def _rename_final_folders(self, source_folder: Path) -> bool:
        """
        重命名最终保留的文件夹为标准名称

        Args:
            source_folder: 源文件夹

        Returns:
            bool: 重命名成功返回True
        """
        try:
            logger.debug("开始重命名最终保留的文件夹...")

            # 定义重命名映射
            rename_map = {
                'main_image': '主图',
                'detail_image': '详情',
                'spec_image': '规格'
            }

            # 获取当前保留的文件夹
            current_folders = []
            for item in source_folder.iterdir():
                if item.is_dir() and item.name != "资料":
                    current_folders.append(item)

            # 分类并重命名
            keywords = self._get_organize_keywords()
            renamed_count = 0

            for folder in current_folders:
                folder_type = self._get_detailed_folder_type(folder.name, keywords)

                # 特殊处理：检查是否是实拍详情
                if folder_type == 'detail_image' and "实拍" in folder.name:
                    new_name = "详情实拍"
                elif folder_type in rename_map:
                    new_name = rename_map[folder_type]
                else:
                    continue  # 跳过未识别的文件夹

                # 执行重命名
                if folder.name != new_name:
                    new_path = source_folder / new_name

                    # 如果目标名称已存在，添加后缀
                    if new_path.exists():
                        counter = 1
                        while new_path.exists():
                            new_path = source_folder / f"{new_name}_{counter}"
                            counter += 1

                    try:
                        folder.rename(new_path)
                        logger.debug(f"重命名成功: {folder.name} -> {new_path.name}")
                        renamed_count += 1
                    except Exception as e:
                        logger.debug(f"重命名失败: {folder.name} -> {new_name}, 错误: {e}")

            logger.debug(f"重命名完成，共重命名 {renamed_count} 个文件夹")

            # 创建缺失的标准文件夹
            self._create_missing_standard_folders(source_folder)

            return True

        except Exception as e:
            logger.debug(f"重命名过程失败: {e}")
            return False

    def _create_missing_standard_folders(self, source_folder: Path) -> bool:
        """
        创建缺失的标准文件夹

        确保以下标准文件夹存在：主图、规格、详情、详情实拍、资料

        Args:
            source_folder: 源文件夹路径

        Returns:
            bool: 创建成功返回True
        """
        try:
            # 定义标准文件夹列表
            standard_folders = ['主图', '规格', '详情', '详情实拍', '资料']

            # 获取当前已存在的文件夹
            existing_folders = set()
            for item in source_folder.iterdir():
                if item.is_dir():
                    existing_folders.add(item.name)

            # 创建缺失的标准文件夹
            created_count = 0
            for folder_name in standard_folders:
                if folder_name not in existing_folders:
                    try:
                        folder_path = source_folder / folder_name
                        folder_path.mkdir(exist_ok=True)
                        logger.debug(f"创建缺失的标准文件夹: {folder_name}")
                        created_count += 1
                    except Exception as e:
                        logger.debug(f"创建文件夹失败 {folder_name}: {e}")

            if created_count > 0:
                logger.debug(f"共创建 {created_count} 个缺失的标准文件夹")
            else:
                logger.debug("所有标准文件夹已存在，无需创建")

            return True

        except Exception as e:
            logger.debug(f"创建缺失标准文件夹失败: {e}")
            return False

    def process_main_image_folder_deep(self, root_folder_path: Union[str, Path]) -> Optional[str]:
        """
        主图深度处理方法

        功能：
        1. 自动找到主图文件夹
        2. 处理主图文件夹中的子文件夹和文件
        3. 找到最优的无码版非白底方形图片

        Args:
            root_folder_path: 根文件夹路径

        Returns:
            str: 处理成功返回最优图片路径，失败返回None
        """
        try:
            root_folder_path = Path(root_folder_path)

            if not root_folder_path.exists() or not root_folder_path.is_dir():
                logger.debug(f"根文件夹不存在或不是目录: {root_folder_path}")
                return None

            logger.debug(f"开始主图深度处理: {root_folder_path.name}")

            # 查找主图文件夹
            main_image_folder = self._find_main_image_folder(root_folder_path)
            if not main_image_folder:
                logger.debug("未找到主图文件夹")
                return None

            logger.debug(f"找到主图文件夹: {main_image_folder.name}")

            # 获取深度处理配置
            config = self._get_main_image_deep_config()

            # 创建资料文件夹（如果不存在）
            data_folder = self._create_data_folder(root_folder_path)

            # 处理主图文件夹内容
            best_image = self._process_main_image_deep_contents(main_image_folder, data_folder, config)

            if best_image:
                logger.debug(f"主图深度处理完成，最优图片: {best_image}")
                return str(best_image)
            else:
                logger.debug("主图深度处理完成，但未找到合适的图片")
                return None

        except Exception as e:
            logger.debug(f"主图深度处理异常: {e}")
            return None

    def _find_main_image_folder(self, root_folder: Path) -> Optional[Path]:
        """
        查找主图文件夹

        Args:
            root_folder: 根文件夹

        Returns:
            Path: 主图文件夹路径，未找到返回None
        """
        try:
            # 获取关键词配置
            keywords = self._get_organize_keywords()
            main_image_keywords = keywords.get('main_image', ['主图'])

            for item in root_folder.iterdir():
                if item.is_dir():
                    folder_name_lower = item.name.lower()
                    for keyword in main_image_keywords:
                        if keyword.lower() in folder_name_lower:
                            return item

            return None

        except Exception as e:
            logger.debug(f"查找主图文件夹失败: {e}")
            return None

    def _get_main_image_deep_config(self) -> dict:
        """
        获取主图深度处理配置

        Returns:
            dict: 配置字典
        """
        try:
            config = G.main_image_deep_process
            deep_config = {
                'supported_formats': getattr(config, 'supported_formats', ['.jpg', '.jpeg', '.png']),
                'remove_keywords': getattr(config, 'remove_subfolder_keywords', ['白底', '750', '水印'])
            }

            # 获取主图优先级配置
            priority_config = G.folder_organize_keywords.priority_filter_keywords
            deep_config['main_priority'] = getattr(priority_config, 'main_image_priority', ['无水印', '800', '', '有水印', '750'])

            logger.debug(f"已加载主图深度处理配置:")
            logger.debug(f"  支持格式: {deep_config['supported_formats']} (类型: {type(deep_config['supported_formats'])})")
            logger.debug(f"  移除关键词: {deep_config['remove_keywords']}")
            logger.debug(f"  主图优先级: {deep_config['main_priority']}")

            return deep_config

        except Exception as e:
            logger.debug(f"获取主图深度处理配置失败，使用默认配置: {e}")
            return {
                'supported_formats': ['.jpg', '.jpeg', '.png'],
                'remove_keywords': ['白底', '750', '水印'],
                'main_priority': ['无水印', '800', '', '有水印', '750']
            }

    def _process_main_image_deep_contents(self, main_image_folder: Path, data_folder: Path, config: dict) -> Optional[Path]:
        """
        处理主图文件夹内容的核心逻辑（优化版）

        Args:
            main_image_folder: 主图文件夹
            data_folder: 资料文件夹
            config: 配置字典

        Returns:
            Path: 最优图片路径，未找到返回None
        """
        try:
            logger.debug(f"开始处理主图文件夹内容: {main_image_folder.name}")

            # 获取配置
            supported_formats = config['supported_formats']
            remove_keywords = config['remove_keywords']
            main_priority = config['main_priority']

            # 第一步：分析文件夹内容（不进行格式过滤）
            all_files = []
            subfolders = []

            for item in main_image_folder.iterdir():
                if item.is_file():
                    all_files.append(item)
                elif item.is_dir():
                    subfolders.append(item)

            logger.debug(f"发现 {len(all_files)} 个文件，{len(subfolders)} 个子文件夹")

            # 第二步：决定保留策略
            if all_files and subfolders:
                # 有文件也有子文件夹，检查文件中是否有方形图片
                square_images = self._find_square_images_from_all_files(all_files, supported_formats)
                if square_images:
                    logger.debug(f"发现 {len(square_images)} 个方形图片，使用根目录文件")
                    # 移除所有子文件夹
                    self._remove_all_subfolders(subfolders, data_folder)
                    # 最后进行格式过滤
                    return self._finalize_main_image_folder(main_image_folder, data_folder, supported_formats)
                else:
                    logger.debug("根目录文件无方形图片，处理子文件夹")
                    # 移除根目录所有文件，处理子文件夹
                    self._move_all_files_to_data(all_files, data_folder)
                    return self._process_subfolders_and_extract(subfolders, main_image_folder, data_folder, remove_keywords, main_priority, supported_formats)

            elif all_files and not subfolders:
                logger.debug("只有根目录文件，无子文件夹")
                # 只有文件，直接进行格式过滤
                return self._finalize_main_image_folder(main_image_folder, data_folder, supported_formats)

            elif not all_files and subfolders:
                logger.debug("只有子文件夹，无根目录文件")
                # 只有子文件夹，处理并提取内容
                return self._process_subfolders_and_extract(subfolders, main_image_folder, data_folder, remove_keywords, main_priority, supported_formats)

            else:
                logger.debug("主图文件夹为空")
                return None

        except Exception as e:
            logger.debug(f"处理主图文件夹内容失败: {e}")
            return None

    def _find_square_images_from_all_files(self, all_files: List[Path], supported_formats: List[str]) -> List[Path]:
        """
        从所有文件中查找方形图片（只检查支持格式的文件）

        Args:
            all_files: 所有文件列表
            supported_formats: 支持的图片格式

        Returns:
            List[Path]: 方形图片列表
        """
        image_files = []
        for file in all_files:
            if file.suffix.lower() in supported_formats:
                image_files.append(file)

        return self._find_square_images(image_files)

    def _move_all_files_to_data(self, files: List[Path], data_folder: Path) -> None:
        """
        将所有文件移动到资料文件夹

        Args:
            files: 文件列表
            data_folder: 资料文件夹
        """
        for file in files:
            success = self._move_item_to_data_folder(file, data_folder, "根目录文件")
            if success:
                logger.debug(f"移动根目录文件: {file.name}")

    def _process_subfolders_and_extract(self, subfolders: List[Path], main_image_folder: Path,
                                      data_folder: Path, remove_keywords: List[str],
                                      main_priority: List[str], supported_formats: List[str]) -> Optional[Path]:
        """
        处理子文件夹并提取最优文件夹的内容到主图文件夹根目录

        Args:
            subfolders: 子文件夹列表
            main_image_folder: 主图文件夹
            data_folder: 资料文件夹
            remove_keywords: 移除关键词
            main_priority: 主图优先级
            supported_formats: 支持格式

        Returns:
            Path: 最优图片路径
        """
        try:
            # 第一步：移除包含移除关键词的子文件夹
            remaining_folders = []

            for subfolder in subfolders:
                should_remove = False
                for keyword in remove_keywords:
                    if keyword.lower() in subfolder.name.lower():
                        should_remove = True
                        break

                if should_remove:
                    success = self._move_item_to_data_folder(subfolder, data_folder, f"包含移除关键词: {subfolder.name}")
                    if success:
                        logger.debug(f"移除子文件夹: {subfolder.name}")
                else:
                    remaining_folders.append(subfolder)

            if not remaining_folders:
                logger.debug("所有子文件夹都被移除")
                return None

            logger.debug(f"剩余 {len(remaining_folders)} 个子文件夹进行优先级处理")

            # 第二步：选择最优子文件夹
            if len(remaining_folders) == 1:
                best_folder = remaining_folders[0]
                logger.debug(f"只有一个子文件夹，直接选择: {best_folder.name}")
            else:
                # 多个文件夹，按优先级选择最优的
                folder_scores = []
                for folder in remaining_folders:
                    score = self._calculate_priority_score_simple(folder.name, main_priority)
                    folder_scores.append((folder, score))
                    logger.debug(f"  {folder.name}: 优先级分数 {score}")

                # 按优先级分数排序（分数越小优先级越高）
                folder_scores.sort(key=lambda x: x[1])

                # 保留优先级最高的文件夹，移动其他文件夹
                best_folder = folder_scores[0][0]
                folders_to_move = [item[0] for item in folder_scores[1:]]

                logger.debug(f"保留最高优先级子文件夹: {best_folder.name}")

                for folder in folders_to_move:
                    success = self._move_item_to_data_folder(folder, data_folder, "低优先级主图子文件夹")
                    if success:
                        logger.debug(f"移除低优先级子文件夹: {folder.name}")

            # 第三步：提取最优文件夹的内容到主图文件夹根目录
            self._extract_folder_contents_to_root(best_folder, main_image_folder)

            # 第四步：删除空的子文件夹
            try:
                best_folder.rmdir()
                logger.debug(f"删除空子文件夹: {best_folder.name}")
            except Exception as e:
                logger.debug(f"删除子文件夹失败: {e}")

            # 第五步：最终格式过滤
            return self._finalize_main_image_folder(main_image_folder, data_folder, supported_formats)

        except Exception as e:
            logger.debug(f"处理子文件夹并提取内容失败: {e}")
            return None

    def _find_square_images(self, image_files: List[Path]) -> List[Path]:
        """
        查找方形图片

        Args:
            image_files: 图片文件列表

        Returns:
            List[Path]: 方形图片列表
        """
        square_images = []

        for image_file in image_files:
            try:
                with Image.open(image_file) as img:
                    width, height = img.size
                    # 判断是否为方形（宽高相等或接近）
                    if abs(width - height) <= min(width, height) * 0.1:  # 允许10%的误差
                        square_images.append(image_file)
                        logger.debug(f"发现方形图片: {image_file.name} ({width}x{height})")
            except Exception as e:
                logger.debug(f"检查图片失败 {image_file.name}: {e}")

        return square_images

    def _remove_all_subfolders(self, subfolders: List[Path], data_folder: Path) -> None:
        """
        移除所有子文件夹到资料文件夹

        Args:
            subfolders: 子文件夹列表
            data_folder: 资料文件夹
        """
        for subfolder in subfolders:
            success = self._move_item_to_data_folder(subfolder, data_folder, "主图子文件夹")
            if success:
                logger.debug(f"移除子文件夹: {subfolder.name}")

    def _extract_folder_contents_to_root(self, source_folder: Path, target_folder: Path) -> None:
        """
        将源文件夹的内容提取到目标文件夹根目录

        Args:
            source_folder: 源文件夹
            target_folder: 目标文件夹
        """
        try:
            logger.debug(f"提取文件夹内容: {source_folder.name} -> {target_folder.name}")

            for item in source_folder.iterdir():
                if item.is_file():
                    # 移动文件到根目录
                    target_path = target_folder / item.name

                    # 如果目标文件已存在，添加数字后缀
                    if target_path.exists():
                        counter = 1
                        stem = target_path.stem
                        suffix = target_path.suffix
                        while target_path.exists():
                            target_path = target_folder / f"{stem}_{counter}{suffix}"
                            counter += 1

                    try:
                        shutil.move(str(item), str(target_path))
                        logger.debug(f"提取文件: {item.name} -> {target_path.name}")
                    except Exception as e:
                        logger.debug(f"提取文件失败 {item.name}: {e}")

        except Exception as e:
            logger.debug(f"提取文件夹内容失败: {e}")

    def _finalize_main_image_folder(self, main_image_folder: Path, data_folder: Path, supported_formats: List[str]) -> Optional[Path]:
        """
        最终处理主图文件夹：过滤格式，选择最优图片

        Args:
            main_image_folder: 主图文件夹
            data_folder: 资料文件夹
            supported_formats: 支持的图片格式

        Returns:
            Path: 最优图片路径
        """
        try:
            logger.debug("开始最终格式过滤和图片选择")

            # 获取所有文件
            all_files = [item for item in main_image_folder.iterdir() if item.is_file()]

            # 分离支持和不支持的格式
            supported_files = []
            unsupported_files = []

            for file in all_files:
                file_suffix = file.suffix.lower()
                logger.debug(f"检查文件: {file.name}, 后缀: {file_suffix}")
                if file_suffix in supported_formats:
                    supported_files.append(file)
                    logger.debug(f"  -> 支持的格式: {file.name}")
                else:
                    unsupported_files.append(file)
                    logger.debug(f"  -> 不支持的格式: {file.name}")

            # 移除不支持格式的文件
            if unsupported_files:
                logger.debug(f"移除 {len(unsupported_files)} 个不支持格式的文件")
                for unsupported_file in unsupported_files:
                    success = self._move_item_to_data_folder(unsupported_file, data_folder, f"不支持的格式: {unsupported_file.suffix}")
                    if success:
                        logger.debug(f"移除不支持格式文件: {unsupported_file.name}")

            if not supported_files:
                logger.debug("没有找到支持格式的图片文件")
                return None

            # 去除白底图
            non_white_bg_files = self._remove_white_background_images(supported_files, data_folder)

            if not non_white_bg_files:
                logger.debug("所有图片都是白底图，已移除")
                return None

            # 优先选择方形图片
            square_images = self._find_square_images(non_white_bg_files)
            if square_images:
                logger.debug(f"选择方形图片: {square_images[0].name}")
                return square_images[0]

            # 没有方形图片，返回第一个非白底图片
            logger.debug(f"选择第一个非白底图片: {non_white_bg_files[0].name}")
            return non_white_bg_files[0]

        except Exception as e:
            logger.debug(f"最终处理主图文件夹失败: {e}")
            return None

    def _remove_white_background_images(self, image_files: List[Path], data_folder: Path) -> List[Path]:
        """
        移除白底图，返回非白底图列表

        Args:
            image_files: 图片文件列表
            data_folder: 资料文件夹

        Returns:
            List[Path]: 非白底图文件列表
        """
        non_white_bg_files = []

        logger.debug(f"开始检测白底图，共 {len(image_files)} 个图片")

        for image_file in image_files:
            try:
                is_white_bg = self._is_white_background_image(image_file)

                if is_white_bg:
                    self._move_item_to_data_folder(image_file, data_folder, "白底图")
                else:
                    non_white_bg_files.append(image_file)

            except Exception as e:
                logger.debug(f"检测白底图失败 {image_file.name}: {e}")
                # 检测失败的图片保留（保守策略）
                non_white_bg_files.append(image_file)

        logger.debug(f"白底图检测完成，保留 {len(non_white_bg_files)} 个非白底图")
        return non_white_bg_files

    def _is_white_background_image(self, image_path: Path) -> bool:
        """
        判断图片是否为白底图

        检测逻辑：
        1. 优先检查文件名是否包含白底或实拍关键词
        2. 如果文件名匹配，直接判定为白底图
        3. 否则检查图片四周边缘的颜色
        4. 计算白色像素的比例
        5. 如果边缘白色像素比例超过阈值，判定为白底图

        Args:
            image_path: 图片路径

        Returns:
            bool: True表示是白底图
        """
        try:
            # 第一步：检查文件名是否包含白底关键词
            filename_lower = image_path.name.lower()
            white_bg_keywords = self._get_white_background_keywords()

            for keyword in white_bg_keywords:
                if keyword in filename_lower:
                    logger.debug(f"文件名包含白底关键词 '{keyword}': {image_path.name} -> 判定为白底图")
                    return True

            # 第二步：如果文件名不包含关键词，进行像素检测
            logger.debug(f"文件名未包含白底关键词，进行像素检测: {image_path.name}")

            with Image.open(image_path) as img:
                # 转换为RGB模式（处理RGBA、灰度图等）
                if img.mode != 'RGB':
                    img = img.convert('RGB')

                width, height = img.size

                # 如果图片太小，跳过检测
                if width < 50 or height < 50:
                    logger.debug(f"图片太小，跳过白底检测: {image_path.name} ({width}x{height})")
                    return False

                # 定义边缘检测区域的宽度（像素）
                edge_width = min(20, width // 10, height // 10)

                # 获取四周边缘的像素
                edge_pixels = []

                # 上边缘
                for x in range(width):
                    for y in range(min(edge_width, height)):
                        edge_pixels.append(img.getpixel((x, y)))

                # 下边缘
                for x in range(width):
                    for y in range(max(0, height - edge_width), height):
                        edge_pixels.append(img.getpixel((x, y)))

                # 左边缘（排除已经取过的角落）
                for y in range(edge_width, height - edge_width):
                    for x in range(min(edge_width, width)):
                        edge_pixels.append(img.getpixel((x, y)))

                # 右边缘（排除已经取过的角落）
                for y in range(edge_width, height - edge_width):
                    for x in range(max(0, width - edge_width), width):
                        edge_pixels.append(img.getpixel((x, y)))

                # 计算白色像素比例
                white_count = 0
                total_count = len(edge_pixels)

                for pixel in edge_pixels:
                    r, g, b = pixel
                    # 判断是否为白色（允许一定的容差）
                    if self._is_white_pixel(r, g, b):
                        white_count += 1

                white_ratio = white_count / total_count if total_count > 0 else 0

                # 白色比例阈值（可调整）
                white_threshold = 0.85  # 85%以上的边缘是白色则判定为白底图

                is_white_bg = white_ratio >= white_threshold

                logger.debug(f"白底检测 {image_path.name}: 边缘白色比例 {white_ratio:.2%}, 阈值 {white_threshold:.2%}, 结果: {'白底图' if is_white_bg else '非白底图'}")

                return is_white_bg

        except Exception as e:
            logger.debug(f"白底图检测异常 {image_path.name}: {e}")
            return False

    def _is_white_pixel(self, r: int, g: int, b: int, tolerance: int = 15) -> bool:
        """
        判断像素是否为白色

        Args:
            r, g, b: RGB值
            tolerance: 容差值，允许的偏差

        Returns:
            bool: True表示是白色像素
        """
        # 白色的RGB值都接近255
        return (r >= 255 - tolerance and
                g >= 255 - tolerance and
                b >= 255 - tolerance)

    def _get_white_background_keywords(self) -> List[str]:
        """
        获取白底图文件名关键词配置

        Returns:
            List[str]: 白底图关键词列表
        """
        try:
            config = G.white_background_detection
            keywords = getattr(config, 'filename_keywords', ['白底', '实拍'])
            return keywords

        except Exception as e:
            logger.debug(f"获取白底图关键词配置失败，使用默认配置: {e}")
            return ['白底', '实拍']

    def _process_subfolders_by_priority(self, subfolders: List[Path], data_folder: Path,
                                      remove_keywords: List[str], main_priority: List[str],
                                      supported_formats: List[str]) -> Optional[Path]:
        """
        按优先级处理子文件夹

        Args:
            subfolders: 子文件夹列表
            data_folder: 资料文件夹
            remove_keywords: 需要移除的关键词
            main_priority: 主图优先级列表
            supported_formats: 支持的图片格式

        Returns:
            Path: 最优图片路径，未找到返回None
        """
        try:
            # 第一步：移除包含移除关键词的子文件夹
            remaining_folders = []

            for subfolder in subfolders:
                should_remove = False
                for keyword in remove_keywords:
                    if keyword.lower() in subfolder.name.lower():
                        should_remove = True
                        break

                if should_remove:
                    success = self._move_item_to_data_folder(subfolder, data_folder, f"包含移除关键词: {subfolder.name}")
                    if success:
                        logger.debug(f"移除子文件夹: {subfolder.name}")
                else:
                    remaining_folders.append(subfolder)

            if not remaining_folders:
                logger.debug("所有子文件夹都被移除")
                return None

            logger.debug(f"剩余 {len(remaining_folders)} 个子文件夹进行优先级处理")

            # 第二步：对剩余文件夹按主图优先级处理
            if len(remaining_folders) == 1:
                # 只有一个文件夹，直接处理
                return self._get_best_image_from_folder(remaining_folders[0], supported_formats)

            # 多个文件夹，按优先级选择最优的
            folder_scores = []
            for folder in remaining_folders:
                score = self._calculate_priority_score_simple(folder.name, main_priority)
                folder_scores.append((folder, score))
                logger.debug(f"  {folder.name}: 优先级分数 {score}")

            # 按优先级分数排序（分数越小优先级越高）
            folder_scores.sort(key=lambda x: x[1])

            # 保留优先级最高的文件夹，移动其他文件夹
            best_folder = folder_scores[0][0]
            folders_to_move = [item[0] for item in folder_scores[1:]]

            logger.debug(f"保留最高优先级子文件夹: {best_folder.name}")

            for folder in folders_to_move:
                success = self._move_item_to_data_folder(folder, data_folder, "低优先级主图子文件夹")
                if success:
                    logger.debug(f"移除低优先级子文件夹: {folder.name}")

            # 从最优文件夹中获取最佳图片
            return self._get_best_image_from_folder(best_folder, supported_formats)

        except Exception as e:
            logger.debug(f"按优先级处理子文件夹失败: {e}")
            return None

    def _get_best_image_from_folder(self, folder: Path, supported_formats: List[str]) -> Optional[Path]:
        """
        从文件夹中获取最佳图片

        Args:
            folder: 文件夹路径
            supported_formats: 支持的图片格式

        Returns:
            Path: 最佳图片路径，未找到返回None
        """
        try:
            image_files = []

            for item in folder.iterdir():
                if item.is_file():
                    file_suffix = item.suffix.lower()
                    logger.debug(f"检查子文件夹文件: {item.name}, 后缀: {file_suffix}, 支持格式: {supported_formats}")
                    if file_suffix in supported_formats:
                        image_files.append(item)
                        logger.debug(f"  -> 添加到图片文件列表: {item.name}")
                    else:
                        logger.debug(f"  -> 跳过不支持的格式: {item.name}")

            if not image_files:
                logger.debug(f"文件夹 {folder.name} 中没有找到图片文件")
                return None

            # 优先选择方形图片
            square_images = self._find_square_images(image_files)
            if square_images:
                logger.debug(f"从 {folder.name} 中选择方形图片: {square_images[0].name}")
                return square_images[0]

            # 没有方形图片，返回第一个图片
            logger.debug(f"从 {folder.name} 中选择第一个图片: {image_files[0].name}")
            return image_files[0]

        except Exception as e:
            logger.debug(f"从文件夹获取最佳图片失败: {e}")
            return None

    def process_detail_image_folder_deep(self, root_folder_path: Union[str, Path]) -> Optional[str]:
        """
        详情深度处理方法（对外接口）

        功能：
        1. 自动找到详情文件夹
        2. 如果详情文件夹有足够的小文件（<1MB），直接使用
        3. 如果没有足够文件，处理子文件夹：移除敏感词，按文件夹大小和gif内容选择最优
        4. 最终保留gif，去除长图和大文件

        Args:
            root_folder_path: 根文件夹路径

        Returns:
            str: 处理成功返回详情文件夹路径，失败返回None
        """
        try:
            root_folder_path = Path(root_folder_path)

            if not root_folder_path.exists() or not root_folder_path.is_dir():
                logger.debug(f"根文件夹不存在或不是目录: {root_folder_path}")
                return None

            logger.debug(f"开始详情深度处理: {root_folder_path.name}")

            # 查找详情文件夹
            detail_folder = self._find_detail_image_folder(root_folder_path)
            if not detail_folder:
                logger.debug("未找到详情文件夹")
                return None

            logger.debug(f"找到详情文件夹: {detail_folder.name}")

            # 获取配置
            config = self._get_detail_deep_config()

            # 创建资料文件夹（如果不存在）
            data_folder = self._create_data_folder(root_folder_path)

            # 处理详情文件夹内容
            success = self._process_detail_image_deep_contents(detail_folder, data_folder, config)

            if success:
                logger.debug(f"详情深度处理完成: {detail_folder}")
                return str(detail_folder)
            else:
                logger.debug("详情深度处理失败")
                return None

        except Exception as e:
            logger.debug(f"详情深度处理异常: {e}")
            return None

    def _find_detail_image_folder(self, root_folder: Path) -> Optional[Path]:
        """
        查找详情文件夹

        Args:
            root_folder: 根文件夹

        Returns:
            Path: 详情文件夹路径，未找到返回None
        """
        try:
            # 获取关键词配置
            keywords = self._get_organize_keywords()
            detail_keywords = keywords.get('detail_image', ['详情'])

            for item in root_folder.iterdir():
                if item.is_dir():
                    folder_name_lower = item.name.lower()
                    for keyword in detail_keywords:
                        if keyword.lower() in folder_name_lower:
                            return item

            return None

        except Exception as e:
            logger.debug(f"查找详情文件夹失败: {e}")
            return None

    def _get_detail_deep_config(self) -> dict:
        """
        获取详情深度处理配置

        Returns:
            dict: 配置字典
        """
        try:
            config = G.detail_image_deep_process
            detail_config = {
                'supported_formats': getattr(config, 'supported_formats', ['.jpg', '.jpeg', '.png', '.gif']),
                'small_file_threshold': getattr(config, 'small_file_threshold', 1048576),  # 1MB
                'large_file_threshold': getattr(config, 'large_file_threshold', 3145728),  # 3MB
                'min_root_files_count': getattr(config, 'min_root_files_count', 5),
                'remove_keywords': getattr(config, 'remove_detail_subfolder_keywords', ['白底', '水印']),
                'height_width_ratio': getattr(config.long_image_detection, 'height_width_ratio', 5.0),
                'standard_slice_height': getattr(config.long_image_detection, 'standard_slice_height', 4000)
            }

            logger.debug(f"已加载详情深度处理配置:")
            logger.debug(f"  支持格式: {detail_config['supported_formats']}")
            logger.debug(f"  小文件阈值: {detail_config['small_file_threshold']} bytes")
            logger.debug(f"  大文件阈值: {detail_config['large_file_threshold']} bytes")
            logger.debug(f"  最少文件数: {detail_config['min_root_files_count']}")
            logger.debug(f"  移除关键词: {detail_config['remove_keywords']}")

            return detail_config

        except Exception as e:
            logger.debug(f"获取详情深度处理配置失败，使用默认配置: {e}")
            return {
                'supported_formats': ['.jpg', '.jpeg', '.png', '.gif'],
                'small_file_threshold': 1048576,  # 1MB
                'large_file_threshold': 3145728,  # 3MB
                'min_root_files_count': 5,
                'remove_keywords': ['白底', '水印'],
                'height_width_ratio': 5.0,
                'standard_slice_height': 4000
            }

    def _process_detail_image_deep_contents(self, detail_folder: Path, data_folder: Path, config: dict) -> bool:
        """
        处理详情文件夹内容的核心逻辑

        Args:
            detail_folder: 详情文件夹
            data_folder: 资料文件夹
            config: 配置字典

        Returns:
            bool: 处理成功返回True
        """
        try:
            logger.debug(f"开始处理详情文件夹内容: {detail_folder.name}")

            # 获取配置
            supported_formats = config['supported_formats']
            small_file_threshold = config['small_file_threshold']
            min_root_files_count = config['min_root_files_count']
            remove_keywords = config['remove_keywords']

            # 第一步：分析文件夹内容
            all_files = []
            subfolders = []

            for item in detail_folder.iterdir():
                if item.is_file():
                    all_files.append(item)
                elif item.is_dir():
                    subfolders.append(item)

            logger.debug(f"发现 {len(all_files)} 个文件，{len(subfolders)} 个子文件夹")

            # 第二步：检查根目录文件是否满足条件
            small_image_files = self._get_small_image_files(all_files, supported_formats, small_file_threshold)

            if len(small_image_files) >= min_root_files_count:
                logger.debug(f"根目录有 {len(small_image_files)} 个小文件（>= {min_root_files_count}），直接使用根目录")
                # 移除所有子文件夹
                self._remove_all_subfolders(subfolders, data_folder)
                # 最终处理根目录文件
                return self._finalize_detail_folder(detail_folder, data_folder, config)

            elif not all_files and subfolders:
                logger.debug("根目录无文件，处理子文件夹")
                # 只有子文件夹，处理并提取内容
                return self._process_detail_subfolders_and_extract(subfolders, detail_folder, data_folder, remove_keywords, config)

            elif all_files and subfolders:
                logger.debug(f"根目录文件不足（{len(small_image_files)} < {min_root_files_count}），移除根目录文件，处理子文件夹")
                # 文件不足，移除根目录文件，处理子文件夹
                self._move_all_files_to_data(all_files, data_folder)
                return self._process_detail_subfolders_and_extract(subfolders, detail_folder, data_folder, remove_keywords, config)

            elif all_files and not subfolders:
                logger.debug("只有根目录文件，无子文件夹，直接处理")
                # 只有文件，直接处理
                return self._finalize_detail_folder(detail_folder, data_folder, config)

            else:
                logger.debug("详情文件夹为空")
                return False

        except Exception as e:
            logger.debug(f"处理详情文件夹内容失败: {e}")
            return False

    def _get_small_image_files(self, all_files: List[Path], supported_formats: List[str], size_threshold: int) -> List[Path]:
        """获取小于指定大小的图片文件"""
        small_files = []
        for file in all_files:
            try:
                if file.suffix.lower() not in supported_formats:
                    continue
                file_size = file.stat().st_size
                if file_size < size_threshold:
                    small_files.append(file)
            except Exception as e:
                logger.debug(f"检查文件失败 {file.name}: {e}")
        return small_files

    def _process_detail_subfolders_and_extract(self, subfolders: List[Path], detail_folder: Path,
                                             data_folder: Path, remove_keywords: List[str], config: dict) -> bool:
        """处理详情子文件夹并提取最优文件夹的内容"""
        try:
            # 移除包含移除关键词的子文件夹
            remaining_folders = []
            for subfolder in subfolders:
                should_remove = False
                for keyword in remove_keywords:
                    if keyword.lower() in subfolder.name.lower():
                        should_remove = True
                        break

                if should_remove:
                    success = self._move_item_to_data_folder(subfolder, data_folder, f"包含移除关键词: {subfolder.name}")
                    if success:
                        logger.debug(f"移除详情子文件夹: {subfolder.name}")
                else:
                    remaining_folders.append(subfolder)

            if not remaining_folders:
                logger.debug("所有详情子文件夹都被移除")
                return False

            # 选择最优子文件夹（按文件夹大小和gif内容）
            if len(remaining_folders) == 1:
                best_folder = remaining_folders[0]
            else:
                best_folder = self._select_best_detail_folder(remaining_folders)
                # 移除其他文件夹
                folders_to_move = [folder for folder in remaining_folders if folder != best_folder]
                for folder in folders_to_move:
                    success = self._move_item_to_data_folder(folder, data_folder, "非最优详情子文件夹")
                    if success:
                        logger.debug(f"移除详情子文件夹: {folder.name}")

            # 提取最优文件夹的内容到详情文件夹根目录
            self._extract_folder_contents_to_root(best_folder, detail_folder)

            # 删除空的子文件夹
            try:
                best_folder.rmdir()
                logger.debug(f"删除空详情子文件夹: {best_folder.name}")
            except Exception as e:
                logger.debug(f"删除详情子文件夹失败: {e}")

            # 最终处理详情文件夹
            return self._finalize_detail_folder(detail_folder, data_folder, config)

        except Exception as e:
            logger.debug(f"处理详情子文件夹失败: {e}")
            return False

    def _select_best_detail_folder(self, folders: List[Path]) -> Path:
        """选择最优的详情文件夹（按文件夹大小和gif内容）"""
        try:
            logger.debug("开始按文件夹大小和gif内容选择最优文件夹")

            folder_info = []
            for folder in folders:
                folder_size = self._get_folder_size(folder)
                has_gif = self._folder_contains_gif(folder)
                folder_info.append({'folder': folder, 'size': folder_size, 'has_gif': has_gif})
                logger.debug(f"文件夹分析: {folder.name} - 大小: {folder_size} bytes, 包含gif: {has_gif}")

            # 按大小排序
            folder_info.sort(key=lambda x: x['size'])
            smallest_folder = folder_info[0]

            # 找到包含gif的最小文件夹
            gif_folders = [info for info in folder_info if info['has_gif']]
            smallest_gif_folder = gif_folders[0] if gif_folders else None

            # 决策逻辑
            if smallest_gif_folder is None:
                selected = smallest_folder['folder']
                logger.debug(f"没有gif文件夹，选择最小文件夹: {selected.name}")
            elif smallest_gif_folder['folder'] == smallest_folder['folder']:
                selected = smallest_folder['folder']
                logger.debug(f"最小文件夹包含gif，直接选择: {selected.name}")
            else:
                selected = smallest_gif_folder['folder']
                logger.debug(f"优先选择包含gif的文件夹: {selected.name}")

            return selected
        except Exception as e:
            logger.debug(f"选择最优详情文件夹失败: {e}")
            return folders[0]

    def _get_folder_size(self, folder: Path) -> int:
        """获取文件夹大小"""
        try:
            total_size = 0
            for item in folder.rglob('*'):
                if item.is_file():
                    total_size += item.stat().st_size
            return total_size
        except Exception:
            return 0

    def _folder_contains_gif(self, folder: Path) -> bool:
        """检查文件夹是否包含gif文件"""
        try:
            for item in folder.rglob('*.gif'):
                if item.is_file():
                    return True
            return False
        except Exception:
            return False

    def _finalize_detail_folder(self, detail_folder: Path, data_folder: Path, config: dict) -> bool:
        """最终处理详情文件夹：过滤格式，去除长图和大文件"""
        try:
            logger.debug("开始最终详情文件夹处理")

            supported_formats = config['supported_formats']
            large_file_threshold = config['large_file_threshold']
            height_width_ratio = config['height_width_ratio']
            standard_slice_height = config['standard_slice_height']

            all_files = [item for item in detail_folder.iterdir() if item.is_file()]

            # 分离支持和不支持的格式
            supported_files = []
            for file in all_files:
                if file.suffix.lower() in supported_formats:
                    supported_files.append(file)
                else:
                    self._move_item_to_data_folder(file, data_folder, f"不支持的格式: {file.suffix}")

            # 过滤大文件和长图
            final_files = []
            for file in supported_files:
                try:
                    file_size = file.stat().st_size
                    is_gif = file.suffix.lower() == '.gif'

                    # 检查文件大小（gif文件允许更大）
                    if not is_gif and file_size > large_file_threshold:
                        logger.debug(f"移除大文件: {file.name}")
                        self._move_item_to_data_folder(file, data_folder, f"文件过大: {file_size} bytes")
                        continue

                    # 检查是否为长图（gif文件跳过长图检测）
                    if not is_gif and self._is_long_image(file, height_width_ratio, standard_slice_height):
                        logger.debug(f"移除长图: {file.name}")
                        self._move_item_to_data_folder(file, data_folder, "长图")
                        continue

                    final_files.append(file)
                    logger.debug(f"保留文件: {file.name}")
                except Exception as e:
                    logger.debug(f"检查文件失败 {file.name}: {e}")
                    final_files.append(file)

            logger.debug(f"最终保留 {len(final_files)} 个文件")
            return len(final_files) > 0
        except Exception as e:
            logger.debug(f"最终处理详情文件夹失败: {e}")
            return False

    def _is_long_image(self, image_path: Path, height_width_ratio: float, standard_slice_height: int) -> bool:
        """判断图片是否为长图"""
        try:
            with Image.open(image_path) as img:
                width, height = img.size
                if height > width * height_width_ratio or height > standard_slice_height:
                    return True
                return False
        except Exception:
            return False

    def filter_sensitive_files_in_image_folders(self, root_folder_path: Union[str, Path]) -> bool:
        """
        去除主图和详情文件夹中包含特殊关键词的文件

        功能：
        1. 查找主图和详情文件夹
        2. 检查文件夹中的文件名是否包含敏感关键词
        3. 将包含敏感关键词的文件移动到资料文件夹

        Args:
            root_folder_path: 根文件夹路径

        Returns:
            bool: 处理成功返回True，失败返回False
        """
        try:
            root_folder_path = Path(root_folder_path)

            if not root_folder_path.exists() or not root_folder_path.is_dir():
                logger.debug(f"根文件夹不存在或不是目录: {root_folder_path}")
                return False

            logger.debug(f"开始过滤敏感文件: {root_folder_path.name}")

            # 获取敏感关键词配置
            sensitive_keywords = self._get_sensitive_file_keywords()
            if not sensitive_keywords:
                logger.debug("未找到敏感关键词配置，跳过过滤")
                return True

            # 创建资料文件夹（如果不存在）
            data_folder = self._create_data_folder(root_folder_path)
            if not data_folder:
                return False

            # 查找主图和详情文件夹
            target_folders = self._find_image_folders_for_filtering(root_folder_path)

            if not target_folders:
                logger.debug("未找到主图或详情文件夹")
                return True

            # 获取最少文件数配置
            min_files_after_filter = self._get_min_files_after_filter()

            # 过滤每个目标文件夹中的敏感文件
            total_moved = 0
            for folder_type, folder_path in target_folders.items():
                # 先分析关键词频率，过滤掉高频关键词
                filtered_keywords = self._filter_high_frequency_keywords(
                    folder_path, sensitive_keywords, folder_type
                )

                moved_count = self._filter_sensitive_files_in_folder(
                    folder_path, data_folder, filtered_keywords, folder_type
                )
                total_moved += moved_count

            # 检查过滤后每个文件夹的文件数量
            for folder_type, folder_path in target_folders.items():
                remaining_files = self._count_image_files_in_folder(folder_path)
                logger.debug(f"{folder_type} 文件夹过滤后剩余 {remaining_files} 个图片文件")

                if remaining_files < min_files_after_filter:
                    logger.debug(f"警告: {folder_type} 文件夹过滤后文件数量不足 ({remaining_files} < {min_files_after_filter})")
                    return False

            logger.debug(f"敏感文件过滤完成，共移动 {total_moved} 个敏感文件到资料文件夹")
            logger.debug("所有图片文件夹过滤后文件数量充足")
            return True

        except Exception as e:
            logger.debug(f"过滤敏感文件异常: {e}")
            return False

    def _get_sensitive_file_keywords(self) -> List[str]:
        """
        获取敏感文件关键词配置

        Returns:
            List[str]: 敏感关键词列表
        """
        try:
            config = G.file_filter_keywords
            keywords = getattr(config, 'remove_file_keywords', [])
            return keywords

        except Exception as e:
            logger.debug(f"获取敏感文件关键词配置失败，使用默认配置: {e}")
            return [
                "私处", "遮挡", "臀", "屁股", "展示", "撩衣", "舌头", "漏", "乳", "走光",
                "姿势", "衣", "故意", "不", "腿", "倒m", "扯", "要求", "手", "肩", "使用",
                "建议", "内裤", "咪咪", "若隐若现", "白", "违规", "内", "文", "加", "深",
                "疑似", "动作", "扒", "有", "嫌疑"
            ]

    def _get_min_files_after_filter(self) -> int:
        """
        获取过滤后最少文件数配置

        Returns:
            int: 最少文件数
        """
        try:
            config = G.file_filter_keywords
            min_files = getattr(config, 'min_files_after_filter', 3)

            logger.debug(f"已加载过滤后最少文件数配置: {min_files}")

            return min_files

        except Exception as e:
            logger.debug(f"获取过滤后最少文件数配置失败，使用默认值3: {e}")
            return 3

    def _count_image_files_in_folder(self, folder_path: Path) -> int:
        """
        统计文件夹中的图片文件数量

        Args:
            folder_path: 文件夹路径

        Returns:
            int: 图片文件数量
        """
        try:
            # 支持的图片格式
            image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff', '.tif'}

            count = 0

            # 递归统计所有图片文件
            for item in folder_path.rglob('*'):
                if item.is_file() and item.suffix.lower() in image_extensions:
                    count += 1

            return count

        except Exception as e:
            logger.debug(f"统计文件夹图片文件数量失败: {e}")
            return 0

    def _filter_high_frequency_keywords(self, folder_path: Path, keywords: List[str], folder_type: str) -> List[str]:
        """
        过滤掉高频关键词（如果80%以上的文件都包含某个关键词，则忽略该关键词）

        Args:
            folder_path: 文件夹路径
            keywords: 原始关键词列表
            folder_type: 文件夹类型

        Returns:
            List[str]: 过滤后的关键词列表
        """
        try:
            logger.debug(f"=== 开始分析 {folder_type} 文件夹关键词频率 ===")

            # 收集所有图片文件
            image_files = self._collect_image_files_from_folder(folder_path, exclude_subfolders=False)
            total_files = len(image_files)

            if total_files == 0:
                logger.debug(f"{folder_type} 文件夹中没有图片文件")
                return keywords

            logger.debug(f"{folder_type} 文件夹总文件数: {total_files}")

            # 分析每个关键词的频率
            keyword_counts = {}
            for keyword in keywords:
                count = 0
                matching_files = []

                for file_path in image_files:
                    filename_lower = file_path.name.lower()
                    if keyword.lower() in filename_lower:
                        count += 1
                        matching_files.append(file_path.name)

                keyword_counts[keyword] = {
                    'count': count,
                    'percentage': (count / total_files) * 100,
                    'files': matching_files
                }

            # 过滤高频关键词（从配置获取阈值）
            frequency_threshold = self._get_high_frequency_threshold()
            filtered_keywords = []
            ignored_keywords = []

            for keyword in keywords:
                stats = keyword_counts[keyword]
                percentage = stats['percentage']

                logger.debug(f"关键词 '{keyword}': {stats['count']}/{total_files} 文件 ({percentage:.1f}%)")

                if percentage >= frequency_threshold:
                    ignored_keywords.append(keyword)
                    logger.debug(f"  -> 忽略高频关键词 '{keyword}' (超过{frequency_threshold}%阈值)")
                    # 显示部分匹配的文件名
                    sample_files = stats['files'][:3]
                    if len(sample_files) > 0:
                        logger.debug(f"     示例文件: {', '.join(sample_files)}")
                        if len(stats['files']) > 3:
                            logger.debug(f"     还有 {len(stats['files']) - 3} 个文件...")
                else:
                    filtered_keywords.append(keyword)
                    logger.debug(f"  -> 保留关键词 '{keyword}'")

            if ignored_keywords:
                logger.debug(f"\n{folder_type} 文件夹忽略的高频关键词: {ignored_keywords}")

            logger.debug(f"{folder_type} 文件夹最终使用的关键词: {filtered_keywords}")

            return filtered_keywords

        except Exception as e:
            logger.debug(f"分析关键词频率失败: {e}")
            return keywords

    def _get_high_frequency_threshold(self) -> float:
        """
        获取高频关键词忽略阈值配置

        Returns:
            float: 阈值百分比
        """
        try:
            config = G.file_filter_keywords
            threshold = getattr(config, 'high_frequency_threshold', 80.0)

            logger.debug(f"已加载高频关键词阈值配置: {threshold}%")

            return threshold

        except Exception as e:
            logger.debug(f"获取高频关键词阈值配置失败，使用默认值80%: {e}")
            return 80.0

    def _find_image_folders_for_filtering(self, root_folder: Path) -> dict:
        """
        查找需要过滤的图片文件夹（主图和详情）

        Args:
            root_folder: 根文件夹

        Returns:
            dict: 文件夹类型和路径的字典
        """
        try:
            # 获取关键词配置
            keywords = self._get_organize_keywords()
            main_image_keywords = keywords.get('main_image', ['主图'])
            detail_image_keywords = keywords.get('detail_image', ['详情'])

            target_folders = {}

            # 收集所有候选文件夹
            main_candidates = []
            detail_candidates = []

            logger.debug(f"开始查找图片文件夹，根目录: {root_folder.name}")
            logger.debug(f"主图关键词: {main_image_keywords}")
            logger.debug(f"详情关键词: {detail_image_keywords}")

            for item in root_folder.iterdir():
                if item.is_dir():
                    folder_name_lower = item.name.lower()

                    # 检查是否是主图文件夹
                    for keyword in main_image_keywords:
                        if keyword.lower() in folder_name_lower:
                            main_candidates.append(item)
                            logger.debug(f"  -> 主图候选: {item.name} (匹配关键词: {keyword})")
                            break

                    # 检查是否是详情文件夹
                    for keyword in detail_image_keywords:
                        if keyword.lower() in folder_name_lower:
                            detail_candidates.append(item)
                            logger.debug(f"  -> 详情候选: {item.name} (匹配关键词: {keyword})")
                            break

            # 选择最佳的主图文件夹（优先精确匹配）
            if main_candidates:
                best_main = self._select_best_folder_match(main_candidates, main_image_keywords)
                target_folders['main_image'] = best_main
                logger.debug(f"找到主图文件夹: {best_main.name}")

            # 选择最佳的详情文件夹（优先精确匹配）
            if detail_candidates:
                best_detail = self._select_best_folder_match(detail_candidates, detail_image_keywords)
                target_folders['detail_image'] = best_detail
                logger.debug(f"找到详情文件夹: {best_detail.name}")

            return target_folders

        except Exception as e:
            logger.debug(f"查找图片文件夹失败: {e}")
            return {}

    def _select_best_folder_match(self, candidates: List[Path], keywords: List[str]) -> Path:
        """
        从候选文件夹中选择最佳匹配

        优先级：
        1. 精确匹配关键词的文件夹
        2. 包含关键词且名称最短的文件夹

        Args:
            candidates: 候选文件夹列表
            keywords: 关键词列表

        Returns:
            Path: 最佳匹配的文件夹
        """
        try:
            if len(candidates) == 1:
                return candidates[0]

            # 按优先级排序
            exact_matches = []
            partial_matches = []

            for folder in candidates:
                folder_name_lower = folder.name.lower()

                # 检查是否有精确匹配
                is_exact_match = False
                for keyword in keywords:
                    if folder_name_lower == keyword.lower():
                        exact_matches.append(folder)
                        is_exact_match = True
                        break

                if not is_exact_match:
                    partial_matches.append(folder)

            # 优先返回精确匹配
            if exact_matches:
                logger.debug(f"找到精确匹配文件夹: {exact_matches[0].name}")
                return exact_matches[0]

            # 如果没有精确匹配，返回名称最短的（通常是最基础的）
            shortest_folder = min(partial_matches, key=lambda x: len(x.name))
            logger.debug(f"选择最短名称文件夹: {shortest_folder.name} (从 {[f.name for f in partial_matches]} 中选择)")
            return shortest_folder

        except Exception as e:
            logger.debug(f"选择最佳文件夹匹配失败: {e}")
            return candidates[0]

    def _filter_sensitive_files_in_folder(self, folder_path: Path, data_folder: Path,
                                        sensitive_keywords: List[str], folder_type: str) -> int:
        """
        过滤文件夹中的敏感文件

        Args:
            folder_path: 要过滤的文件夹路径
            data_folder: 资料文件夹路径
            sensitive_keywords: 敏感关键词列表
            folder_type: 文件夹类型（用于日志）

        Returns:
            int: 移动的文件数量
        """
        try:
            logger.debug(f"开始过滤 {folder_type} 文件夹中的敏感文件: {folder_path.name}")

            moved_count = 0

            # 遍历文件夹中的所有文件
            for item in folder_path.iterdir():
                if item.is_file():
                    # 检查文件名是否包含敏感关键词
                    file_name_lower = item.name.lower()
                    is_sensitive = False
                    matched_keyword = ""

                    for keyword in sensitive_keywords:
                        if keyword.lower() in file_name_lower:
                            is_sensitive = True
                            matched_keyword = keyword
                            break

                    if is_sensitive:
                        # 移动敏感文件到资料文件夹
                        success = self._move_item_to_data_folder(
                            item, data_folder, f"包含敏感关键词'{matched_keyword}'"
                        )
                        if success:
                            moved_count += 1
                            logger.debug(f"移除敏感文件: {item.name} (关键词: {matched_keyword})")
                    else:
                        logger.debug(f"保留文件: {item.name}")

                elif item.is_dir():
                    # 递归处理子文件夹
                    sub_moved = self._filter_sensitive_files_in_folder(
                        item, data_folder, sensitive_keywords, f"{folder_type}子文件夹"
                    )
                    moved_count += sub_moved

            logger.debug(f"{folder_type} 文件夹过滤完成，移动了 {moved_count} 个敏感文件")
            return moved_count

        except Exception as e:
            logger.debug(f"过滤 {folder_type} 文件夹中的敏感文件失败: {e}")
            return 0

    def clean_spec_folder_non_images(self, root_folder_path: Union[str, Path]) -> bool:
        """
        清理规格文件夹中的非图片文件

        功能：
        1. 查找规格文件夹
        2. 删除文件夹中的所有非图片文件
        3. 保留图片格式文件

        Args:
            root_folder_path: 根文件夹路径

        Returns:
            bool: 处理成功返回True，失败返回False
        """
        try:
            root_folder_path = Path(root_folder_path)

            if not root_folder_path.exists() or not root_folder_path.is_dir():
                logger.debug(f"根文件夹不存在或不是目录: {root_folder_path}")
                return False

            logger.debug(f"开始清理规格文件夹非图片文件: {root_folder_path.name}")

            # 查找规格文件夹
            spec_folder = self._find_spec_folder(root_folder_path)
            if not spec_folder:
                logger.debug("未找到规格文件夹")
                return True  # 没有规格文件夹不算失败

            logger.debug(f"找到规格文件夹: {spec_folder.name}")

            # 创建资料文件夹（如果不存在）
            data_folder = self._create_data_folder(root_folder_path)
            if not data_folder:
                return False

            # 清理非图片文件
            removed_count = self._remove_non_image_files_from_folder(spec_folder, data_folder)

            logger.debug(f"规格文件夹清理完成，移除了 {removed_count} 个非图片文件")
            return True

        except Exception as e:
            logger.debug(f"清理规格文件夹非图片文件异常: {e}")
            return False

    def sort_and_rename_main_images(self, root_folder_path: Union[str, Path]) -> bool:
        """
        对主图文件夹中的图片进行自然排序并重命名

        功能：
        1. 查找主图文件夹
        2. 按自然语言顺序排序所有图片文件
        3. 重命名为 主图_01, 主图_02, ... 格式

        Args:
            root_folder_path: 根文件夹路径

        Returns:
            bool: 处理成功返回True，失败返回False
        """
        try:
            root_folder_path = Path(root_folder_path)

            if not root_folder_path.exists() or not root_folder_path.is_dir():
                logger.debug(f"根文件夹不存在或不是目录: {root_folder_path}")
                return False

            logger.debug(f"开始主图排序重命名: {root_folder_path.name}")

            # 查找主图文件夹
            main_folder = self._find_main_folder_for_sorting(root_folder_path)
            if not main_folder:
                logger.debug("未找到主图文件夹")
                return False

            logger.debug(f"找到主图文件夹: {main_folder.name}")

            # 收集所有图片文件（包括子文件夹）
            all_image_files = self._collect_image_files_from_folder(main_folder, exclude_subfolders=False)

            if not all_image_files:
                logger.debug("未找到任何图片文件")
                return False

            logger.debug(f"主图文件夹图片文件: {len(all_image_files)} 个")

            # 检查是否需要特殊处理（同时存在"主图"和"辅图"关键词文件）
            sorted_files = self._check_and_handle_main_auxiliary_mix(all_image_files)

            # 打印排序结果
            logger.debug(f"=== 主图排序结果 (共{len(sorted_files)}个文件) ===")
            for i, file_path in enumerate(sorted_files, 1):
                logger.debug(f"{i:02d}. {file_path.name}")

            # 重命名文件
            success = self._rename_main_files(sorted_files, main_folder)

            if success:
                logger.debug(f"主图排序重命名完成: {len(sorted_files)} 个文件")
                return True
            else:
                logger.debug("主图重命名失败")
                return False

        except Exception as e:
            logger.debug(f"主图排序重命名异常: {e}")
            return False

    def _check_and_handle_main_auxiliary_mix(self, all_image_files: List[Path]) -> List[Path]:
        """
        检查并处理主图辅图混合情况
        只有当同时存在包含"主图"和"辅图"关键词的文件时才特殊处理

        Args:
            all_image_files: 所有图片文件列表

        Returns:
            List[Path]: 最终排序后的文件列表
        """
        try:
            # 分类文件：包含"主图"关键词的 vs 包含"辅图"关键词的 vs 其他文件
            main_keyword_files = []
            auxiliary_keyword_files = []
            other_files = []

            for image_file in all_image_files:
                file_name = image_file.name.lower()
                if "主图" in file_name:
                    main_keyword_files.append(image_file)
                elif "辅图" in file_name:
                    auxiliary_keyword_files.append(image_file)
                else:
                    other_files.append(image_file)

            # 检查是否需要特殊处理：同时存在"主图"和"辅图"关键词文件
            if main_keyword_files and auxiliary_keyword_files:
                logger.debug(f"=== 检测到主图辅图混合情况，启用特殊处理 ===")
                logger.debug(f"包含'主图'关键词的文件: {len(main_keyword_files)} 张")
                for img in main_keyword_files:
                    logger.debug(f"  - {img.name}")
                logger.debug(f"包含'辅图'关键词的文件: {len(auxiliary_keyword_files)} 张")
                for img in auxiliary_keyword_files:
                    logger.debug(f"  - {img.name}")
                if other_files:
                    logger.debug(f"其他文件: {len(other_files)} 张")
                    for img in other_files:
                        logger.debug(f"  - {img.name}")

                # 特殊处理：主图关键词随机选1张，辅图关键词全选，其他文件全选
                import random
                selected_main = random.choice(main_keyword_files)
                logger.debug(f"从包含'主图'关键词的文件中随机选择: {selected_main.name}")

                # 将多余的主图关键词文件移入资料文件夹
                unused_main_files = [f for f in main_keyword_files if f != selected_main]
                if unused_main_files:
                    self._move_unused_main_files_to_data(unused_main_files, selected_main.parent)
                    logger.debug(f"已将 {len(unused_main_files)} 张多余的主图文件移入资料文件夹")

                # 对辅图和其他文件进行自然排序
                remaining_files = auxiliary_keyword_files + other_files
                sorted_remaining = self._natural_sort_files(remaining_files)

                # 构建最终列表：选中的主图 + 排序后的辅图和其他文件
                final_list = [selected_main] + sorted_remaining

                logger.debug(f"=== 特殊处理结果 (共{len(final_list)}张) ===")
                for i, file_path in enumerate(final_list, 1):
                    if i == 1:
                        file_type = "主图(随机选择)"
                    elif file_path in auxiliary_keyword_files:
                        file_type = "辅图"
                    else:
                        file_type = "其他"
                    logger.debug(f"{i:02d}. {file_path.name} ({file_type})")

                return final_list
            else:
                # 不是混合情况，使用原有逻辑：按自然语言顺序排序
                logger.debug("=== 使用原有排序逻辑 ===")
                return self._natural_sort_files(all_image_files)

        except Exception as e:
            logger.debug(f"处理主图辅图混合失败: {e}")
            # 如果处理失败，回退到原有逻辑
            return self._natural_sort_files(all_image_files)

    def _move_unused_main_files_to_data(self, unused_files: List[Path], main_folder: Path) -> bool:
        """
        将多余的主图文件移入资料文件夹

        Args:
            unused_files: 多余的主图文件列表
            main_folder: 主图文件夹路径

        Returns:
            bool: 移动成功返回True
        """
        try:
            # 查找或创建资料文件夹
            root_folder = main_folder.parent
            data_folder = root_folder / "资料"

            if not data_folder.exists():
                data_folder.mkdir(parents=True, exist_ok=True)
                logger.debug(f"创建资料文件夹: {data_folder}")

            # 移动多余的主图文件
            moved_count = 0
            for file_path in unused_files:
                try:
                    target_path = data_folder / file_path.name

                    # 如果目标文件已存在，添加序号
                    counter = 1
                    original_target = target_path
                    while target_path.exists():
                        stem = original_target.stem
                        suffix = original_target.suffix
                        target_path = data_folder / f"{stem}_{counter}{suffix}"
                        counter += 1

                    # 移动文件
                    file_path.rename(target_path)
                    logger.debug(f"移动文件: {file_path.name} -> 资料/{target_path.name}")
                    moved_count += 1

                except Exception as e:
                    logger.debug(f"移动文件失败 {file_path.name}: {e}")

            return moved_count > 0

        except Exception as e:
            logger.debug(f"移动多余主图文件到资料文件夹失败: {e}")
            return False

    def sort_and_rename_detail_images(self, root_folder_path: Union[str, Path]) -> bool:
        """
        对详情文件夹中的图片进行自然排序并重命名

        功能：
        1. 查找详情文件夹
        2. 如果存在详情实拍文件夹，先处理其中的文件
        3. 按自然语言顺序排序所有图片文件
        4. 重命名为 详情_01, 详情_02, ... 格式

        Args:
            root_folder_path: 根文件夹路径

        Returns:
            bool: 处理成功返回True，失败返回False
        """
        try:
            root_folder_path = Path(root_folder_path)

            if not root_folder_path.exists() or not root_folder_path.is_dir():
                logger.debug(f"根文件夹不存在或不是目录: {root_folder_path}")
                return False

            logger.debug(f"开始详情图片排序重命名: {root_folder_path.name}")

            # 查找详情文件夹
            detail_folder = self._find_detail_folder_for_sorting(root_folder_path)
            if not detail_folder:
                logger.debug("未找到详情文件夹")
                return False

            logger.debug(f"找到详情文件夹: {detail_folder.name}")

            # 查找详情实拍文件夹（在根目录中查找）
            detail_real_folder = self._find_detail_real_folder(root_folder_path)

            # 收集所有需要排序的图片文件
            all_image_files = []

            # 先收集详情文件夹根目录的图片
            detail_root_files = self._collect_image_files_from_folder(detail_folder, exclude_subfolders=True)
            all_image_files.extend(detail_root_files)
            logger.debug(f"详情根目录图片文件: {len(detail_root_files)} 个")

            # 如果存在详情实拍文件夹，先将其文件移动到详情根目录
            if detail_real_folder:
                logger.debug(f"找到详情实拍文件夹: {detail_real_folder.name}")

                # 显示详情实拍文件夹中的所有内容
                all_items = list(detail_real_folder.iterdir())
                logger.debug(f"详情实拍文件夹内容: {len(all_items)} 个项目")
                for item in all_items:
                    if item.is_file():
                        logger.debug(f"  文件: {item.name}")
                    elif item.is_dir():
                        logger.debug(f"  文件夹: {item.name}")

                detail_real_files = self._collect_image_files_from_folder(detail_real_folder, exclude_subfolders=False)
                logger.debug(f"详情实拍图片文件: {len(detail_real_files)} 个")

                if detail_real_files:
                    # 将详情实拍文件移动到详情根目录
                    moved_files = self._move_detail_real_files_to_root(detail_real_files, detail_folder)
                    logger.debug(f"已将 {len(moved_files)} 个详情实拍文件移动到详情根目录")

                    # 强制删除详情实拍文件夹（包括可能的db文件等）
                    self._remove_detail_real_folder(detail_real_folder)
                else:
                    logger.debug("详情实拍文件夹中没有图片文件")
                    # 即使没有图片文件，也删除详情实拍文件夹（可能有其他文件）
                    self._remove_detail_real_folder(detail_real_folder)
            else:
                logger.debug("未找到详情实拍文件夹，跳过合并步骤")

            if not all_image_files:
                logger.debug("未找到任何图片文件")
                return False

            # 重新收集详情根目录的所有文件（包括刚移动的文件）
            logger.debug(f"=== 重新收集详情根目录文件 ===")
            final_files = self._collect_image_files_from_folder(detail_folder, exclude_subfolders=True)
            logger.debug(f"详情根目录最终文件数: {len(final_files)} 个")

            # 按自然语言顺序排序
            sorted_files = self._natural_sort_files(final_files)

            # 打印排序结果
            logger.debug(f"=== 排序结果 (共{len(sorted_files)}个文件) ===")
            for i, file_path in enumerate(sorted_files, 1):
                logger.debug(f"{i:02d}. {file_path.name}")

            # 重命名文件
            success = self._rename_detail_files(sorted_files, detail_folder)

            if success:
                logger.debug(f"详情图片排序重命名完成: {len(sorted_files)} 个文件")
                return True
            else:
                logger.debug("详情图片重命名失败")
                return False

        except Exception as e:
            logger.debug(f"详情图片排序重命名异常: {e}")
            return False

    def _find_detail_folder_for_sorting(self, root_folder: Path) -> Optional[Path]:
        """查找详情文件夹（用于排序）"""
        try:
            # 获取关键词配置
            keywords = self._get_organize_keywords()
            detail_keywords = keywords.get('detail_image', ['详情'])

            # 收集所有候选详情文件夹
            detail_candidates = []

            for item in root_folder.iterdir():
                if item.is_dir():
                    folder_name_lower = item.name.lower()
                    for keyword in detail_keywords:
                        if keyword.lower() in folder_name_lower:
                            detail_candidates.append(item)
                            break

            if not detail_candidates:
                return None

            # 选择最佳的详情文件夹
            best_detail = self._select_best_folder_match(detail_candidates, detail_keywords)
            return best_detail

        except Exception as e:
            logger.debug(f"查找详情文件夹失败: {e}")
            return None

    def _find_detail_real_folder(self, root_folder: Path) -> Optional[Path]:
        """查找详情实拍文件夹（在根目录中查找）"""
        try:
            logger.debug(f"开始查找详情实拍文件夹，根目录: {root_folder.name}")

            # 查找包含"详情"和"实拍"关键词的文件夹
            detail_real_candidates = []

            for item in root_folder.iterdir():
                if item.is_dir():
                    folder_name_lower = item.name.lower()

                    # 检查是否同时包含"详情"和"实拍"
                    if "详情" in folder_name_lower and "实拍" in folder_name_lower:
                        detail_real_candidates.append(item)
                        logger.debug(f"  -> 详情实拍候选: {item.name}")

            if detail_real_candidates:
                # 如果有多个候选，选择名称最短的（通常是最基础的）
                best_candidate = min(detail_real_candidates, key=lambda x: len(x.name))
                logger.debug(f"选择详情实拍文件夹: {best_candidate.name}")
                return best_candidate
            else:
                logger.debug("未找到详情实拍文件夹")
                return None

        except Exception as e:
            logger.debug(f"查找详情实拍文件夹失败: {e}")
            return None

    def _collect_image_files_from_folder(self, folder_path: Path, exclude_subfolders: bool = False) -> List[Path]:
        """
        从文件夹收集图片文件

        Args:
            folder_path: 文件夹路径
            exclude_subfolders: 是否排除子文件夹

        Returns:
            List[Path]: 图片文件列表
        """
        try:
            # 支持的图片格式
            image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff', '.tif'}

            image_files = []

            if exclude_subfolders:
                # 只收集当前文件夹的文件，不包括子文件夹
                for item in folder_path.iterdir():
                    if item.is_file() and item.suffix.lower() in image_extensions:
                        image_files.append(item)
            else:
                # 递归收集所有图片文件
                for item in folder_path.rglob('*'):
                    if item.is_file() and item.suffix.lower() in image_extensions:
                        image_files.append(item)

            return image_files

        except Exception as e:
            logger.debug(f"收集图片文件失败: {e}")
            return []

    def _remove_detail_real_folder(self, detail_real_folder: Path):
        """删除详情实拍文件夹（强制删除，包括非空文件夹）"""
        try:
            import shutil

            # 强制删除整个文件夹（包括所有内容）
            shutil.rmtree(detail_real_folder)
            logger.debug(f"强制删除详情实拍文件夹: {detail_real_folder.name}")

        except Exception as e:
            logger.debug(f"删除详情实拍文件夹失败: {e}")

    def _move_detail_real_files_to_root(self, detail_real_files: List[Path], detail_folder: Path) -> List[Path]:
        """
        将详情实拍文件移动到详情根目录

        Args:
            detail_real_files: 详情实拍文件列表
            detail_folder: 详情文件夹路径

        Returns:
            List[Path]: 移动后的文件路径列表
        """
        try:
            moved_files = []

            for file_path in detail_real_files:
                try:
                    # 生成目标路径
                    target_path = detail_folder / file_path.name

                    # 如果目标文件已存在，添加后缀
                    if target_path.exists():
                        counter = 1
                        stem = target_path.stem
                        suffix = target_path.suffix
                        while target_path.exists():
                            target_path = detail_folder / f"{stem}_{counter}{suffix}"
                            counter += 1

                    # 移动文件
                    file_path.rename(target_path)
                    moved_files.append(target_path)
                    logger.debug(f"  移动详情实拍文件: {file_path.name} -> {target_path.name}")

                except Exception as e:
                    logger.debug(f"  移动详情实拍文件失败 {file_path.name}: {e}")
                    continue

            return moved_files

        except Exception as e:
            logger.debug(f"移动详情实拍文件到根目录失败: {e}")
            return []

    def _find_spec_folder(self, root_folder: Path) -> Optional[Path]:
        """查找规格文件夹"""
        try:
            # 获取关键词配置
            keywords = self._get_organize_keywords()
            spec_keywords = keywords.get('spec_image', ['规格'])

            # 收集所有候选规格文件夹
            spec_candidates = []

            for item in root_folder.iterdir():
                if item.is_dir():
                    folder_name_lower = item.name.lower()
                    for keyword in spec_keywords:
                        if keyword.lower() in folder_name_lower:
                            spec_candidates.append(item)
                            break

            if not spec_candidates:
                return None

            # 选择最佳的规格文件夹
            best_spec = self._select_best_folder_match(spec_candidates, spec_keywords)
            return best_spec

        except Exception as e:
            logger.debug(f"查找规格文件夹失败: {e}")
            return None

    def _remove_non_image_files_from_folder(self, folder_path: Path, data_folder: Path) -> int:
        """
        从文件夹中移除所有非图片文件

        Args:
            folder_path: 要清理的文件夹路径
            data_folder: 资料文件夹路径

        Returns:
            int: 移除的文件数量
        """
        try:
            logger.debug(f"开始清理文件夹中的非图片文件: {folder_path.name}")

            # 支持的图片格式
            image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff', '.tif'}

            removed_count = 0

            # 遍历文件夹中的所有项目
            for item in folder_path.iterdir():
                if item.is_file():
                    file_extension = item.suffix.lower()

                    if file_extension not in image_extensions:
                        # 非图片文件，移动到资料文件夹
                        success = self._move_item_to_data_folder(
                            item, data_folder, f"非图片文件: {file_extension}"
                        )
                        if success:
                            removed_count += 1

                elif item.is_dir():
                    # 递归处理子文件夹
                    sub_removed = self._remove_non_image_files_from_folder(item, data_folder)
                    removed_count += sub_removed

            return removed_count

        except Exception as e:
            logger.debug(f"清理文件夹非图片文件失败: {e}")
            return 0

    def _find_main_folder_for_sorting(self, root_folder: Path) -> Optional[Path]:
        """查找主图文件夹（用于排序）"""
        try:
            # 获取关键词配置
            keywords = self._get_organize_keywords()
            main_keywords = keywords.get('main_image', ['主图'])

            # 收集所有候选主图文件夹
            main_candidates = []

            for item in root_folder.iterdir():
                if item.is_dir():
                    folder_name_lower = item.name.lower()
                    for keyword in main_keywords:
                        if keyword.lower() in folder_name_lower:
                            main_candidates.append(item)
                            break

            if not main_candidates:
                return None

            # 选择最佳的主图文件夹
            best_main = self._select_best_folder_match(main_candidates, main_keywords)
            return best_main

        except Exception as e:
            logger.debug(f"查找主图文件夹失败: {e}")
            return None

    def _rename_main_files(self, sorted_files: List[Path], main_folder: Path) -> bool:
        """
        重命名主图文件为 main_01, main_02, ... 格式

        Args:
            sorted_files: 排序后的文件列表
            main_folder: 主图文件夹路径

        Returns:
            bool: 重命名成功返回True
        """
        try:
            logger.debug(f"=== 开始重命名主图文件 ===")

            # 创建临时文件名映射，避免重命名冲突
            temp_mappings = []
            final_mappings = []

            for i, file_path in enumerate(sorted_files, 1):
                # 生成新的文件名
                file_extension = file_path.suffix
                new_filename = f"main_{i:02d}{file_extension}"
                new_path = main_folder / new_filename

                # 生成临时文件名（避免冲突）
                temp_filename = f"temp_main_{i:02d}_{file_path.name}"
                temp_path = main_folder / temp_filename

                temp_mappings.append((file_path, temp_path))
                final_mappings.append((temp_path, new_path, new_filename))

            # 第一步：重命名为临时文件名
            for original_path, temp_path in temp_mappings:
                try:
                    original_path.rename(temp_path)
                except Exception as e:
                    logger.debug(f"  临时重命名失败: {original_path.name} - {e}")
                    return False

            # 第二步：重命名为最终文件名
            for temp_path, final_path, final_filename in final_mappings:
                try:
                    temp_path.rename(final_path)
                except Exception as e:
                    logger.debug(f"  最终重命名失败: {temp_path.name} - {e}")
                    return False

            logger.debug(f"主图文件重命名完成，共处理 {len(sorted_files)} 个文件")
            return True

        except Exception as e:
            logger.debug(f"重命名主图文件失败: {e}")
            return False

    def _natural_sort_files(self, file_list: List[Path]) -> List[Path]:
        """
        按自然语言顺序排序文件

        自然排序：1, 2, 3, ..., 9, 10, 11, 12
        而不是：1, 10, 11, 12, ..., 2, 20, 21

        Args:
            file_list: 文件路径列表

        Returns:
            List[Path]: 排序后的文件列表
        """
        import re

        def natural_sort_key(file_path: Path) -> List:
            """生成自然排序的键"""
            # 获取文件名（不包括扩展名）
            filename = file_path.stem

            # 将文件名分解为数字和非数字部分
            parts = re.split(r'(\d+)', filename.lower())

            # 将数字部分转换为整数，非数字部分保持字符串
            key = []
            for part in parts:
                if part.isdigit():
                    key.append(int(part))
                else:
                    key.append(part)

            return key

        try:
            # 按自然排序键排序
            sorted_files = sorted(file_list, key=natural_sort_key)

            for i, file_path in enumerate(file_list, 1):
                logger.debug(f"  {i}. {file_path.name}")

            for i, file_path in enumerate(sorted_files, 1):
                logger.debug(f"  {i}. {file_path.name}")

            return sorted_files

        except Exception as e:
            logger.debug(f"自然排序失败: {e}")
            return file_list

    def _rename_detail_files(self, sorted_files: List[Path], detail_folder: Path) -> bool:
        """
        重命名详情文件为 detail_01, detail_02, ... 格式

        Args:
            sorted_files: 排序后的文件列表
            detail_folder: 详情文件夹路径

        Returns:
            bool: 重命名成功返回True
        """
        try:
            logger.debug(f"=== 开始重命名详情文件 ===")

            # 创建临时文件名映射，避免重命名冲突
            temp_mappings = []
            final_mappings = []

            for i, file_path in enumerate(sorted_files, 1):
                # 生成新的文件名
                file_extension = file_path.suffix
                new_filename = f"detail_{i:02d}{file_extension}"
                new_path = detail_folder / new_filename

                # 生成临时文件名（避免冲突）
                temp_filename = f"temp_detail_{i:02d}_{file_path.name}"
                temp_path = detail_folder / temp_filename

                temp_mappings.append((file_path, temp_path))
                final_mappings.append((temp_path, new_path, new_filename))

            # 第一步：重命名为临时文件名
            for original_path, temp_path in temp_mappings:
                try:
                    original_path.rename(temp_path)
                except Exception as e:
                    logger.debug(f"  临时重命名失败: {original_path.name} - {e}")
                    return False

            # 第二步：重命名为最终文件名
            for temp_path, final_path, _ in final_mappings:
                try:
                    temp_path.rename(final_path)
                except Exception as e:
                    logger.debug(f"  最终重命名失败: {temp_path.name} - {e}")
                    return False

            logger.debug(f"详情文件重命名完成，共处理 {len(sorted_files)} 个文件")
            return True

        except Exception as e:
            logger.debug(f"重命名详情文件失败: {e}")
            return False


if __name__ == '__main__':
    # 测试图片包处理器
    processor = ImagePackageProcessor()
    result = processor.extract_package(r'C:\Users\<USER>\Pictures\codesdsf.rar')
    print(result)