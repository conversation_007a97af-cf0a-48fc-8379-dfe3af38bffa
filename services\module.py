"""工具函数"""
import requests
from pathlib import Path
from typing import Optional, Union, Tuple, List
import re


def read_txt_to_list(file_path, strip=True, skip_empty=True, remove_duplicates=False):
    """
    读取txt文件内容并转换为列表
    
    Args:
        file_path (str | Path): 文件路径，支持字符串或Path对象
        strip (bool): 是否去除每行首尾的空白字符，默认为True
        skip_empty (bool): 是否跳过空行和只包含空白字符的行，默认为True
        remove_duplicates (bool): 是否去除重复行，默认为False
        
    Returns:
        list: 文件内容列表。如果文件不存在返回空列表
        
    Raises:
        Exception: 当读取文件失败时抛出异常
    """
    try:
        path = Path(file_path)
        if not path.exists():
            print(f"文件不存在: {file_path}")
            return []
            
        with path.open('r', encoding='utf-8') as f:
            lines = f.readlines()
        if strip:
            # 确保去除每行首尾的所有空白字符，包括空格、换行符等
            lines = [line.strip('\r\n\t ') for line in lines]
        if skip_empty:
            # 跳过空行和只包含空白字符的行
            lines = [line for line in lines if line and not line.isspace()]
        if remove_duplicates:
            # 去除重复行，保持原有顺序
            seen = set()
            lines = [line for line in lines if not (line in seen or seen.add(line))]
        return lines
    except Exception as e:
        raise Exception(f"读取文件 {file_path} 失败: {str(e)}")
    

def doubao_chat_with_bot(message: str, model_id: str, api_key: str):
    """
    豆包智能体调用

    :param message: 消息内容
    :param model_id: 智能体模型id
    :param api_key: 豆包API密钥
    :return: 返回消息内容
    """
    url = 'https://ark.cn-beijing.volces.com/api/v3/bots/chat/completions'
  
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json'
    }
  
    data = {
        "model": model_id,
        "stream": False,
        "stream_options": {"include_usage": True},
        "messages": [
            {
                "role": "user",
                "content": message
            }
        ]
    }
  
    try:
        response = requests.post(url, headers=headers, json=data)
        response.raise_for_status()  # 检查响应状态
        response_data = response.json()
        content = response_data['choices'][0]['message']['content']
    
        return content
    
    except requests.exceptions.RequestException as e:
        print(f"请求错误: {e}")
        return None
    except (KeyError, IndexError) as e:
        print(f"解析响应错误: {e}")
        return None



def deepseek_chat(user_content: str, model: str = "deepseek-chat", api_key: str = None,
                  system_content: str = "You are a helpful assistant"):
    """
    调用DeepSeek AI API进行对话
    https://api-docs.deepseek.com/zh-cn/

    Args:
        user_content (str): 用户输入内容
        model (str): 模型名称，可选 "deepseek-chat" 或 "deepseek-reasoner"，默认为 "deepseek-chat"
        api_key (str): DeepSeek API密钥
        system_content (str): 系统提示词，默认为 "You are a helpful assistant"

    Returns:
        对于 deepseek-chat: 返回 str (AI回复内容) 或 None (失败时)
        对于 deepseek-reasoner: 返回 tuple (content, reasoning_content) 或 None (失败时)
    """
    
    url = "https://api.deepseek.com/chat/completions"

    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }

    # 构建消息列表
    messages = [
        {"role": "system", "content": system_content},
        {"role": "user", "content": user_content}
    ]

    data = {
        "model": model,
        "messages": messages,
        "stream": False
    }

    try:
        response = requests.post(url, headers=headers, json=data, timeout=30)
        response.raise_for_status()  # 检查HTTP状态码

        response_data = response.json()

        # 提取AI回复内容
        if "choices" in response_data and len(response_data["choices"]) > 0:
            choice = response_data["choices"][0]
            message = choice["message"]

            if model == "deepseek-reasoner":
                # 推理模型返回推理过程和最终答案
                reasoning_content = message.get("reasoning_content", "")
                content = message.get("content", "")
                return content, reasoning_content
            else:
                # 普通对话模型只返回内容
                content = message.get("content", "")
                return content
        else:
            raise Exception("DeepSeek API响应格式异常，未找到choices字段")

    except Exception as e:
        raise Exception(f"DeepSeek API调用异常: {e}")

    
def get_files_natural_sorted(folder_path: Union[str, Path], filter_unsupported: bool = True) -> List[Path]:
    """
    获取文件夹中的文件列表，按自然排序

    Args:
        folder_path: 文件夹路径
        filter_unsupported: 是否过滤并删除不支持的文件格式

    Returns:
        List[Path]: 按自然排序的文件路径列表
    """
    def natural_sort_key(text: str):
        """生成自然排序的键值"""
        return [int(c) if c.isdigit() else c.lower() for c in re.split(r'(\d+)', text)]

    folder_path = Path(folder_path)

    if not folder_path.exists():
        raise FileNotFoundError(f"文件夹不存在: {folder_path}")

    if not folder_path.is_dir():
        raise NotADirectoryError(f"路径不是文件夹: {folder_path}")

    # 支持的图片格式
    supported_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'}

    # 不支持的文件（需要删除）
    unsupported_files = {'thumbs.db', 'desktop.ini', '.ds_store'}

    # 获取所有文件
    all_files = [item for item in folder_path.iterdir() if item.is_file()]

    valid_files = []

    for file_path in all_files:
        file_name_lower = file_path.name.lower()
        file_ext_lower = file_path.suffix.lower()

        # 检查是否是需要删除的不支持文件
        if filter_unsupported and (file_name_lower in unsupported_files or
                                 (file_ext_lower and file_ext_lower not in supported_extensions)):
            try:
                file_path.unlink()  # 删除文件
                print(f"已删除不支持的文件: {file_path.name}")
            except Exception as e:
                print(f"删除文件失败 {file_path.name}: {e}")
            continue

        # 只保留支持的图片文件
        if not filter_unsupported or file_ext_lower in supported_extensions:
            valid_files.append(file_path)

    # 按文件名自然排序
    valid_files.sort(key=lambda x: natural_sort_key(x.name))

    return valid_files


def check_file_exists_in_folder(folder_path: Union[str, Path], filename: Union[str, List[str]],
                                extensions: Optional[Union[str, List[str]]] = None) -> Union[bool, dict]:
    """
    检查文件夹中是否存在指定文件名的文件（严格精确匹配）

    Args:
        folder_path: 文件夹路径
        filename: 文件名，支持单个文件名(str)或多个文件名(list)
        extensions: 可选的文件扩展名，不指定则匹配任何扩展名
                   支持单个扩展名(str)如'.jpg'或多个扩展名(list)如['.jpg', '.png']

    Returns:
        - 如果filename是str: 返回bool，True表示文件存在
        - 如果filename是list: 返回dict，格式为 {"文件名": True/False, ...}
    """
    try:
        folder_path = Path(folder_path)

        if not folder_path.exists() or not folder_path.is_dir():
            # 文件夹不存在，返回对应格式的False结果
            if isinstance(filename, str):
                return False
            else:
                return {name: False for name in filename}

        # 获取文件夹中所有文件
        all_files = [f for f in folder_path.iterdir() if f.is_file()]

        # 处理扩展名参数 - 严格匹配，不转换大小写
        if extensions is None:
            target_extensions = None
        elif isinstance(extensions, str):
            target_extensions = [extensions]
        else:
            target_extensions = list(extensions)

        def file_exists_strict(target_name: str) -> bool:
            """严格检查单个文件是否存在 - 精确匹配，区分大小写"""

            for file_path in all_files:
                file_stem = file_path.stem  # 不带扩展名的文件名，保持原始大小写
                file_ext = file_path.suffix  # 扩展名，保持原始大小写

                # 严格精确匹配文件名（区分大小写）
                if file_stem == target_name:
                    # 如果没有指定扩展名，直接返回True
                    if target_extensions is None:
                        return True
                    # 如果指定了扩展名，检查扩展名是否匹配（不区分大小写）
                    elif file_ext.lower() in [ext.lower() for ext in target_extensions]:
                        return True

            return False

        # 处理单个文件名
        if isinstance(filename, str):
            return file_exists_strict(filename)

        # 处理多个文件名
        else:
            result = {}
            for name in filename:
                result[name] = file_exists_strict(name)
            return result

    except Exception:
        # 发生异常时返回对应格式的False结果
        if isinstance(filename, str):
            return False
        else:
            return {name: False for name in filename}


def validate_spec_images_completeness(products: list, image_package_folder: Union[str, Path]) -> bool:
    """
    验证所有产品的规格图片完整性

    Args:
        products: 产品列表，每个产品包含product_code和spec_value字段
        image_package_folder: 图片包根文件夹路径

    Returns:
        bool: 所有规格图片都存在返回True，否则返回False

    Raises:
        Exception: 当发现缺失的规格图片时抛出异常
    """
    try:
        image_package_folder = Path(image_package_folder)

        print("开始检查规格图片完整性...")
        missing_spec_images = []
        total_products = len(products)

        for i, product in enumerate(products, 1):
            product_code = product.get('product_code', '')
            spec_value = product.get('spec_value', '')

            if not product_code or not spec_value:
                continue

            # 构建规格文件夹路径
            spec_folder_path = image_package_folder / product_code / "规格"

            # 检查规格图片是否存在
            exists = check_file_exists_in_folder(spec_folder_path, spec_value, [".jpg", ".png", ".gif", ".jpeg"])

            if not exists:
                missing_spec_images.append({
                    'product_code': product_code,
                    'spec_value': spec_value,
                    'spec_folder': str(spec_folder_path)
                })
                print(f"❌ 缺失规格图片: {product_code} - {spec_value}")

            # 显示进度
            if i % 50 == 0 or i == total_products:
                print(f"规格图片检查进度: {i}/{total_products} ({i/total_products*100:.1f}%)")

        # 检查结果汇总
        if missing_spec_images:
            print(f"\n❌ 规格图片检查失败！发现 {len(missing_spec_images)} 个缺失的规格图片:")
            print("缺失详情:")
            for missing in missing_spec_images:
                print(f"  - 产品: {missing['product_code']}, 规格: {missing['spec_value']}")
                print(f"    路径: {missing['spec_folder']}")

            print(f"\n请检查并补充缺失的规格图片后重新运行！")
            raise Exception(f"规格图片检查失败，缺失 {len(missing_spec_images)} 个规格图片")
        else:
            print(f"✅ 规格图片检查通过！所有 {total_products} 个商品的规格图片都存在")
            return True

    except Exception as e:
        # 如果是我们主动抛出的异常，直接重新抛出
        if "规格图片检查失败" in str(e):
            raise
        # 其他异常包装后抛出
        raise Exception(f"规格图片检查过程中发生错误: {str(e)}")


def check_spec_images_exist(product_code: str, spec_values: List[str], image_package_folder: Union[str, Path]) -> dict:
    """
    检查产品规格文件夹中是否存在对应规格值的图片

    Args:
        product_code: 产品编码
        spec_values: 规格值列表，如 ["红色", "蓝色", "大号"]
        image_package_folder: 图片包根文件夹路径

    Returns:
        dict: 检查结果，格式为:
        {
            "product_code": "产品编码",
            "spec_folder_exists": True/False,  # 规格文件夹是否存在
            "spec_images": {
                "红色": True/False,  # 该规格值对应的图片是否存在
                "蓝色": True/False,
                ...
            },
            "missing_specs": ["缺失的规格值1", "缺失的规格值2"],  # 缺失图片的规格值
            "found_specs": ["存在的规格值1", "存在的规格值2"],    # 存在图片的规格值
            "total_specs": 总规格数量,
            "found_count": 找到图片的规格数量,
            "missing_count": 缺失图片的规格数量
        }
    """
    try:
        image_package_folder = Path(image_package_folder)

        # 构建产品规格文件夹路径
        product_folder = image_package_folder / product_code
        spec_folder = product_folder / "规格"

        result = {
            "product_code": product_code,
            "spec_folder_exists": spec_folder.exists() and spec_folder.is_dir(),
            "spec_images": {},
            "missing_specs": [],
            "found_specs": [],
            "total_specs": len(spec_values),
            "found_count": 0,
            "missing_count": 0
        }

        # 如果规格文件夹不存在，所有规格都标记为缺失
        if not result["spec_folder_exists"]:
            for spec_value in spec_values:
                result["spec_images"][spec_value] = False
                result["missing_specs"].append(spec_value)
            result["missing_count"] = len(spec_values)
            return result

        # 获取规格文件夹中的所有图片文件
        try:
            spec_image_files = get_files_natural_sorted(spec_folder, filter_unsupported=True)
            spec_image_names = [img.stem.lower() for img in spec_image_files]  # 获取不带扩展名的文件名
        except Exception as e:
            # 如果获取文件列表失败，标记所有规格为缺失
            for spec_value in spec_values:
                result["spec_images"][spec_value] = False
                result["missing_specs"].append(spec_value)
            result["missing_count"] = len(spec_values)
            return result

        # 检查每个规格值是否有对应的图片
        for spec_value in spec_values:
            spec_value_clean = spec_value.strip().lower()

            # 检查是否有文件名包含该规格值
            found = any(spec_value_clean in img_name for img_name in spec_image_names)

            result["spec_images"][spec_value] = found

            if found:
                result["found_specs"].append(spec_value)
                result["found_count"] += 1
            else:
                result["missing_specs"].append(spec_value)
                result["missing_count"] += 1

        return result

    except Exception as e:
        # 发生异常时返回错误结果
        return {
            "product_code": product_code,
            "spec_folder_exists": False,
            "spec_images": {spec: False for spec in spec_values},
            "missing_specs": spec_values.copy(),
            "found_specs": [],
            "total_specs": len(spec_values),
            "found_count": 0,
            "missing_count": len(spec_values),
            "error": str(e)
        }