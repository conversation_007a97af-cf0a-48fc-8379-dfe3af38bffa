#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
七牛云存储管理器
功能：上传文件到七牛云存储
"""

import time
from pathlib import Path
from typing import Union, Dict
from qiniu import Auth, put_file

# ==================== 业务服务类 ====================
class QiniuManager:
    """七牛云存储管理器 - 标准4分组结构"""

    DEFAULT_EXPIRES = 3600
    TOKEN_BUFFER_TIME = 300  # 5分钟缓冲时间
    DEFAULT_VERSION = 'v2'

    def __init__(self, qiniu_config: Dict):
        """初始化七牛云存储管理器（依赖注入）

        Args:
            qiniu_config: 七牛云配置字典
            - access_key: 七牛云AccessKey
            - secret_key: 七牛云SecretKey
            - bucket_name: 七牛云存储空间名称
            - version: 七牛云API版本，默认v2
            - domain: 七牛云存储空间的域名，默认为空
        """
        self.access_key = qiniu_config['access_key']
        self.secret_key = qiniu_config['secret_key']
        self.bucket_name = qiniu_config['bucket_name']
        self.version = qiniu_config.get('version', self.DEFAULT_VERSION)
        self.domain = qiniu_config.get('domain')

        self.q = Auth(self.access_key, self.secret_key)
        self._token_cache = {}


    def upload_file(self, local_file_path: Union[str, Path], remote_key: str) -> Dict:
        """上传文件到七牛云

        Args:
            local_file_path: 本地文件路径
            remote_key: 远程文件名 含路径 如 products/123456/main/1.jpg

        Returns:
            dict: {'key': str, 'hash': str, 'url': str}
        """
        token = self._get_cached_upload_token(remote_key)
        ret, info = put_file(
            up_token=token,
            key=remote_key,
            file_path=str(local_file_path),
            version=self.version
        )

        if info.status_code != 200:
            raise Exception(f"上传失败，状态码: {info.status_code}")

        return {
            'key': ret['key'],
            'hash': ret['hash'],
            'url': self._get_file_url(ret['key'])
        }


    def _get_upload_token(self, key: str, expires: int = None) -> str:
        """获取上传凭证"""
        expires = expires or self.DEFAULT_EXPIRES
        return self.q.upload_token(
            bucket=self.bucket_name,
            key=key,
            expires=expires
        )

    def _get_file_url(self, key: str) -> str:
        """获取文件的访问URL"""
        return f"https://{self.domain}/{key}" if self.domain else key

    def _get_cached_upload_token(self, key: str) -> str:
        """获取缓存的上传凭证，如果过期则重新生成"""
        current_time = time.time()

        try:
            cached_data = self._token_cache[key]
            if cached_data['expires_at'] - current_time > self.TOKEN_BUFFER_TIME:
                return cached_data['token']
        except KeyError:
            pass

        token = self._get_upload_token(key, self.DEFAULT_EXPIRES)
        self._token_cache[key] = {
            'token': token,
            'expires_at': current_time + self.DEFAULT_EXPIRES
        }

        return token
