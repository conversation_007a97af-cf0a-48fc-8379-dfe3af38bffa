"""
图鉴管理器 - 用于处理验证码
"""

import requests


class TjCaptcha:
    """图鉴验证码识别类"""

    def __init__(self, username: str, password: str):
        self.base_params = {'username': username, 'password': password}
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) "
                          "AppleWebKit/537.36 (KHTML, like Gecko) Chrome/103.0.0.0 Safari/537.36",
        }

    def check(self, base64_img: str, type_id: int = 1) -> dict:
        """验证码识别"""
        params = {'typeid': type_id, 'image': base64_img}
        params.update(self.base_params)
        return requests.post("http://api.ttshitu.com/predict", json=params, headers=self.headers).json()
