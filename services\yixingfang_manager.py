#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
伊性坊网页自动化管理器
"""

import sys
import os
import re
import time
import requests
import threading
from pathlib import Path
from typing import Dict, Optional
from datetime import datetime, timedelta
from DrissionPage import Chromium, ChromiumOptions
# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))
from core import logger, G
from services.tujian_manager import TjCaptcha


class GlobalAuthManager:
    """全局Authorization管理器"""

    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
                    cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        if not self._initialized:
            self._authorization = None
            self._expire_time = None
            self._last_update = None
            self._update_lock = threading.Lock()
            self._initialized = True
            logger.debug("全局Authorization管理器已初始化")

    def set_authorization(self, authorization: str, expire_minutes: int = 30):
        """设置Authorization"""
        with self._update_lock:
            self._authorization = authorization
            self._expire_time = datetime.now() + timedelta(minutes=expire_minutes)
            self._last_update = datetime.now()
            logger.debug(f"全局Authorization已更新: {authorization[:20]}... (有效期至: {self._expire_time.strftime('%H:%M:%S')})")

    def get_authorization(self) -> Optional[str]:
        """获取Authorization"""
        with self._update_lock:
            if self._authorization and self._expire_time and datetime.now() < self._expire_time:
                return self._authorization
            elif self._authorization and self._expire_time:
                logger.debug(f"Authorization已过期 (过期时间: {self._expire_time.strftime('%H:%M:%S')})")
            return None

    def is_expired(self) -> bool:
        """检查是否过期"""
        if not self._expire_time:
            return True
        return datetime.now() >= self._expire_time

    def clear(self):
        """清除Authorization"""
        with self._update_lock:
            self._authorization = None
            self._expire_time = None
            self._last_update = None
            logger.debug("全局Authorization已清除")

    def get_status(self) -> dict:
        """获取状态信息"""
        with self._update_lock:
            return {
                "has_auth": bool(self._authorization),
                "is_expired": self.is_expired(),
                "expire_time": self._expire_time.strftime('%H:%M:%S') if self._expire_time else None,
                "last_update": self._last_update.strftime('%H:%M:%S') if self._last_update else None
            }


def is_authorization_error(response_json: dict) -> bool:
    """
    判断是否为Authorization错误
    基于实际测试结果的精确判断逻辑
    """
    if not response_json:
        return False

    code = response_json.get('code')
    message = response_json.get('msg', response_json.get('message', '')).lower()

    # 明确的Authorization错误标识（基于测试结果）
    if code == 401:
        return True

    # 检查错误消息关键词（基于实际测试的错误消息）
    auth_error_keywords = [
        '身份验证失败',
        '用户不存在',
        '请从伊性坊页面跳转进入',
        '请登录后操作',
        'unauthorized',
        'authentication failed'
    ]

    if any(keyword in message for keyword in auth_error_keywords):
        return True

    return False


# 全局Authorization管理器实例
global_auth_manager = GlobalAuthManager()


class YixingfangBase:
    """伊性坊基础辅助类"""

    def __init__(self, headless: bool = False):
        """
        初始化基础类

        Args:
            prot: 本地端口
            headless: 是否无头模式
        """
        self.username = G.yixingfang.username
        self.password = G.yixingfang.password
        self.url = "https://www.yxfshop.com/"
        self.mode = "d"  # 默认浏览器模式

        # 初始化浏览器
        co = ChromiumOptions()
        co.headless(headless)
        self.browser = Chromium(co)

        try:
            self.tab = self.browser.get_tab(url="yxfshop.com")
        except Exception as e:
            self.tab = self.browser.new_tab(self.url)

        # 初始化图鉴验证码（使用图鉴账号）
        self.tj_captcha = TjCaptcha(G.tujian.username, G.tujian.password)

        # 元素定位器 - 实例属性
        self.username_input = "t:input@@id=in_login"
        self.password_input = "t:input@@id=in_passwd"
        self.captcha_input = "t:input@@id=iptlogin"
        self.captcha_img = "#LoginimgVerifyCode"
        self.captcha_refresh = "t:a@@text()= 看不清楚?换个图片"
        self.submit_btn = "t:input@@value=登录"
        self.login_link = "t:a@@text()=[请登录]"
        self.login_title = "t:h4@@text()=已注册用户，请登录"
        self.login_error = "t:div@@class=error"
        self.login_status = "@@id=loginBar_widgets_1970"

        self.login()

    def login(self) -> bool:
        """
        检测登录状态，未登录则登录

        Returns:
            bool: 登录成功返回True
        """
        # 刷新页面
        self.tab.refresh()
        time.sleep(2)

        # 检查登录状态
        if self._is_logged_in():
            logger.debug("伊性坊已登录，跳过登录")
            return True

        logger.debug("伊性坊开始登录流程")
        # 未登录，开始登录流程
        try:
            login_ele = self.tab.ele(self.login_link)
            if login_ele:
                login_ele.click()
            else:
                raise Exception("未找到登录链接")
        except Exception as e:
            raise Exception(f"点击登录链接失败: {e}")

        time.sleep(2)

        # 检查是否在登录页面
        if not self.tab.wait.ele_displayed(self.login_title, timeout=10):
            raise Exception("不在登录页面")

        # 尝试登录3次
        for login_attempt in range(3):
            if self._attempt_login():
                logger.debug("伊性坊登录成功")
                # 登录成功后立即获取Authorization
                self._fetch_and_cache_authorization()
                return True

            if login_attempt < 2:
                logger.debug(f"伊性坊第{login_attempt + 1}次登录失败，重试")
                time.sleep(1)

        raise Exception("重复登录失败，程序退出")

    def switch_mode(self, mode: str) -> bool:
        """
        自动判断切换目标模式
        如果当前模式与目标模式不同，则执行切换操作

        Args:
            mode: 目标模式，'d'表示浏览器模式，'s'表示requests模式

        Returns:
            bool: 切换成功返回True，失败或无需切换返回True
        """
        # 检查目标模式是否有效
        if mode not in ['d', 's']:
            raise ValueError(f"无效的模式参数: {mode}，只支持 'd' 或 's'")

        # 获取当前模式（如果没有设置过，默认为'd'）
        current_mode = getattr(self, 'mode', 'd')

        # 如果当前模式已经是目标模式，无需切换
        if current_mode == mode:
            return True

        # 执行模式切换
        self.tab.change_mode()

        # 更新当前模式
        self.mode = mode
        return True

    def close_browser(self):
        """关闭浏览器"""
        try:
            if hasattr(self, 'browser') and self.browser:
                self.browser.quit()
                self.browser = None
        except Exception as e:
            logger.debug(f"关闭浏览器异常: {e}")

    def _fetch_and_cache_authorization(self):
        """获取并缓存Authorization到全局管理器"""
        try:
            # 使用与get_authorization相同的可靠方法
            # 构造请求参数
            params = {"bn": "4589809440140"}
            url = "https://www.yxfshop.com/refund/transit/skip.php"

            # 获取cookies
            cookies_dict = {}
            cookies_list = self.browser.cookies()
            for cookie in cookies_list:
                if cookie['domain'] in ['www.yxfshop.com', '.yxfshop.com']:
                    cookies_dict[cookie['name']] = cookie['value']

            # 构造headers
            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
            }

            # 发送请求
            response = requests.get(
                url,
                params=params,
                headers=headers,
                cookies=cookies_dict,
                allow_redirects=False
            )

            # 获取Location头
            location = response.headers.get('Location')
            if location and "s=" in location:
                authorization = location.split("s=")[-1]
                # 保存到全局管理器
                global_auth_manager.set_authorization(authorization, expire_minutes=30)
                logger.debug(f"Authorization已缓存: {authorization[:20]}...")
            else:
                logger.debug("获取Authorization失败")

        except Exception as e:
            logger.debug(f"获取Authorization异常: {e}")

    def _is_logged_in(self) -> bool:
        """检查是否已登录"""
        try:
            if self.tab.wait.eles_loaded(self.login_status, timeout=10):
                ele_html = self.tab.ele(self.login_status).html
                return "display" in ele_html
            return False
        except Exception:
            return False

    def _attempt_login(self) -> bool:
        """尝试一次登录"""
        try:
            # 输入用户名
            username_ele = self.tab.ele(self.username_input)
            if username_ele:
                username_ele.click()
                username_ele.input(self.username, clear=True)
            else:
                return False
            time.sleep(0.5)

            # 输入密码
            password_ele = self.tab.ele(self.password_input)
            if password_ele:
                password_ele.click()
                password_ele.input(self.password, clear=True)
            else:
                return False
            time.sleep(0.5)

            # 验证码处理，尝试3次
            for captcha_attempt in range(3):
                if self._handle_captcha():
                    break
                else:
                    if captcha_attempt < 2:
                        refresh_ele = self.tab.ele(self.captcha_refresh)
                        if refresh_ele:
                            refresh_ele.click()
                            time.sleep(1)

            # 等待登录结果
            time.sleep(3)

            # 判断是否登录成功
            if self.tab.wait.ele_displayed(self.login_error, timeout=5):
                return False

            return self._is_logged_in()

        except Exception:
            return False

    def _handle_captcha(self) -> bool:
        """处理验证码"""
        try:
            # 获取验证码图片
            captcha_img_ele = self.tab.ele(self.captcha_img)
            if not captcha_img_ele:
                logger.debug("未找到验证码图片元素")
                return False

            base64_img = captcha_img_ele.get_screenshot(as_base64=True)
            if not base64_img:
                logger.debug("验证码图片元素: ", code=captcha_img_ele.html)
                return False

            result = self.tj_captcha.check(base64_img, 1)
            logger.debug("图鉴识别结果: ", code=result)

            if result.get("success"):
                yzm = result["data"]["result"]
                logger.debug("识别的验证码: ", data=yzm)

                # 输入验证码
                captcha_input_ele = self.tab.ele(self.captcha_input)
                if captcha_input_ele:
                    captcha_input_ele.input(yzm, clear=True)
                else:
                    logger.debug("未找到验证码输入框")
                    return False

                # 点击提交按钮
                submit_ele = self.tab.ele(self.submit_btn)
                if submit_ele:
                    submit_ele.click()
                    return True
                else:
                    logger.debug("未找到提交按钮")
                    return False
            else:
                logger.debug("验证码识别失败: ", code=result)
                return False

        except Exception as e:
            logger.debug("验证码处理异常: ", code=e)
            return False



class YixingfangCollector:
    """伊性坊数据采集类"""

    # 特殊词过滤配置 - 类属性
    TITLE_FILTER_KEYWORDS = {"清仓", "负责人", "备案", "私人", "厂家"}
    SKU_FILTER_KEYWORDS = {"箱", "清仓"}
    SKU_FILTER_PRICES = {"987.00"}
    REMARK_FILTER_KEYWORDS = {"备注", "通知", "备案"}

    def __init__(self, base: YixingfangBase):
        self.base = base

        # 商品页面元素定位器
        self.goods_title = "t:h1@@class=goodsname"
        self.goods_props = "t:ul@@class=goodsprops clearfix"
        self.sku_table = "#listall_ll"
        self.goods_intro = "t:div@@id=goods-intro"
        self.product_listall = "t:div@@id=listall"

    def collect_product_link(self, product_code: str, mode: str = "s") -> dict:
        """
        根据商品编号采集商品链接

        Args:
            product_code: 商品编号
            mode: 模式，'d'表示浏览器模式，'s'表示requests模式

        Returns:
            dict: 商品编号与链接的字典
        """

        url = f"https://www.yxfshop.com/?gallery--n,{product_code}-wholesale.html"
        # 切换模式
        self.base.switch_mode(mode)
        self.base.tab.get(url)

        # 获取第一条url
        one_url = self.base.tab.ele("t:a@@class=nolnk entry-title", timeout=5).link
        logger.debug(f"{product_code}采集到商品链接", data=one_url)
        return one_url
        
    def collect_product_data(self, url: str, mode: str = "s") -> dict:
        """
        获取商品数据

        Args:
            url: 商品URL
            mode: 模式，'d'表示浏览器模式，'s'表示requests模式

        Returns:
            dict: 商品数据字典
        """
        # 切换模式
        self.base.switch_mode(mode)
        self.base.tab.get(url)

        try:
            # 获取商品基本信息
            basic_info = self._get_basic_info()
            logger.debug("商品基本信息: ", data=basic_info)

            # 获取SKU数据
            skus = self._get_sku_data(basic_info['weight'])
            logger.debug("商品SKU数据: ", data=skus)

            # 获取图片包链接
            image_package = self._get_image_package()
            logger.debug("商品图片包链接: ", data=image_package)

            # 获取备注信息
            remark = self._get_remark()
            logger.debug("商品备注信息: ", data=remark)

            # 组装商品数据（与数据库字段对应）
            product_data = {
                "product_code": basic_info['product_code'],  # 商品编号
                "brand": basic_info['brand'],                # 品牌
                "title": basic_info['title'],  # 商品标题
                "source": "伊性坊",                          # 来源
                "source_url": url,                          # 伊性坊商品链接
                "image_package_url": image_package,         # 图片包链接
                "description": remark,  # 商品描述（备注）
                "product_status": "正常",                  # 默认状态
                "skus": skus                        # SKU列表
            }

            logger.debug(f"{[url]} 商品数据采集完成", data=product_data)
            return product_data

        except Exception as e:
            safe_error = str(e)
            raise Exception(f"获取商品数据失败: {safe_error}")
        
    def download_image_package(self, image_package_url: str, save_folder: str = None, product_code: str = None) -> str:
        """
        下载图片包

        Args:
            image_package_url: 图片包链接
            save_folder: 保存目录（可选）
            product_code: 商品编号（用于重命名，可选）

        Returns:
            str: 下载成功时返回文件路径

        Raises:
            Exception: 下载失败时抛出异常
        """
        if not image_package_url or image_package_url == "没有图片包":
            raise Exception("没有图片包链接")

        logger.debug(f"开始下载{image_package_url}, 目标保存目录：{save_folder}, 目标重命名：{product_code}")

        # 获取Authorization（使用缓存）
        authorization = self.get_authorization()
        if not authorization:
            raise Exception("获取Authorization失败")

        # 获取图片包数据（带重试机制）
        package_data = None
        max_retries = 2

        for retry in range(max_retries):
            try:
                package_data = self._get_image_package_data(image_package_url, authorization)
                if package_data:
                    break
                else:
                    if retry < max_retries - 1:
                        logger.debug(f"第{retry+1}次获取图片包数据失败，重试...")
                        continue
                    else:
                        logger.debug("获取图片包数据失败：所有重试都失败了")
                        raise Exception("获取图片包数据失败：所有重试都失败了")
            except Exception as e:
                if "Authorization错误" in str(e) and retry < max_retries - 1:
                    logger.debu("Authorization错误，重新获取...")

                    # 重新获取Authorization
                    try:
                        new_authorization = self.get_authorization(force_refresh=True)

                        if new_authorization:
                            logger.debug(f"Authorization已更新: {new_authorization[:20]}...")
                            authorization = new_authorization  # 更新局部变量
                            continue
                        else:
                            logger.debug("重新获取Authorization失败")
                            raise Exception("重新获取Authorization失败")

                    except Exception as refresh_error:
                        logger.debug(f"重新获取Authorization失败: {refresh_error}")
                        raise Exception(f"重新获取Authorization失败: {refresh_error}")
                else:
                    raise

        if not package_data:
            raise Exception("获取图片包数据失败")

        logger.debug(f"获取到{package_data.get('list', [])}")

        # 解析下载链接
        download_url = self._parse_package_data(package_data)
        if not download_url:
            raise Exception("解析下载链接失败")

        # 下载文件
        if save_folder:
            file_path = self._download_file(download_url, save_folder, product_code)
            logger.debug(f"下载完成，文件路径：{file_path}")
            return file_path
        else:
            # 如果不提供保存目录，只返回下载链接
            return download_url
        
    def _get_basic_info(self) -> dict:
        """获取商品基本信息"""
        try:
            # 商品标题
            raw_goods_title = self.base.tab.ele(self.goods_title).text
            goods_title = raw_goods_title

            # 检查是否为特殊商品
            if any(word in goods_title for word in self.TITLE_FILTER_KEYWORDS):
                raise ValueError(f"商品标题包含特殊词，跳过: {goods_title}")

            # 提取商品编号、品牌、重量
            props_eles = self.base.tab.ele(self.goods_props).eles("t:li")
            goods_number = ""
            brand = ""
            weight = ""

            for ele in props_eles:
                text = ele.text
                if "商品编号：" in text:
                    raw_goods_number = text.split("：")[-1].strip()
                    goods_number = raw_goods_number
                elif "品牌：" in text:
                    raw_brand = text.split("：")[-1].strip()
                    brand = raw_brand
                elif "重量：" in text:
                    weight = text.split("：")[-1].strip()
                    # 处理重量格式：34.000 克(g) -> 34
                    try:
                        weight = weight.split(" ")[0]
                        weight = str(int(float(weight)))
                    except:
                        pass

            # 清理品牌名称
            cleaned_brand = self._clean_brand_name(brand)
            print(f"调试: 品牌清理: {brand} -> {cleaned_brand}")

            return {
                'title': goods_title,           # 商品标题
                'product_code': goods_number,   # 商品编号
                'brand': cleaned_brand,         # 清理后的品牌
                'weight': weight                # 重量
            }

        except Exception as e:
            safe_error = str(e)
            raise Exception(f"获取商品基本信息: {safe_error}")


    def _clean_brand_name(self, brand: str) -> str:
        """清理品牌名称，优先检查是否包含标准品牌名，然后再匹配映射表

        Args:
            brand: 原始品牌名称

        Returns:
            str: 清理后的标准品牌名称
        """
        if not brand or not brand.strip():
            return ""

        brand = brand.strip()

        # 获取品牌映射配置，添加错误处理
        try:
            brand_mapping = G.brand_mapping
        except Exception as e:
            logger.error(f"获取品牌映射配置失败: {e}")
            return brand

        # 第一步：优先检查是否直接包含标准品牌名
        for standard_brand in brand_mapping.keys():
            if standard_brand in brand:
                logger.debug(f"品牌包含标准名称 '{standard_brand}': {brand}")
                return standard_brand

        # 第二步：如果没有直接包含，再检查映射表中的变体
        for standard_brand, variants in brand_mapping.items():
            for variant in variants:
                if variant.lower() == brand.lower():
                    logger.debug(f"品牌匹配变体 '{variant}' -> '{standard_brand}': {brand}")
                    return standard_brand

        # 第三步：如果都没有匹配，返回原始品牌名
        logger.debug(f"品牌未找到匹配，保持原样: {brand}")
        return brand

    def _get_sku_data(self, weight: str) -> list:
        """获取SKU数据"""
        try:
            skus = []
            sku_trs = self.base.tab.ele(self.sku_table).s_eles("t:tr")
            logger.debug("获取skus", data=sku_trs)

            for sku_tr in sku_trs:
                sku_tds = sku_tr.s_eles("t:td")
                if len(sku_tds) < 6:  # 确保有足够的列
                    continue

                # 获取货号、规格、销售价、会员价、库存
                raw_sku_code = sku_tds[0].text.replace("货号：", "")
                raw_spec_value = sku_tds[1].text.replace("规格：", "")
                sale_price = sku_tds[2].text.replace("售价：", "")
                cost_price = sku_tds[3].text.replace("会员价：", "")
                stock_quantity = sku_tds[5].text.replace("库存：", "")

                # 过滤特殊字符
                sku_code = raw_sku_code
                spec_value = raw_spec_value
                if stock_quantity == '无货':
                    stock_quantity = 0

                # 跳过特殊商品
                if (any(word in spec_value for word in self.SKU_FILTER_KEYWORDS) or
                    any(price in cost_price for price in self.SKU_FILTER_PRICES)):
                    continue

                # 处理规格和价格
                spec_value = self._filter_string(spec_value)
                sale_price = self._filter_price(sale_price)
                cost_price = self._filter_price(cost_price)

                # 添加到SKU列表（与数据库字段对应）
                skus.append({
                    "sku_code": sku_code,              # 货号（SKU唯一主键）
                    "spec_value": spec_value,          # 规格值
                    "sale_price": sale_price,          # 售价
                    "cost_price": cost_price,          # 成本价
                    "weight": weight,                  # 重量
                    "stock_quantity": stock_quantity,  # 库存数量
                    "sku_status": "正常"               # SKU状态（默认正常）
                })

            logger.debug("最终商品SKU数据: ", data=skus)

            if len(skus) == 0:
                raise ValueError("没有有效的SKU数据")

            return skus

        except Exception as e:
            logger.debug(f"获取SKU数据异常: {str(e)}")
            raise Exception(f"获取SKU数据失败: {str(e)}")

    def _get_image_package(self) -> str:
        """获取图片包链接"""
        try:
            a_eles = self.base.tab.ele(self.goods_intro).eles("t:a")
            valid_links = []

            for a_ele in a_eles:
                img_url = a_ele.attr("href")
                if img_url and "yxfshop.com/refund/transit" in img_url:
                    valid_links.append(img_url)

            # 根据链接数量返回结果
            if len(valid_links) == 0:
                img_url = "没有图片包"
            elif len(valid_links) == 1:
                img_url = valid_links[0]
            else:
                img_url = valid_links[1]  # 取第二个链接

            return img_url

        except Exception as e:
            return "获取图片包链接失败"

    def _get_remark(self) -> str:
        """获取备注信息"""
        try:
            beizhu_text = ""
            for div in self.base.tab.ele(self.goods_intro).eles("t:div"):
                if any(word in div.text for word in self.REMARK_FILTER_KEYWORDS):
                    beizhu_text += div.text

            return beizhu_text

        except Exception:
            return "获取备注信息失败"

    def _filter_string(self, input_string: str) -> str:
        """处理规格字符串"""

        try:
            # 删除特殊字符
            special_chars = r'[\\/:*?"<>|]'
            filtered_string = re.sub(special_chars, '', input_string)

            # 删除"限价"及其后面的内容
            filtered_string = re.sub(r'\s*（?限价\s*\d*\.?\d*）?', '', filtered_string)

            # 删除结尾的"查看"
            filtered_string = re.sub(r'\s*查看\s*', '', filtered_string)

            return filtered_string.strip()
        except Exception:
            return input_string

    def _filter_price(self, price_string: str) -> str:
        """处理价格字符串"""
        import re
        try:
            # 去除所有非数字和小数点的字符
            price_clean = re.sub(r'[^\d.]', '', price_string)

            # 转换为浮点数并格式化
            price_float = float(price_clean)
            return f"{price_float:.2f}"
        except ValueError:
            return "0.00"

    def refresh_authorization(self) -> bool:
        """
        手动刷新Authorization

        Returns:
            bool: 刷新是否成功
        """
        authorization = self.get_authorization(force_refresh=True)
        return authorization is not None

    def get_cached_authorization(self) -> str:
        """
        获取当前缓存的Authorization（使用全局管理器）

        Returns:
            str: 当前缓存的Authorization，如果过期或不存在则返回None
        """
        return global_auth_manager.get_authorization()

    def get_authorization(self, force_refresh: bool = False) -> str:
        """
        获取Authorization（使用全局管理器）

        Args:
            force_refresh: 是否强制刷新

        Returns:
            str: Authorization值
        """
        # 首先尝试从全局管理器获取
        if not force_refresh:
            cached_auth = global_auth_manager.get_authorization()
            if cached_auth:
                return cached_auth

        try:
            # 构造请求参数
            params = {"bn": "4589809440140"}
            url = "https://www.yxfshop.com/refund/transit/skip.php"

            # 获取cookies
            cookies_dict = {}
            cookies_list = self.base.browser.cookies()
            for cookie in cookies_list:
                if cookie['domain'] in ['www.yxfshop.com', '.yxfshop.com']:
                    cookies_dict[cookie['name']] = cookie['value']

            # 构造headers
            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
            }

            # 发送请求
            response = requests.get(
                url,
                params=params,
                headers=headers,
                cookies=cookies_dict,
                allow_redirects=False
            )

            # 获取Location头
            location = response.headers.get('Location')
            if not location:
                return None

            # 提取Authorization
            if "s=" in location:
                authorization = location.split("s=")[-1]
                logger.debug(f"获取到新Authorization: {authorization[:20]}...")
                # 保存到全局管理器
                global_auth_manager.set_authorization(authorization, expire_minutes=30)
                return authorization
            else:
                logger.debug("Location中未找到Authorization参数")
                return None

        except Exception as e:
            logger.debug(f"获取Authorization异常: {e}")
            return None

    def _get_image_package_data(self, package_url: str, authorization: str) -> dict:
        """获取商品图片包信息"""
        url = "https://api.360zqf.com/yxf/api/file/queryList"

        userid = self.base.username

        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Authorization": authorization,
            "Userid": userid
        }

        # 提取bn_id
        bn_id = self._extract_bn_id(package_url)
        if not bn_id:
            raise Exception("✗ 提取bn_id失败")

        # 构造JSON格式的请求数据
        data = {"goodsCode": str(bn_id)}

        try:
            # 发送POST请求，使用json参数而不是data
            res = self.base.tab.post(url, json=data, headers=headers)
            res_json = res.json()

            # 检查是否为Authorization错误
            if is_authorization_error(res_json):
                global_auth_manager.clear()
                raise Exception(f"Authorization错误: {res_json.get('msg', '身份验证失败')}")

            if res_json.get("code") != 200:
                raise Exception(f"获取图片包信息失败: {res_json.get('code')} - {res_json.get('msg', '')}")

            return res_json.get("data")
        except Exception as e:
            if "Authorization错误" in str(e):
                raise  # 重新抛出Authorization错误
            raise Exception(f"获取图片包信息失败: {str(e)}")

    def _extract_bn_id(self, url: str) -> str:
        """从URL中提取bn_id"""
        try:
            if "bn=" in url:
                # 提取bn参数值
                bn_part = url.split("bn=")[-1]
                # 如果有其他参数，只取bn参数部分
                if "&" in bn_part:
                    bn_part = bn_part.split("&")[0]
                # 返回提取的bn参数
                return bn_part
            elif "bn_id=" in url:
                bn_part = url.split("bn_id=")[-1]
                if "&" in bn_part:
                    bn_part = bn_part.split("&")[0]
                return bn_part

            logger.debug("URL中未找到bn参数")
            return None
        except Exception as e:
            logger.debug(f"提取bn_id异常: {e}")
            return None

    def _extract_userid(self, url: str) -> str:
        """从URL中提取用户ID"""
        try:
            if "u=" in url:
                # 提取u参数值
                u_part = url.split("u=")[-1]
                # 如果有其他参数，只取u参数部分
                if "&" in u_part:
                    u_part = u_part.split("&")[0]

                return u_part

            return None
        except Exception:
            return None

    def _parse_package_data(self, package_data: dict) -> str:
        """解析图片包数据，返回最新的压缩包下载链接"""
        try:
            if not package_data or not isinstance(package_data, dict):
                logger.debug("图片包数据为空或格式错误")
                return None

            # 获取文件列表
            file_list = package_data.get('list', [])
            if not file_list:
                logger.debug("文件列表为空")
                return None

            # 压缩文件后缀列表
            zip_extensions = [".zip", ".rar", ".7z", ".tar", ".gz", ".bz2", ".xz"]

            # 过滤出压缩包文件
            zip_files = []
            for file_info in file_list:
                file_name = file_info.get('originalName', '')
                if any(file_name.lower().endswith(ext) for ext in zip_extensions):
                    zip_files.append(file_info)

            if not zip_files:
                logger.debug("未找到压缩包文件")
                return None

            # 按创建时间排序，获取最新的压缩包
            sorted_zip_files = sorted(
                zip_files,
                key=lambda x: x.get('createTime', 0),
                reverse=True
            )

            # 选择压缩包，跳过包含"视频"的文件
            selected_zip = None
            for zip_file in sorted_zip_files:
                file_name = zip_file.get('originalName', '')
                if "视频" in file_name:
                    logger.debug(f"跳过包含'视频'的压缩包: {file_name}")
                    continue
                else:
                    selected_zip = zip_file
                    break

            if not selected_zip:
                logger.debug("所有压缩包都包含'视频'关键词，选择最新的")
                selected_zip = sorted_zip_files[0]

            download_url = selected_zip.get('url')
            logger.debug(f"选择压缩包: {selected_zip.get('originalName', 'Unknown')}")
            return download_url

        except Exception as e:
            logger.debug(f"解析图片包数据异常: {e}")
            return None

    def _download_file(self, download_url: str, save_folder: str, product_code: str = None) -> str:
        """
        下载文件

        Returns:
            str: 下载成功时返回文件路径

        Raises:
            Exception: 下载失败时抛出异常
        """
        from pathlib import Path

        # 创建保存目录
        save_path = Path(save_folder)
        save_path.mkdir(parents=True, exist_ok=True)

        # 下载文件，使用内置的rename参数
        if product_code:
            # 使用商品编号作为文件名，DrissionPage会自动补充扩展名
            result = self.base.tab.download(
                download_url,
                save_path,
                rename=product_code,
                timeout=30,
                show_msg=False  # 禁用进度显示
            )

            if result[0] == "success":
                logger.debug(f"文件下载并重命名成功: {result[1]}")
                return result[1]
            else:
                raise Exception(f"下载失败: {result[1]}")
        else:
            # 不重命名，使用原文件名
            result = self.base.tab.download(
                download_url,
                save_path,
                timeout=30,
                show_msg=False  # 禁用进度显示
            )

            if result[0] == "success":
                logger.debug(f"文件下载成功: {result[1]}")
                return result[1]
            else:
                raise Exception(f"下载失败: {result[1]}")


class YixingfangOperator:
    """伊性坊数据操作类"""

    def __init__(self, base: YixingfangBase):
        self.base = base

    def operate_data(self):
        """数据操作方法 - 待实现"""
        # 直接使用基础类的定位器
        # self.base.page.ele(self.base.submit_btn).click()
        # TODO: 实现数据操作逻辑
        pass


if __name__ == "__main__":
    yxf_username = G.yixingfang.username
    yxf_password = G.yixingfang.password
    tj_username = G.tujian.username
    tj_password = G.tujian.password
    yxf = YixingfangBase(yxf_username, yxf_password, tj_username, tj_password)
    yxf_collector = YixingfangCollector(yxf)

    result = yxf_collector.download_image_package('https://www.yxfshop.com/refund/front/#/file/detail?u=shike&bn=6942508600402-NY1ZFT005247-NY1ZFT005248-NY1ZFT005249-NY1ZFT005250-NY1ZFT005251&s=73530c97b95a9a766e2f7bb5112ed970', r'C:\Users\<USER>\Pictures', 'codesdsf')
    logger.debug(result)