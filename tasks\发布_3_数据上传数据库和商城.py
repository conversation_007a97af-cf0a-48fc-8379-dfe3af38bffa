"""
通用任务模板
复制此文件到 tasks/ 目录并重命名为你的任务名
"""
import sys
from pathlib import Path
from datetime import datetime

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))

# 智能导入 - 自动初始化配置和日志
from core import G, logger

from services.module import get_files_natural_sorted
from services.excel_manager import ExcelManager
from services.qiniuyun_manager import QiniuManager
from services.database_manager import product_manager, shop_product_manager
from services.crmeb_manager import CrmebProductManager
from services.module import check_file_exists_in_folder


def validate_spec_images_completeness(products, image_package_folder):
    """
    验证所有产品的规格图片完整性

    Args:
        products: 产品列表，每个产品包含product_code和spec_value字段
        image_package_folder: 图片包根文件夹路径

    Returns:
        bool: 所有规格图片都存在返回True，否则抛出异常

    Raises:
        Exception: 当发现缺失的规格图片时抛出异常
    """


    print("开始检查规格图片完整性...", level=INFO)
    missing_spec_images = []
    missing_folders = []
    total_products = len(products)
    valid_products = 0  # 有效商品数量（有product_code和spec_value）
    success_products = 0  # 成功通过检查的商品数量

    sku_count = 0  # 总SKU数量计数

    for i, product in enumerate(products, 1):
        product_code = product.get('product_code', '')
        skus = product.get('skus', [])

        if not product_code:
            continue

        if not skus:
            continue

        # 检查每个SKU的规格图片
        for sku in skus:
            spec_value = sku.get('spec_value', '')
            sku_count += 1

            if not spec_value:
                continue

            valid_products += 1

            # 构建规格文件夹路径
            spec_folder_path = image_package_folder / product_code / "规格"

            # 检查文件夹是否存在
            if not spec_folder_path.exists():
                missing_folders.append({
                    'product_code': product_code,
                    'spec_value': spec_value,
                    'spec_folder': str(spec_folder_path)
                })
                # 不要continue，继续检查下一个SKU
            else:
                # 文件夹存在，检查规格图片是否存在
                exists = check_file_exists_in_folder(spec_folder_path, spec_value, [".jpg", ".png", ".gif", ".jpeg"])

                if not exists:
                    missing_spec_images.append({
                        'product_code': product_code,
                        'spec_value': spec_value,
                        'spec_folder': str(spec_folder_path)
                    })
                else:
                    # 只有文件夹存在且图片存在才算成功
                    success_products += 1

        # 简化进度显示
        if i == total_products:
            print(f"检查完成: {total_products} 个商品, {sku_count} 个SKU", level=INFO)

    # 检查结果汇总
    total_missing = len(missing_spec_images) + len(missing_folders)

    if total_missing > 0:
        print(f"\n❌ 规格图片检查失败！发现 {total_missing} 个问题", level=ERROR)

        if missing_folders:
            print(f"\n📁 缺失文件夹详情 ({len(missing_folders)} 个):", level=ERROR)
            for i, missing in enumerate(missing_folders):  # 只显示前10个
                print(f"  {i}. product_code: {missing['product_code']}, spec_value: {missing['spec_value']}", level=ERROR)

        if missing_spec_images:
            print(f"\n🖼️ 缺失图片文件详情 ({len(missing_spec_images)} 个):", level=ERROR)
            for i, missing in enumerate(missing_spec_images, 1):  
                print(f"  {i}. product_code: {missing['product_code']}, spec_value: {missing['spec_value']}", level=ERROR)
            
        raise Exception(f"规格图片检查失败，共发现 {total_missing} 个问题（文件夹缺失: {len(missing_folders)}, 图片缺失: {len(missing_spec_images)}）")
    else:
        return True


def get_sku_code_dict(skus):
    """get_sku_code_dict 从skus中获取规格值对应的sku_code

    Args:
        skus (list): 商品的skus列表

    Returns:
        dict: 规格值对应的sku_code字典
    """
    sku_code_dict = {}
    for sku in skus:
        spec_value = sku.get('spec_value', '')
        sku_code = sku.get('sku_code', '')
        if spec_value and sku_code:
            sku_code_dict[spec_value] = sku_code
    return sku_code_dict

def update_skus_with_sku_code(skus, specs):
    """update_skus_with_sku_code 更新skus将sku_code和specs匹配

    Args:
        skus (list): 商品的skus列表
        specs (list): 规格图片路径列表

    Returns:
        list: 更新后的skus列表
    """
    updated_skus = []
    for sku in skus:
        updated_sku = sku.copy()
        sku_code = sku.get('sku_code', '')

        # 确保sku_code是字符串类型
        if not isinstance(sku_code, str):
            sku_code = str(sku_code)

        for spec in specs:
            if sku_code and sku_code in spec:
                updated_sku['spec_image'] = spec
                break
        updated_skus.append(updated_sku)
    return updated_skus


def one_product_update(product_code, product_folder, qiniu_manager, qiniu_config, sku_code_dict):
    """one_product_update 单商品上传七牛云

    Args:
        product_code (str): 商品编码
        product_folder (Path): 商品图片包文件夹路径
        qiniu_manager (QiniuManager): 七牛云管理器实例
        qiniu_config (dict): 七牛云配置
        sku_code_dict (dict): 商品的SKU字典(获取规格值对应的sku_code)

    Returns:
        tuple: 主图列表, 详情图列表, 规格图片列表
    """
    try:
        # 定义要处理的子文件夹
        folder_mapping = {'主图': 'main', '详情': 'detail', '规格': 'spec'}
        mains = []
        details = []
        specs = []

        for folder_name, folder_type in folder_mapping.items():
            folder_path = product_folder / folder_name
            images = get_files_natural_sorted(folder_path)
            
            qiniu_sava_path = f"{qiniu_config.sava_img_path}/{product_code}/{folder_type}"

            # 上传图片到七牛云
            for image in images:
                if folder_name != '规格':
                    name, ext = os.path.splitext(image.absolute().name)
                    random_suffix = ''.join([str(random.randint(0, 9)) for _ in range(6)])
                    sava_image_name = f"{name}_{random_suffix}{ext}"
                else:
                    name, ext = os.path.splitext(image.absolute().name)
                    code_name = sku_code_dict.get(name, name)
                    sava_image_name = f"{code_name}{ext}"
                
                result = qiniu_manager.upload_file(image, f"{qiniu_sava_path}/{sava_image_name}")
                
                # 保存图片url到列表中，用于后续处理数据库和商城上传逻辑
                if folder_name != '规格':
                    if folder_type == 'main':
                        mains.append(result['url'])
                    elif folder_type == 'detail':
                        details.append(result['url'])
                else:
                    specs.append(result['url'])

        return mains, details, specs

    except Exception as e:
        print(f"上传到七牛云失败: {product_code} - {e}", level=ERROR)
        raise 


def main():
    # 读取配置
    main_folder = G.local.main_folder
    excel_file_path = Path(main_folder).joinpath(G.local.products_excel_file_name)
    image_package_folder = Path(main_folder).joinpath(G.local.image_package_folder)
    print(image_package_folder)
    qiniu_config = G.qiniu
    crmeb_config = G.skcrmeb    

    # 类方法初始化
    excel_manager = ExcelManager(excel_file_path, "商品表")
    qiniu_manager = QiniuManager(qiniu_config)
    crmeb_product_manager = CrmebProductManager(crmeb_config)

    # 读取表格数据和本地数据
    products = excel_manager.read_all_products_from_excel(include_all_fields=True)
    print(f"开始处理 {len(products)} 个商品", level=INFO)

    # 简化调试信息
    total_skus = sum(len(product.get('skus', [])) for product in products)
    print(f"数据概览: {len(products)} 个商品, 共 {total_skus} 个SKU", level=INFO)

    # 检查规格图片完整性
    validate_spec_images_completeness(products, image_package_folder)
    print("开始执行")

    for product in products:
        print(f"开始处理商品: {product['product_code']}", level=INFO)
        product_code = product.get('product_code', 'Unknown')
        product_folder = image_package_folder / product_code
        skus = product.get('skus', [])
        sku_code_dict = get_sku_code_dict(skus)

        try:
            if not product_folder.exists():
                print(f"商品文件夹不存在，跳过: {product_code}", level=WARNING)
                continue

            # 上传到七牛云
            mains, details, specs = one_product_update(product_code, product_folder, qiniu_manager, qiniu_config, sku_code_dict)
            print(f"上传到七牛云完成: mains:{mains}, details:{details}, specs:{specs}", level=SUCCESS)

            # 准备数据库数据 上传
            print(f"开始上传到数据库: {product_code}", level=DEBUG)
            product['main_images'] = mains
            product['detail_images'] = details
            product['skus'] = update_skus_with_sku_code(skus, specs)
            print(f"上传数据库数据: {product}", level=DEBUG)

            # 保存完整的商品数据用于商城上传（因为create_product会pop掉skus）
            product_for_crmeb = product.copy()
            product_manager.create_product(product)
            print(f"上传到数据库完成: {product_code}", level=DEBUG)

            # 上传到时刻商城
            product_sk = crmeb_product_manager.convert_product_data_for_upload(product_for_crmeb, ['规格', '描述'])
            print(f"上传到商城的数据: {product_sk}", level=DEBUG)
            crmeb_product_manager.upload_product(product_sk)
            sk_id = crmeb_product_manager.search_product_by_keyword(product_code)

            # 增加到店铺关联表里面
            shop_product_manager.add_product_to_shop('shike_shop', product_code, sk_id)
            print(f"商品处理完成: {product_code}，时刻商品ID: {sk_id}", level=SUCCESS)

        except Exception as e:
            print(f"处理商品异常 {product_code}: {e}", level=ERROR)
            continue

    print("所有商品处理完成", level=SUCCESS)


if __name__ == "__main__":
    main()
