"""
通用任务模板
复制此文件到 tasks/ 目录并重命名为你的任务名
"""
import sys
from pathlib import Path
from datetime import datetime

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))

# 智能导入 - 自动初始化配置和日志
from core import G, logger
from services.excel_manager import ExcelManager
from services.yixingfang_manager import YixingfangBase, YixingfangCollector
from services.image_package_processor import ImagePackageProcessor

def main():
    # 获取配置参数
    main_folder = G.local.main_folder
    excel_file_path = Path(main_folder).joinpath(G.local.products_excel_file_name)
    image_package_folder = Path(main_folder).joinpath(G.local.image_package_folder)

    # 初始化类方法管理器
    excel_manager = ExcelManager(excel_file_path, "商品表")
    yxf_base = YixingfangBase()
    yxf_base.login()
    yxf_collector = YixingfangCollector(yxf_base)
    image_processor = ImagePackageProcessor()

    # 从表格读取商品数据（需要读取所有字段以获取image_package_url）
    produsts = excel_manager.read_all_products_from_excel(include_all_fields=True)
    len_products = len(produsts)
    for i,produst in enumerate(produsts):
        produst_code = produst['product_code']
        image_package_url = produst.get('image_package_url', '')
        print(f"开始处理商品: {produst_code}，图片包URL: {image_package_url}")

        # 检查商品文件夹是否已存在
        product_folder_path = image_package_folder / produst_code
        if product_folder_path.exists() and product_folder_path.is_dir():
            print(f"{produst_code}文件夹已存在，跳过处理 ({i+1}/{len_products})", level=INFO)
            continue

        if not image_package_url or str(image_package_url).strip() == '':
            print(f"{produst_code}图片包URL为空，跳过", level=WARNING)
            continue

        try:
            image_package_path = yxf_collector.download_image_package(image_package_url, image_package_folder, produst_code)
            produst_image_package_folder = image_processor.extract_package(image_package_path, delete_original=True)
            image_processor.process_extracted_folder(produst_image_package_folder)
            image_processor.organize_image_package_folder(produst_image_package_folder)
            image_processor.process_main_image_folder_deep(produst_image_package_folder)
            image_processor.process_detail_image_folder_deep(produst_image_package_folder)
            filter_result = image_processor.filter_sensitive_files_in_image_folders(produst_image_package_folder)
            if not filter_result:
                print(f"{produst_code}过滤后文件数量不足，跳过后续处理", level=WARNING)
                continue
            image_processor.sort_and_rename_main_images(produst_image_package_folder)
            image_processor.sort_and_rename_detail_images(produst_image_package_folder)
            image_processor.clean_spec_folder_non_images(produst_image_package_folder)
            print(f"{produst_code}处理完成：{i+1}/{len_products}", level=SUCCESS)
        except Exception as e:
            print(f"{produst_code}处理失败跳过: {e}", level=WARNING)


if __name__ == "__main__":
    main()
