"""
通用任务模板
复制此文件到 tasks/ 目录并重命名为你的任务名
"""
import sys
from pathlib import Path
from datetime import datetime

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))

# 智能导入 - 自动初始化配置和日志
from core import G, logger
from services.excel_manager import ExcelManager
from services.yixingfang_manager import YixingfangBase, YixingfangCollector
from services.image_package_processor import ImagePackageProcessor


def main():
    logger.step("任务初始化", "开始图片包下载和处理任务")

    # 获取配置参数
    main_folder = G.local.main_folder
    excel_file_path = Path(main_folder).joinpath(G.local.products_excel_file_name)
    image_package_folder = Path(main_folder).joinpath(G.local.image_package_folder)

    # 初始化类方法管理器
    logger.step("系统初始化", "准备Excel管理器和图片处理器")
    excel_manager = ExcelManager(excel_file_path, "商品表")
    yxf_base = YixingfangBase()
    yxf_collector = YixingfangCollector(yxf_base)
    image_processor = ImagePackageProcessor()
    logger.info("系统初始化完成", status="ready")

    # 从表格读取商品数据（需要读取所有字段以获取image_package_url）
    produsts = excel_manager.read_all_products_from_excel(include_all_fields=True)
    len_products = len(produsts)

    if not produsts:
        logger.warning("Excel中没有商品数据", file=G.local.products_excel_file_name)
        return

    logger.step("开始处理", f"共{len_products}个商品", count=len_products)

    processed_count = 0
    skipped_count = 0
    failed_count = 0

    for i, produst in enumerate(produsts):
        produst_code = produst['product_code']
        image_package_url = produst.get('image_package_url', '')
        progress = f"{i+1}/{len_products}"

        logger.info("开始处理商品", product_code=produst_code, url=image_package_url, progress=progress)

        # 检查商品文件夹是否已存在
        product_folder_path = image_package_folder / produst_code
        if product_folder_path.exists() and product_folder_path.is_dir():
            logger.info("文件夹已存在，跳过处理", product_code=produst_code, progress=progress)
            skipped_count += 1
            continue

        if not image_package_url or str(image_package_url).strip() == '':
            logger.warning("图片包URL为空，跳过", product_code=produst_code, progress=progress)
            skipped_count += 1
            continue

        try:
            logger.debug("开始下载图片包", product_code=produst_code, url=image_package_url)
            image_package_path = yxf_collector.download_image_package(image_package_url, image_package_folder, produst_code)

            logger.debug("开始解压图片包", product_code=produst_code)
            produst_image_package_folder = image_processor.extract_package(image_package_path, delete_original=True)

            logger.debug("开始处理图片", product_code=produst_code)
            image_processor.process_extracted_folder(produst_image_package_folder)
            image_processor.organize_image_package_folder(produst_image_package_folder)
            image_processor.process_main_image_folder_deep(produst_image_package_folder)
            image_processor.process_detail_image_folder_deep(produst_image_package_folder)

            logger.debug("开始过滤敏感文件", product_code=produst_code)
            filter_result = image_processor.filter_sensitive_files_in_image_folders(produst_image_package_folder)
            if not filter_result:
                logger.warning("过滤后文件数量不足，跳过后续处理", product_code=produst_code, progress=progress)
                skipped_count += 1
                continue

            logger.debug("开始整理和重命名图片", product_code=produst_code)
            image_processor.sort_and_rename_main_images(produst_image_package_folder)
            image_processor.sort_and_rename_detail_images(produst_image_package_folder)
            image_processor.clean_spec_folder_non_images(produst_image_package_folder)

            logger.success("商品处理完成", product_code=produst_code, progress=progress)
            processed_count += 1

        except Exception as e:
            logger.warning(f"商品处理失败: {e}", product_code=produst_code, progress=progress)
            failed_count += 1

    # 任务完成统计
    logger.step("任务完成", "统计处理结果")
    logger.success("图片包处理任务完成",
                  total=len_products,
                  processed=processed_count,
                  skipped=skipped_count,
                  failed=failed_count)


if __name__ == "__main__":
    main()
