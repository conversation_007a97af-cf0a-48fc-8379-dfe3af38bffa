"""
通用任务模板
复制此文件到 tasks/ 目录并重命名为你的任务名
"""
import sys
from pathlib import Path
from datetime import datetime
import time
# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))

# 智能导入 - 自动初始化配置和日志
from core import G, logger
from services import module
from services.yixingfang_manager import YixingfangBase, YixingfangCollector
from services.database_manager import product_manager
from services.excel_manager import ExcelManager


def main():
    logger.step("任务初始化", "开始商品采集任务")

    # 获取配置参数
    main_folder = G.local.main_folder
    urls_txt_file_path = Path(main_folder).joinpath(G.local.urls_txt_file_name)
    excel_file_path = Path(main_folder).joinpath(G.local.products_excel_file_name)

    logger.debug("配置参数", main_folder=main_folder, urls_file=G.local.urls_txt_file_name, excel_file=G.local.products_excel_file_name)

    # 读取商品URL列表
    try:
        product_urls = module.read_txt_to_list(urls_txt_file_path, remove_duplicates=True)
        logger.info("URL列表读取完成", count=len(product_urls), file=G.local.urls_txt_file_name)
    except Exception as e:
        logger.error(f"读取URL文件失败: {e}", file=str(urls_txt_file_path), status="failed")
        return

    if not product_urls:
        logger.warning("URL列表为空", file=G.local.urls_txt_file_name, status="empty")
        return

    # 初始化采集器和Excel管理器
    logger.step("系统初始化", "准备采集器和Excel管理器")
    try:
        yxf_base = YixingfangBase()
        yxf_base.login()
        yxf_collector = YixingfangCollector(yxf_base)
        excel_manager = ExcelManager(excel_file_path, "商品表")
        logger.info("系统初始化完成", status="ready")
    except Exception as e:
        logger.error(f"系统初始化失败: {e}", status="failed")
        return

    # 第一阶段：同步采集所有商品数据
    print("=== 第一阶段：采集商品数据 ===")
    collected_products = []

    for i, product_url in enumerate(product_urls, 1):
        try:
            print(f"采集进度: {i}/{len(product_urls)} - {product_url}")

            # 采集商品数据
            product_data = yxf_collector.collect_product_data(product_url)
            product_code = product_data.get('product_code', '')

            if product_code:
                collected_products.append(product_data)
                print(f"采集成功: {product_code}")
            else:
                print(f"采集失败: 无商品编号")

        except Exception as e:
            print(f"采集失败: {product_url} - {e}")

    print(f"采集阶段完成，成功采集 {len(collected_products)} 个商品")

    if not collected_products:
        print("没有成功采集到商品数据，程序结束")
        return

    # 第二阶段：处理和格式化
    print("\n=== 第二阶段：处理和格式化 ===")

    processed_count = 0
    failed_count = 0
    total_count = len(collected_products)

    start_time = time.time()

    for i, product_data in enumerate(collected_products, 1):
        product_code = product_data.get('product_code', 'Unknown')

        try:
            # 检查商品编号是否已经存在数据库
            if product_manager.product_exists(product_code):
                print(f"商品{product_code}已存在数据库，跳过 ({i}/{total_count})")
                continue

            # 格式化数据
            print(f"正在格式化商品数据: {product_code}（AI处理中...） ({i}/{total_count})")
            format_start = time.time()

            formatted_data = excel_manager.format_product_data(product_data)
            format_end = time.time()

            print(f"商品{product_code}格式化完成，耗时: {format_end - format_start:.2f}秒")

            # 写入Excel
            if excel_manager.write_to_excel(formatted_data, mode='append'):
                print(f"商品{product_code}写入Excel成功")
                processed_count += 1
            else:
                print(f"商品{product_code}写入Excel失败")
                failed_count += 1

        except Exception as e:
            print(f"处理商品{product_code}失败 ({i}/{total_count}): {e}")
            failed_count += 1

    end_time = time.time()

    print(f"总耗时: {end_time - start_time:.2f}秒")
    print(f"成功处理: {processed_count} 个商品")
    print(f"处理失败: {failed_count} 个商品")

    if processed_count > 0:
        avg_time = (end_time - start_time) / len(collected_products)
        print(f"平均处理时间: {avg_time:.2f}秒/商品")


if __name__ == "__main__":
    main()
