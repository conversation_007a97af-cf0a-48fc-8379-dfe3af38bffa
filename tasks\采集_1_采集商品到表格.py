"""
通用任务模板
复制此文件到 tasks/ 目录并重命名为你的任务名
"""
import sys
from pathlib import Path
from datetime import datetime
import time
# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))

# 智能导入 - 自动初始化配置和日志
from core import G, logger
from services import module
from services.yixingfang_manager import YixingfangBase, YixingfangCollector
from services.database_manager import product_manager
from services.excel_manager import ExcelManager


def main():
    logger.step("任务初始化", "开始商品采集和处理任务")

    # 获取配置参数
    main_folder = G.local.main_folder
    urls_txt_file_path = Path(main_folder).joinpath(G.local.urls_txt_file_name)
    excel_file_path = Path(main_folder).joinpath(G.local.products_excel_file_name)

    product_urls = module.read_txt_to_list(urls_txt_file_path, remove_duplicates=True)
    logger.info("URL列表读取完成", count=len(product_urls), file=G.local.urls_txt_file_name)

    if not product_urls:
        logger.warning("URL列表为空，任务结束", file=G.local.urls_txt_file_name)
        return

    # 初始化采集器和Excel管理器
    logger.step("系统初始化", "准备采集器和Excel管理器")
    yxf_base = YixingfangBase()
    yxf_base.login()
    yxf_collector = YixingfangCollector(yxf_base)
    excel_manager = ExcelManager(excel_file_path, "商品表")
    logger.info("系统初始化完成", status="ready")

    # 统计变量
    collected_count = 0
    processed_count = 0
    failed_count = 0
    total_count = len(product_urls)

    start_time = time.time()
    logger.step("开始处理", f"共{total_count}个商品URL", count=total_count)

    # 一次循环：采集 -> 处理 -> 写入
    for i, product_url in enumerate(product_urls, 1):
        logger.debug("开始处理商品", url=product_url, progress=f"{i}/{total_count}")

        try:
            # 1. 采集商品数据
            logger.info(f"采集商品数据", url=product_url, progress=f"{i}/{total_count}")
            product_data = yxf_collector.collect_product_data(product_url)
            product_code = product_data.get('product_code', '')

            if not product_code:
                logger.warning("采集失败：无商品编号", url=product_url, progress=f"{i}/{total_count}")
                failed_count += 1
                continue

            logger.debug("采集成功", product_code=product_code, url=product_url)
            collected_count += 1

            # 2. 检查是否已存在数据库
            if product_manager.product_exists(product_code):
                logger.info("商品已存在，跳过处理", product_code=product_code, progress=f"{i}/{total_count}")
                continue

            # 3. AI处理和格式化
            logger.info("开始AI处理", product_code=product_code, progress=f"{i}/{total_count}")
            format_start = time.time()

            formatted_data = excel_manager.format_product_data(product_data)
            format_duration = time.time() - format_start

            logger.debug("AI处理完成", product_code=product_code, duration=f"{format_duration:.2f}s")

            # 4. 写入Excel
            if excel_manager.write_to_excel(formatted_data, mode='append'):
                logger.success("商品处理完成", product_code=product_code, duration=f"{format_duration:.2f}s", progress=f"{i}/{total_count}")
                processed_count += 1
            else:
                logger.error("Excel写入失败", product_code=product_code, progress=f"{i}/{total_count}")
                failed_count += 1

        except Exception as e:
            logger.warning(f"商品处理异常: {e}", url=product_url, progress=f"{i}/{total_count}")
            failed_count += 1

    # 任务完成统计
    end_time = time.time()
    total_duration = end_time - start_time

    logger.step("任务完成", "统计处理结果")
    logger.success("任务执行完成",
                  total_urls=total_count,
                  collected=collected_count,
                  processed=processed_count,
                  failed=failed_count,
                  duration=f"{total_duration:.2f}s")

    if processed_count > 0:
        avg_time = total_duration / total_count
        logger.info("性能统计", avg_time=f"{avg_time:.2f}s/商品", file=G.local.products_excel_file_name)


if __name__ == "__main__":
    main()
