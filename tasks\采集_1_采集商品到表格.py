"""
通用任务模板
复制此文件到 tasks/ 目录并重命名为你的任务名
"""
import sys
from pathlib import Path
import threading
from datetime import datetime
import time
from queue import Queue, Empty
from concurrent.futures import ThreadPoolExecutor
# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))

# 智能导入 - 自动初始化配置和日志
from core import G, logger
from services import module
from services.yixingfang_manager import YixingfangBase, YixingfangCollector
from services.database_manager import product_manager
from services.excel_manager import ExcelManager

class AsyncProductProcessor:
    """异步商品处理器"""

    def __init__(self, excel_manager, max_workers=3):
        self.excel_manager = excel_manager
        self.max_workers = max_workers
        self.excel_write_queue = Queue()
        self.excel_write_lock = threading.Lock()
        self.processed_count = 0
        self.failed_count = 0

    def process_product_sync(self, product_data):
        """同步处理单个商品（在线程池中运行）"""
        product_code = product_data.get('product_code', 'Unknown')

        try:
            print(f"开始处理商品: {product_code}")

            # 检查商品编号是否已经存在数据库
            if product_manager.product_exists(product_code):
                print(f"商品{product_code}已存在数据库，跳过")
                return None

            # 格式化数据（这里是最耗时的AI调用部分）
            print(f"正在格式化商品数据: {product_code}（AI处理中...）")
            start_time = time.time()

            # Excel管理器内部已经有完善的容错机制，直接调用即可
            formatted_data = self.excel_manager.format_product_data(product_data)
            end_time = time.time()
            print(f"商品{product_code}格式化完成，耗时: {end_time - start_time:.2f}秒")

            return {
                'product_code': product_code,
                'formatted_data': formatted_data,
                'success': True
            }

        except Exception as e:
            print(f"处理商品{product_code}失败: {e}")
            return {
                'product_code': product_code,
                'error': str(e),
                'success': False
            }


    def excel_writer_worker(self):
        """Excel写入工作线程"""
        print("Excel写入工作线程已启动")

        while True:
            try:
                item = self.excel_write_queue.get(timeout=2)  # 增加超时时间到2秒
                if item is None:  # 结束信号
                    print("Excel写入工作线程收到结束信号")
                    break

                with self.excel_write_lock:
                    if self.excel_manager.write_to_excel(item['formatted_data'], mode='append'):
                        print(f"商品{item['product_code']}写入Excel成功")
                        self.processed_count += 1
                    else:
                        print(f"商品{item['product_code']}写入Excel失败")
                        self.failed_count += 1

                self.excel_write_queue.task_done()

            except Empty:
                # 队列为空是正常的，继续等待
                continue
            except Exception as e:
                # 其他真正的异常才打印错误
                print(f"Excel写入工作线程异常: {e} (类型: {type(e).__name__})")
                continue

        print("Excel写入工作线程已结束")

    def process_products_async(self, product_data_list):
        """异步处理商品列表"""
        print(f"开始异步处理 {len(product_data_list)} 个商品，最大并发数: {self.max_workers}")

        # 启动Excel写入工作线程
        excel_thread = threading.Thread(target=self.excel_writer_worker, daemon=True)
        excel_thread.start()

        completed_count = 0
        total_count = len(product_data_list)

        # 使用线程池处理商品格式化
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交所有任务
            future_to_product = {
                executor.submit(self.process_product_sync, product_data): product_data
                for product_data in product_data_list
            }

            # 处理完成的任务
            for future in future_to_product:
                try:
                    result = future.result()
                    completed_count += 1

                    if result and result['success']:
                        # 将成功处理的数据加入写入队列
                        self.excel_write_queue.put({
                            'product_code': result['product_code'],
                            'formatted_data': result['formatted_data']
                        })
                        print(f"处理完成 ({completed_count}/{total_count}): {result['product_code']}")

                    elif result and not result['success']:
                        self.failed_count += 1
                        print(f"处理失败 ({completed_count}/{total_count}): {result['product_code']}")
                        if 'error' in result:
                            print(f"  错误详情: {result['error']}")
                    else:
                        # result为None的情况（商品已存在等）
                        print(f"跳过处理 ({completed_count}/{total_count}): 商品已存在或其他原因")

                except Exception as e:
                    completed_count += 1
                    print(f"获取处理结果异常 ({completed_count}/{total_count}): {e}")
                    self.failed_count += 1

        print(f"所有商品格式化完成，等待Excel写入完成...")

        # 等待所有Excel写入完成
        self.excel_write_queue.join()

        # 发送结束信号给Excel写入线程
        self.excel_write_queue.put(None)
        excel_thread.join(timeout=10)  # 增加等待时间

        print(f"异步处理完成: 成功{self.processed_count}个，失败{self.failed_count}个")


def main():
    # 获取配置参数
    main_folder = G.local.main_folder
    urls_txt_file_path = Path(main_folder).joinpath(G.local.urls_txt_file_name)
    excel_file_path = Path(main_folder).joinpath(G.local.products_excel_file_name)

    product_urls = module.read_txt_to_list(urls_txt_file_path, remove_duplicates=True)
    print(f"读取到 {len(product_urls)} 个商品URL")

    # 初始化采集器和Excel管理器
    yxf_base = YixingfangBase()
    yxf_base.login()
    yxf_collector = YixingfangCollector(yxf_base)
    excel_manager = ExcelManager(excel_file_path, "商品表")

    # 第一阶段：同步采集所有商品数据
    print("=== 第一阶段：采集商品数据 ===")
    collected_products = []

    for i, product_url in enumerate(product_urls, 1):
        try:
            print(f"采集进度: {i}/{len(product_urls)} - {product_url}")

            # 采集商品数据
            product_data = yxf_collector.collect_product_data(product_url)
            product_code = product_data.get('product_code', '')

            if product_code:
                collected_products.append(product_data)
                print(f"采集成功: {product_code}")
            else:
                print(f"采集失败: 无商品编号")

        except Exception as e:
            print(f"采集失败: {product_url} - {e}")

    print(f"采集阶段完成，成功采集 {len(collected_products)} 个商品")

    if not collected_products:
        print("没有成功采集到商品数据，程序结束")
        return

    # 第二阶段：异步处理和格式化
    print("\n=== 第二阶段：异步处理和格式化 ===")

    # 创建异步处理器（最大并发数可以根据需要调整）
    max_workers = min(3, len(collected_products))  # 最多3个并发，避免AI接口压力过大
    processor = AsyncProductProcessor(excel_manager, max_workers=max_workers)

    # 开始异步处理
    start_time = time.time()
    processor.process_products_async(collected_products)
    end_time = time.time()

    print(f"总耗时: {end_time - start_time:.2f}秒")
    print(f"成功处理: {processor.processed_count} 个商品")
    print(f"处理失败: {processor.failed_count} 个商品")

    if processor.processed_count > 0:
        avg_time = (end_time - start_time) / len(collected_products)
        print(f"平均处理时间: {avg_time:.2f}秒/商品")


if __name__ == "__main__":
    main()
