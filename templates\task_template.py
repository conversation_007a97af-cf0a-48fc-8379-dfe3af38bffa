"""
通用任务模板
复制此文件到 tasks/ 目录并重命名为你的任务名
"""
import sys
from pathlib import Path
from datetime import datetime

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))

# 智能导入 - 自动初始化配置和日志
from core import G, logger


def main():
    """任务主函数"""
    logger.step("开始执行任务")

    # 1. 读取配置
    task_name = G.file_processing_keywords
    # logger.info(f"任务名称: {task_name}")
    
    # 读取其他配置项
    # example_config = G.settings.example_setting
    # logger.info(f"示例配置: {example_config}")
    
    # 2. 执行业务逻辑
    logger.step("执行核心业务逻辑")
    # 在这里添加你的业务代码
    # 例如：
    # - 数据处理
    # - API调用
    # - 文件操作
    # - 数据库操作
    
    logger.info("业务逻辑执行中...")


if __name__ == "__main__":
    main()
