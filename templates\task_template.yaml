# 任务配置模板
# 复制此文件到 config/tasks/ 目录并重命名为 任务名.yaml

# ========================================
# 任务基本信息 (必需)
# ========================================
task_info:
  name: "任务显示名称"
  description: "任务的详细描述"
  # dependencies: []  # 可选：手动指定依赖，留空则完全自动检测

  # 依赖管理说明：
  # 1. 不配置 dependencies - 系统自动检测所有依赖
  # 2. 配置 dependencies: [] - 混合模式，自动检测 + 手动指定
  # 3. 需要特定版本时才手动配置，如：
  #    dependencies:
  #      - "pandas==1.5.0"    # 指定版本
  #      - "requests>=2.28.0"  # 最低版本
  #      - numpy               # 最新版本

# ========================================
# 定时任务配置 (可选)
# ========================================
schedule:
  enabled: false           # 是否启用定时执行
  cron: "0 9 * * *"       # cron表达式，示例：每天上午9点
  # 常用cron表达式示例：
  # "*/5 * * * *"         # 每5分钟
  # "0 * * * *"           # 每小时
  # "0 9 * * *"           # 每天上午9点
  # "0 9 * * 1-5"         # 工作日上午9点
  # "0 9 1 * *"           # 每月1号上午9点

# ========================================
# 企业微信通知配置 (可选)
# ========================================
notification:
  enabled: true            # 是否启用通知
  notify_on_success: true  # 成功时是否通知
  notify_on_failure: true  # 失败时是否通知
  # webhook_url: "自定义webhook地址"  # 可选，不设置则使用全局配置

# ========================================
# 任务设置 (根据需要自定义)
# ========================================
settings:
  task_name: "我的任务"
  
  # 数据处理设置
  batch_size: 100          # 批处理大小
  max_items: 1000          # 最大处理项目数
  timeout: 30              # 超时时间(秒)
  
  # 文件路径设置
  input_dir: "./input"     # 输入目录
  output_dir: "./output"   # 输出目录
  temp_dir: "./temp"       # 临时目录
  
  # 日志设置
  keep_logs: true          # 是否保留日志
  log_level: "INFO"        # 日志级别
  
  # 其他自定义设置
  # custom_setting: "value"

# ========================================
# 数据库配置 (可选，会覆盖全局配置)
# ========================================
# database:
#   host: "localhost"
#   port: 5432
#   username: "user"
#   password: "password"
#   database: "mydb"

# ========================================
# API配置 (可选，会覆盖全局配置)
# ========================================
# api:
#   base_url: "https://api.example.com"
#   timeout: 60
#   retry_count: 3
#   headers:
#     Authorization: "Bearer your-token"
#     Content-Type: "application/json"

# ========================================
# 自定义配置段 (完全自定义)
# ========================================
# custom:
#   feature_flags:
#     enable_cache: true
#     enable_debug: false
#   
#   external_services:
#     service_a:
#       url: "https://service-a.com"
#       key: "your-api-key"
#     service_b:
#       url: "https://service-b.com"
#       token: "your-token"
#   
#   business_rules:
#     max_retry_attempts: 3
#     error_threshold: 0.1
#     alert_recipients:
#       - "<EMAIL>"
#       - "<EMAIL>"

# ========================================
# 配置使用说明
# ========================================
# 在任务代码中访问配置：
# 
# # 访问settings段
# task_name = G.settings.task_name
# batch_size = G.settings.batch_size
# 
# # 访问自定义段
# enable_cache = G.custom.feature_flags.enable_cache
# service_url = G.custom.external_services.service_a.url
# 
# # 访问全局配置
# db_host = G.database.host
# api_timeout = G.api.timeout
# 
# # 使用默认值
# log_level = G.settings.get('log_level', 'INFO')
# custom_value = G.custom.get('missing_key', 'default_value')
