#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试优化后的日志系统
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent))

from core import logger

def test_logger():
    """测试日志系统的参数处理"""
    
    print("=== 测试优化后的日志系统 ===")
    
    # 测试预定义的参数
    logger.info("测试预定义参数", count=100, file="test.txt", status="success")
    
    # 测试新增的参数
    logger.debug("测试新增参数", product_code="G123456", url="https://example.com")
    
    # 测试任意自定义参数
    logger.info("测试自定义参数", custom_field="自定义值", another_param=123, test_data="测试数据")
    
    # 测试混合参数
    logger.success("测试混合参数", 
                  count=50, 
                  product_code="G789012", 
                  custom_status="完成",
                  random_info="随机信息",
                  number_value=99.99)
    
    # 测试空参数
    logger.warning("测试无参数")
    
    # 测试step日志
    logger.step("测试步骤", "执行自定义步骤", 
               step_number=1, 
               total_steps=5, 
               custom_data="步骤数据")

if __name__ == "__main__":
    test_logger()
